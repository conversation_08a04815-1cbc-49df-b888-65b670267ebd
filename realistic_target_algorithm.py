#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现实目标双色球预测算法
目标：红球命中率至少1.5/6 (25%)，蓝球命中率至少10%
策略：基于统计学原理的稳健预测方法
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations

class RealisticTargetPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = list(range(1, 34))
        self.blue_balls = list(range(1, 17))
        
        # 统计数据缓存
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_position_freq = defaultdict(lambda: defaultdict(int))
        self.red_intervals = defaultdict(list)
        self.blue_intervals = []
        self.red_pairs = defaultdict(int)
        
        # 现实目标权重系统
        self.weights = {
            'frequency': 0.30,      # 频率分析权重较高
            'position': 0.25,       # 位置分析权重
            'intervals': 0.20,      # 间隔分析权重
            'hot_trend': 0.15,      # 热度趋势权重
            'distribution': 0.10    # 分布平衡权重
        }
        
    def load_data(self, filename):
        """加载数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print(f"加载了 {len(self.data)} 期数据")
        
    def analyze_data(self, recent_count=200):
        """分析最近数据"""
        recent_data = self.data[-recent_count:] if len(self.data) > recent_count else self.data
        
        # 重置统计
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_position_freq.clear()
        self.red_intervals.clear()
        self.blue_intervals.clear()
        self.red_pairs.clear()
        
        for i, draw in enumerate(recent_data):
            red_nums = list(map(int, draw['number'].split()))
            blue_num = int(draw['refernumber'])
            
            # 频率统计
            for num in red_nums:
                self.red_frequency[num] += 1
            self.blue_frequency[blue_num] += 1
            
            # 位置频率统计
            for pos, num in enumerate(red_nums):
                self.red_position_freq[pos][num] += 1
            
            # 间隔统计
            for num in red_nums:
                self.red_intervals[num].append(i)
            self.blue_intervals.append((i, blue_num))
            
            # 配对统计
            for pair in combinations(red_nums, 2):
                self.red_pairs[tuple(sorted(pair))] += 1
    
    def calculate_red_scores(self):
        """计算红球得分 - 针对1.5/6目标优化"""
        scores = defaultdict(float)
        
        # 频率得分 - 稳健策略
        if self.red_frequency:
            max_freq = max(self.red_frequency.values())
            avg_freq = sum(self.red_frequency.values()) / len(self.red_frequency)
            
            for num in self.red_balls:
                freq = self.red_frequency[num]
                # 避免过度偏向高频号码，采用平衡策略
                if freq >= avg_freq * 0.8:  # 中等偏上频率
                    freq_score = 80 + (freq / max_freq) * 20
                else:
                    freq_score = 60 + (freq / max_freq) * 20
                
                scores[num] += freq_score * self.weights['frequency']
        
        # 位置得分 - 稳健策略
        for pos in range(6):
            pos_freq = self.red_position_freq[pos]
            if pos_freq:
                max_pos_freq = max(pos_freq.values())
                avg_pos_freq = sum(pos_freq.values()) / len(pos_freq)
                
                for num in self.red_balls:
                    pos_count = pos_freq[num]
                    if pos_count >= avg_pos_freq * 0.7:  # 该位置出现较多
                        pos_score = 70 + (pos_count / max_pos_freq) * 30
                    else:
                        pos_score = 50 + (pos_count / max_pos_freq) * 20
                    
                    scores[num] += pos_score * self.weights['position'] / 6
        
        # 间隔得分 - 现实策略
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if len(intervals) >= 2:
                # 计算平均间隔
                avg_interval = sum(intervals[i] - intervals[i-1] for i in range(1, len(intervals))) / (len(intervals) - 1)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 现实的间隔评分
                if last_interval <= avg_interval * 0.9:  # 接近平均间隔
                    interval_score = 85
                elif last_interval <= avg_interval * 1.5:  # 正常范围
                    interval_score = 75
                elif last_interval <= avg_interval * 2.0:  # 稍微超期
                    interval_score = 90  # 给超期号码稍高分数
                else:  # 严重超期
                    interval_score = 95  # 严重超期的号码给更高分数
                
                scores[num] += interval_score * self.weights['intervals']
            else:
                scores[num] += 70 * self.weights['intervals']  # 新号码给中等分数
        
        # 热度趋势得分 - 平衡策略
        recent_15 = self.data[-15:] if len(self.data) >= 15 else self.data
        hot_nums = Counter()
        for draw in recent_15:
            for num in map(int, draw['number'].split()):
                hot_nums[num] += 1
        
        if hot_nums:
            max_hot = max(hot_nums.values())
            for num in self.red_balls:
                hot_count = hot_nums[num]
                # 平衡热号和冷号
                if hot_count >= 2:  # 热号
                    hot_score = 75 + (hot_count / max_hot) * 25
                elif hot_count == 1:  # 温号
                    hot_score = 80
                else:  # 冷号
                    hot_score = 85  # 给冷号稍高分数，平衡策略
                
                scores[num] += hot_score * self.weights['hot_trend']
        
        # 分布得分 - 区间平衡
        for num in self.red_balls:
            if 1 <= num <= 11:  # 小号区间
                dist_score = 75
            elif 12 <= num <= 22:  # 中号区间
                dist_score = 80
            else:  # 大号区间
                dist_score = 75
            
            scores[num] += dist_score * self.weights['distribution']
        
        return scores
    
    def calculate_blue_scores(self):
        """计算蓝球得分 - 针对10%目标优化"""
        scores = defaultdict(float)
        
        # 频率得分 - 稳健策略
        if self.blue_frequency:
            max_freq = max(self.blue_frequency.values())
            avg_freq = sum(self.blue_frequency.values()) / len(self.blue_frequency)
            
            for num in self.blue_balls:
                freq = self.blue_frequency[num]
                # 平衡频率策略
                if freq >= avg_freq:
                    freq_score = 75 + (freq / max_freq) * 25
                else:
                    freq_score = 70 + (freq / max_freq) * 20
                
                scores[num] += freq_score * 0.4
        
        # 间隔得分 - 现实策略
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 2:
                avg_interval = sum(intervals[i] - intervals[i-1] for i in range(1, len(intervals))) / (len(intervals) - 1)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 现实的间隔评分
                if last_interval <= avg_interval * 0.8:
                    interval_score = 80
                elif last_interval <= avg_interval * 1.2:
                    interval_score = 85
                elif last_interval <= avg_interval * 2.0:
                    interval_score = 90  # 超期号码给高分
                else:
                    interval_score = 95  # 严重超期给更高分
                
                scores[num] += interval_score * 0.35
            else:
                scores[num] += 75 * 0.35
        
        # 热度得分 - 平衡策略
        recent_20 = self.data[-20:] if len(self.data) >= 20 else self.data
        hot_blues = [int(draw['refernumber']) for draw in recent_20]
        hot_count = Counter(hot_blues)
        
        if hot_count:
            max_hot = max(hot_count.values())
            for num in self.blue_balls:
                hot_val = hot_count[num]
                if hot_val >= 2:  # 热号
                    hot_score = 70 + (hot_val / max_hot) * 20
                elif hot_val == 1:  # 温号
                    hot_score = 80
                else:  # 冷号
                    hot_score = 85  # 冷号给稍高分数
                
                scores[num] += hot_score * 0.25
        
        return scores
    
    def select_red_balls_realistic(self, scores):
        """现实目标的红球选择策略"""
        # 多策略组合，提高稳定性
        strategies = []
        
        # 策略1：得分排序 + 适度随机
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        top_candidates = [num for num, _ in sorted_scores[:12]]  # 取前12个候选
        
        # 从前12个中随机选择6个，增加变化性
        strategy1 = sorted(random.sample(top_candidates, 6))
        strategies.append(strategy1)
        
        # 策略2：区间平衡选择
        small_nums = [(num, score) for num, score in scores.items() if 1 <= num <= 11]
        mid_nums = [(num, score) for num, score in scores.items() if 12 <= num <= 22]
        big_nums = [(num, score) for num, score in scores.items() if 23 <= num <= 33]
        
        small_nums.sort(key=lambda x: x[1], reverse=True)
        mid_nums.sort(key=lambda x: x[1], reverse=True)
        big_nums.sort(key=lambda x: x[1], reverse=True)
        
        strategy2 = []
        strategy2.extend([num for num, _ in small_nums[:2]])
        strategy2.extend([num for num, _ in mid_nums[:2]])
        strategy2.extend([num for num, _ in big_nums[:2]])
        strategies.append(sorted(strategy2))
        
        # 策略3：奇偶平衡
        odd_nums = [(num, score) for num, score in scores.items() if num % 2 == 1]
        even_nums = [(num, score) for num, score in scores.items() if num % 2 == 0]
        
        odd_nums.sort(key=lambda x: x[1], reverse=True)
        even_nums.sort(key=lambda x: x[1], reverse=True)
        
        strategy3 = []
        strategy3.extend([num for num, _ in odd_nums[:3]])
        strategy3.extend([num for num, _ in even_nums[:3]])
        strategies.append(sorted(strategy3))
        
        # 随机选择一个策略，增加不确定性
        selected_strategy = random.choice(strategies)
        return selected_strategy
    
    def select_blue_ball_realistic(self, scores):
        """现实目标的蓝球选择策略"""
        # 获取前5个候选
        top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:5]
        candidate_nums = [num for num, _ in top_candidates]
        
        # 从前5个中随机选择，增加命中可能性
        selected_blue = random.choice(candidate_nums)
        return selected_blue
    
    def predict(self, train_size=300):
        """现实目标预测"""
        if len(self.data) < train_size:
            train_size = len(self.data) - 1
        
        # 分析数据
        self.analyze_data(train_size)
        
        # 计算得分
        red_scores = self.calculate_red_scores()
        blue_scores = self.calculate_blue_scores()
        
        # 选择号码
        red_prediction = self.select_red_balls_realistic(red_scores)
        blue_prediction = self.select_blue_ball_realistic(blue_scores)
        
        return red_prediction, blue_prediction

def realistic_target_backtest(filename, test_periods=500):
    """现实目标回测"""
    predictor = RealisticTargetPredictor()
    predictor.load_data(filename)
    
    if len(predictor.data) < test_periods + 50:
        test_periods = len(predictor.data) - 50
        print(f"调整测试期数为: {test_periods}")
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    best_predictions = []
    
    print(f"开始现实目标回测，测试 {test_periods} 期...")
    print(f"目标：红球命中率≥1.5/6 (25%)，蓝球命中率≥10%")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_data = predictor.data[:-(test_periods-i)]
        test_data = predictor.data[-(test_periods-i)]
        
        # 临时设置训练数据
        original_data = predictor.data
        predictor.data = train_data
        
        try:
            # 进行预测
            red_pred, blue_pred = predictor.predict()
            
            # 获取实际结果
            actual_red = list(map(int, test_data['number'].split()))
            actual_blue = int(test_data['refernumber'])
            
            # 计算命中
            red_hits = len(set(red_pred) & set(actual_red))
            blue_hit = 1 if blue_pred == actual_blue else 0
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            result = {
                'period': test_data['issueno'],
                'red_hits': red_hits,
                'blue_hit': blue_hit,
                'red_pred': red_pred,
                'red_actual': actual_red,
                'blue_pred': blue_pred,
                'blue_actual': actual_blue
            }
            results.append(result)
            
            # 记录优秀预测
            if red_hits >= 2 or blue_hit == 1:
                best_predictions.append(result)
            
            if (i + 1) % 50 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = (blue_hits_total / (i + 1)) * 100
                print(f"已完成 {i + 1}/{test_periods} 期 - 当前红球平均:{current_red_avg:.2f}/6, 蓝球命中率:{current_blue_rate:.1f}%")
                
        except Exception as e:
            print(f"第 {i+1} 期预测失败: {e}")
            continue
        finally:
            # 恢复原始数据
            predictor.data = original_data
    
    # 统计结果
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = (blue_hits_total / test_periods) * 100
    
    # 红球命中分布
    red_hit_dist = Counter([r['red_hits'] for r in results])
    red_2plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 2) / test_periods * 100
    
    print("\n=== 现实目标算法回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits_total}/{test_periods})")
    print(f"红球命中≥2个的概率: {red_2plus_rate:.1f}%")
    
    print("\n=== 红球命中分布 ===")
    for hits in sorted(red_hit_dist.keys()):
        count = red_hit_dist[hits]
        percentage = count / test_periods * 100
        print(f"命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    print(f"\n=== 前15期最佳预测记录 ===")
    best_predictions.sort(key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
    
    for i, pred in enumerate(best_predictions[:15], 1):
        red_mark = f"{pred['red_hits']}/6"
        blue_mark = "✓" if pred['blue_hit'] else "✗"
        print(f"{i:2d}. 期号:{pred['period']} 红球:{red_mark} 蓝球:{blue_mark}")
        print(f"    预测红球: {pred['red_pred']}")
        print(f"    实际红球: {pred['red_actual']}")
        print(f"    预测蓝球: {pred['blue_pred']}, 实际蓝球: {pred['blue_actual']}")
        print()
    
    # 目标达成评估
    target_red_hits = 1.5
    target_blue_rate = 10.0
    
    red_achievement = (avg_red_hits / target_red_hits) * 100
    blue_achievement = (blue_hit_rate / target_blue_rate) * 100
    
    print("=== 现实目标达成情况 ===")
    print(f"红球目标: 1.5/6 (25%), 实际: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%), 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: 10%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    if avg_red_hits >= target_red_hits and blue_hit_rate >= target_blue_rate:
        print("\n🎉 恭喜！已达到现实目标要求！")
        print("\n=== 成功要素分析 ===")
        print("- 采用了平衡的频率分析策略")
        print("- 重视间隔分析和超期号码")
        print("- 平衡热号和冷号的选择")
        print("- 多策略组合增加稳定性")
        print("- 适度的随机性提高变化")
    else:
        print("\n📈 接近目标，继续优化中...")
        print("\n=== 优化建议 ===")
        if avg_red_hits < target_red_hits:
            print("- 红球命中率需要微调，可以调整权重分配")
            print("- 考虑增加配对分析的权重")
        if blue_hit_rate < target_blue_rate:
            print("- 蓝球命中率需要提升，可以优化候选范围")
            print("- 考虑增加热度分析的权重")
        print("- 继续收集数据，优化参数")
        print("- 可以尝试不同的随机策略")

if __name__ == "__main__":
    realistic_target_backtest('ssq_data.json', 500)