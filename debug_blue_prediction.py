#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import numpy as np
from collections import Counter
from main import AdvancedBlueBallPredictor

# 创建预测器实例
predictor = AdvancedBlueBallPredictor()

print("=== 调试蓝球预测算法 ===")
print(f"数据量: {len(predictor.data)}")
print(f"蓝球范围: {predictor.blue_range}")

# 检查最近几期的蓝球数据
print("\n最近10期蓝球数据:")
for i, entry in enumerate(predictor.data[:10]):
    print(f"第{i+1}期: {entry.get('blue', 'N/A')}")

# 测试各个预测算法
print("\n=== 各算法预测得分 ===")

# 1. 频率分析
freq_scores = predictor.frequency_analysis_advanced()
print("\n1. 频率分析得分:")
for ball in sorted(freq_scores.keys()):
    print(f"蓝球{ball}: {freq_scores[ball]:.4f}")
freq_best = max(freq_scores.items(), key=lambda x: x[1])
print(f"频率分析最佳: 蓝球{freq_best[0]} (得分:{freq_best[1]:.4f})")

# 2. 间隔模式分析
interval_scores = predictor.interval_pattern_analysis()
print("\n2. 间隔模式分析得分:")
for ball in sorted(interval_scores.keys()):
    print(f"蓝球{ball}: {interval_scores[ball]:.4f}")
interval_best = max(interval_scores.items(), key=lambda x: x[1])
print(f"间隔分析最佳: 蓝球{interval_best[0]} (得分:{interval_best[1]:.4f})")

# 3. 序列学习
sequence_scores = predictor.sequence_learning()
print("\n3. 序列学习得分:")
for ball in sorted(sequence_scores.keys()):
    print(f"蓝球{ball}: {sequence_scores[ball]:.4f}")
sequence_best = max(sequence_scores.items(), key=lambda x: x[1])
print(f"序列学习最佳: 蓝球{sequence_best[0]} (得分:{sequence_best[1]:.4f})")

# 4. 分布建模
distribution_scores = predictor.distribution_modeling()
print("\n4. 分布建模得分:")
for ball in sorted(distribution_scores.keys()):
    print(f"蓝球{ball}: {distribution_scores[ball]:.4f}")
dist_best = max(distribution_scores.items(), key=lambda x: x[1])
print(f"分布建模最佳: 蓝球{dist_best[0]} (得分:{dist_best[1]:.4f})")

# 5. 趋势分析
trend_scores = predictor.trend_analysis_advanced()
print("\n5. 趋势分析得分:")
for ball in sorted(trend_scores.keys()):
    print(f"蓝球{ball}: {trend_scores[ball]:.4f}")
trend_best = max(trend_scores.items(), key=lambda x: x[1])
print(f"趋势分析最佳: 蓝球{trend_best[0]} (得分:{trend_best[1]:.4f})")

# 6. 神经网络预测
neural_scores = predictor.neural_prediction()
print("\n6. 神经网络预测得分:")
for ball in sorted(neural_scores.keys()):
    print(f"蓝球{ball}: {neural_scores[ball]:.4f}")
neural_best = max(neural_scores.items(), key=lambda x: x[1])
print(f"神经网络最佳: 蓝球{neural_best[0]} (得分:{neural_best[1]:.4f})")

# 7. 反向验证
reverse_scores = predictor.reverse_validation()
print("\n7. 反向验证得分:")
for ball in sorted(reverse_scores.keys()):
    print(f"蓝球{ball}: {reverse_scores[ball]:.4f}")
reverse_best = max(reverse_scores.items(), key=lambda x: x[1])
print(f"反向验证最佳: 蓝球{reverse_best[0]} (得分:{reverse_best[1]:.4f})")

# 最终预测
print("\n=== 最终预测 ===")
final_prediction = predictor.predict_blue_ball()
print(f"最终预测蓝球: {final_prediction}")

# 检查权重
print("\n=== 算法权重 ===")
for key, weight in predictor.feature_weights.items():
    print(f"{key}: {weight:.4f}")

# 多次预测测试
print("\n=== 多次预测测试 ===")
predictions = []
for i in range(10):
    pred = predictor.predict_blue_ball()
    predictions.append(pred)
    print(f"第{i+1}次预测: {pred}")

pred_counter = Counter(predictions)
print(f"\n预测分布: {dict(pred_counter)}")
print(f"是否所有预测都是11: {all(p == 11 for p in predictions)}")