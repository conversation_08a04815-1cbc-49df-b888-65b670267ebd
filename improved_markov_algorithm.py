import json
import random
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import math

class ImprovedMarkovPredictor:
    """改进的马尔科夫链预测器
    
    主要改进：
    1. 统计每期号码与上期号码的重复概率
    2. 最近一期的号码权重放到最大，利用马尔科夫链算法
    3. 在权重号码里做出不同的搭配可能，并给出得分
    """
    
    def __init__(self, data_file='ssq_data.json'):
        self.data = self.load_data(data_file)
        self.red_range = list(range(1, 34))
        self.blue_range = list(range(1, 17))
        
        # 重复概率统计
        self.repeat_stats = self.calculate_repeat_probability()
        
        # 马尔科夫转移矩阵
        self.red_transition_matrix = self.build_red_transition_matrix()
        self.blue_transition_matrix = self.build_blue_transition_matrix()
        
        # 权重配置
        self.recent_weight = 0.6  # 最近一期权重
        self.markov_weight = 0.3  # 马尔科夫链权重
        self.frequency_weight = 0.1  # 频率权重
        
        print(f"改进马尔科夫预测器初始化完成，历史数据：{len(self.data)}期")
        print(f"重复概率统计：红球平均重复率 {self.repeat_stats['red_repeat_rate']:.2%}")
        print(f"重复概率统计：蓝球平均重复率 {self.repeat_stats['blue_repeat_rate']:.2%}")
    
    def load_data(self, data_file):
        """加载数据"""
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换数据格式，按时间顺序排列（最新的在前）
            formatted_data = []
            for entry in data:
                red_str = entry['number'].split(' ')
                red = sorted([int(x) for x in red_str])
                blue = int(entry['refernumber'])
                period = entry['issueno']
                formatted_data.append({
                    'period': period,
                    'red': red, 
                    'blue': blue
                })
            
            return formatted_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def calculate_repeat_probability(self):
        """计算每期号码与上期号码的重复概率"""
        if len(self.data) < 2:
            return {'red_repeat_rate': 0, 'blue_repeat_rate': 0, 'repeat_patterns': {}}
        
        red_repeats = []
        blue_repeats = []
        repeat_patterns = defaultdict(int)
        
        # 从第二期开始统计（因为要与上期比较）
        for i in range(1, len(self.data)):
            current_red = set(self.data[i]['red'])
            previous_red = set(self.data[i-1]['red'])
            current_blue = self.data[i]['blue']
            previous_blue = self.data[i-1]['blue']
            
            # 红球重复数量
            red_repeat_count = len(current_red & previous_red)
            red_repeats.append(red_repeat_count)
            
            # 蓝球重复
            blue_repeat = 1 if current_blue == previous_blue else 0
            blue_repeats.append(blue_repeat)
            
            # 记录重复模式
            pattern_key = f"red_{red_repeat_count}_blue_{blue_repeat}"
            repeat_patterns[pattern_key] += 1
        
        # 计算平均重复率
        red_repeat_rate = np.mean(red_repeats) / 6  # 红球总共6个
        blue_repeat_rate = np.mean(blue_repeats)
        
        return {
            'red_repeat_rate': red_repeat_rate,
            'blue_repeat_rate': blue_repeat_rate,
            'red_repeats': red_repeats,
            'blue_repeats': blue_repeats,
            'repeat_patterns': dict(repeat_patterns)
        }
    
    def build_red_transition_matrix(self):
        """构建红球马尔科夫转移矩阵"""
        # 为每个红球号码构建转移概率
        transition_matrix = {}
        
        for ball in self.red_range:
            transition_matrix[ball] = defaultdict(float)
        
        # 统计转移频次，给最近的数据更高权重
        for i in range(1, len(self.data)):
            previous_red = self.data[i-1]['red']
            current_red = self.data[i]['red']
            
            # 计算时间权重（越近权重越大）
            time_weight = math.exp(-0.01 * i)  # 指数衰减权重
            
            # 对于上期出现的每个红球，统计这期可能出现的红球
            for prev_ball in previous_red:
                for curr_ball in current_red:
                    transition_matrix[prev_ball][curr_ball] += time_weight
        
        # 归一化转移概率
        for ball in self.red_range:
            total = sum(transition_matrix[ball].values())
            if total > 0:
                for target_ball in transition_matrix[ball]:
                    transition_matrix[ball][target_ball] /= total
            else:
                # 如果没有历史数据，使用均匀分布
                for target_ball in self.red_range:
                    transition_matrix[ball][target_ball] = 1.0 / len(self.red_range)
        
        return transition_matrix
    
    def build_blue_transition_matrix(self):
        """构建蓝球马尔科夫转移矩阵"""
        transition_matrix = {}
        
        for ball in self.blue_range:
            transition_matrix[ball] = defaultdict(float)
        
        # 统计转移频次，给最近的数据更高权重
        for i in range(1, len(self.data)):
            previous_blue = self.data[i-1]['blue']
            current_blue = self.data[i]['blue']
            
            # 计算时间权重（越近权重越大）
            time_weight = math.exp(-0.01 * i)
            
            transition_matrix[previous_blue][current_blue] += time_weight
        
        # 归一化转移概率
        for ball in self.blue_range:
            total = sum(transition_matrix[ball].values())
            if total > 0:
                for target_ball in transition_matrix[ball]:
                    transition_matrix[ball][target_ball] /= total
            else:
                # 如果没有历史数据，使用均匀分布
                for target_ball in self.blue_range:
                    transition_matrix[ball][target_ball] = 1.0 / len(self.blue_range)
        
        return transition_matrix
    
    def calculate_ball_weights(self):
        """计算每个号码的综合权重"""
        if not self.data:
            return {}, {}
        
        # 最近一期的号码（权重最大）
        latest_red = set(self.data[0]['red'])
        latest_blue = int(self.data[0]['refernumber'])
        
        # 频率统计
        red_frequency = Counter()
        blue_frequency = Counter()
        
        for record in self.data:
            for ball in record['red']:
                red_frequency[ball] += 1
            blue_frequency[record['blue']] += 1
        
        # 计算红球权重
        red_weights = {}
        for ball in self.red_range:
            # 基础频率权重
            freq_weight = red_frequency.get(ball, 0) / len(self.data)
            
            # 最近一期权重
            recent_weight = self.recent_weight if ball in latest_red else 0
            
            # 马尔科夫链权重（基于最近一期的转移概率）
            markov_weight = 0
            for prev_ball in latest_red:
                markov_weight += self.red_transition_matrix[prev_ball].get(ball, 0)
            markov_weight /= len(latest_red) if latest_red else 1
            
            # 重复概率调整
            repeat_adjustment = 1.0
            if ball in latest_red:
                # 如果上期出现过，根据重复概率调整
                repeat_adjustment = self.repeat_stats['red_repeat_rate'] * 2
            
            # 综合权重
            total_weight = (
                freq_weight * self.frequency_weight +
                recent_weight +
                markov_weight * self.markov_weight
            ) * repeat_adjustment
            
            red_weights[ball] = total_weight
        
        # 计算蓝球权重
        blue_weights = {}
        for ball in self.blue_range:
            # 基础频率权重
            freq_weight = blue_frequency.get(ball, 0) / len(self.data)
            
            # 最近一期权重
            recent_weight = self.recent_weight if ball == latest_blue else 0
            
            # 马尔科夫链权重
            markov_weight = self.blue_transition_matrix[latest_blue].get(ball, 0)
            
            # 重复概率调整
            repeat_adjustment = 1.0
            if ball == latest_blue:
                repeat_adjustment = self.repeat_stats['blue_repeat_rate'] * 2
            
            # 综合权重
            total_weight = (
                freq_weight * self.frequency_weight +
                recent_weight +
                markov_weight * self.markov_weight
            ) * repeat_adjustment
            
            blue_weights[ball] = total_weight
        
        return red_weights, blue_weights
    
    def generate_multiple_combinations(self, red_weights, blue_weights, num_combinations=5):
        """生成多个不同的号码搭配组合并评分"""
        combinations = []
        
        # 归一化权重
        red_balls = list(red_weights.keys())
        red_probs = [red_weights[ball] for ball in red_balls]
        red_probs = np.array(red_probs)
        red_probs = red_probs / np.sum(red_probs) if np.sum(red_probs) > 0 else np.ones_like(red_probs) / len(red_probs)
        
        blue_balls = list(blue_weights.keys())
        blue_probs = [blue_weights[ball] for ball in blue_balls]
        blue_probs = np.array(blue_probs)
        blue_probs = blue_probs / np.sum(blue_probs) if np.sum(blue_probs) > 0 else np.ones_like(blue_probs) / len(blue_probs)
        
        for i in range(num_combinations):
            # 使用不同的随机种子确保多样性
            seed_value = (int(datetime.now().timestamp() * 1000) + i) % (2**32 - 1)
            np.random.seed(seed_value)
            
            # 生成红球组合
            selected_red = []
            temp_red_probs = red_probs.copy()
            
            for _ in range(7):
                # 根据权重选择红球
                choice_idx = np.random.choice(len(red_balls), p=temp_red_probs)
                selected_ball = red_balls[choice_idx]
                
                # 避免重复选择
                if selected_ball not in selected_red:
                    selected_red.append(selected_ball)
                    # 将已选择的球的概率设为0，重新归一化
                    temp_red_probs[choice_idx] = 0
                    if np.sum(temp_red_probs) > 0:
                        temp_red_probs = temp_red_probs / np.sum(temp_red_probs)
                    else:
                        # 如果所有概率都为0，使用均匀分布
                        remaining_balls = [b for b in red_balls if b not in selected_red]
                        if remaining_balls:
                            temp_red_probs = np.zeros(len(red_balls))
                            for j, ball in enumerate(red_balls):
                                if ball in remaining_balls:
                                    temp_red_probs[j] = 1.0 / len(remaining_balls)
                else:
                    # 如果选中了重复的球，随机选择一个未选中的
                    remaining_balls = [b for b in red_balls if b not in selected_red]
                    if remaining_balls:
                        selected_red.append(random.choice(remaining_balls))
            
            # 确保选择了7个红球
            while len(selected_red) < 7:
                remaining_balls = [b for b in red_balls if b not in selected_red]
                if remaining_balls:
                    selected_red.append(random.choice(remaining_balls))
                else:
                    break
            
            selected_red = sorted(selected_red[:7])
            
            # 生成蓝球
            selected_blue = np.random.choice(blue_balls, p=blue_probs)
            
            # 计算组合得分
            score = self.calculate_combination_score(selected_red, selected_blue, red_weights, blue_weights)
            
            combinations.append({
                'red': selected_red,
                'blue': selected_blue,
                'score': score,
                'rank': i + 1
            })
        
        # 按得分排序
        combinations.sort(key=lambda x: x['score'], reverse=True)
        
        # 重新分配排名
        for i, combo in enumerate(combinations):
            combo['rank'] = i + 1
        
        return combinations
    
    def calculate_combination_score(self, red_balls, blue_ball, red_weights, blue_weights):
        """计算号码组合的得分"""
        # 红球得分（权重之和）
        red_score = sum(red_weights.get(ball, 0) for ball in red_balls)
        
        # 蓝球得分
        blue_score = blue_weights.get(blue_ball, 0)
        
        # 组合多样性得分（号码分布）
        diversity_score = self.calculate_diversity_score(red_balls)
        
        # 重复概率得分
        repeat_score = self.calculate_repeat_score(red_balls, blue_ball)
        
        # 综合得分
        total_score = (
            red_score * 0.4 +
            blue_score * 0.2 +
            diversity_score * 0.2 +
            repeat_score * 0.2
        )
        
        return total_score
    
    def calculate_diversity_score(self, red_balls):
        """计算号码分布多样性得分"""
        # 检查号码在低、中、高区域的分布
        low_count = sum(1 for ball in red_balls if ball <= 11)
        mid_count = sum(1 for ball in red_balls if 12 <= ball <= 22)
        high_count = sum(1 for ball in red_balls if ball >= 23)
        
        # 理想分布是2-3-2或3-2-2
        ideal_distributions = [(2, 3, 2), (3, 2, 2), (2, 2, 3)]
        current_distribution = (low_count, mid_count, high_count)
        
        # 计算与理想分布的距离
        min_distance = min(
            sum(abs(current_distribution[i] - ideal[i]) for i in range(3))
            for ideal in ideal_distributions
        )
        
        # 距离越小，得分越高
        diversity_score = max(0, 1.0 - min_distance / 7)
        
        return diversity_score
    
    def calculate_repeat_score(self, red_balls, blue_ball):
        """计算重复概率得分"""
        if not self.data:
            return 0.5
        
        latest_red = set(self.data[0]['red'])
        latest_blue = int(self.data[0]['refernumber'])
        
        # 红球重复数量
        red_repeat_count = len(set(red_balls) & latest_red)
        
        # 蓝球重复
        blue_repeat = 1 if blue_ball == latest_blue else 0
        
        # 根据历史重复概率计算得分
        expected_red_repeats = self.repeat_stats['red_repeat_rate'] * 6
        expected_blue_repeat = self.repeat_stats['blue_repeat_rate']
        
        # 计算与期望重复数的接近程度
        red_repeat_score = 1.0 - abs(red_repeat_count - expected_red_repeats) / 6
        blue_repeat_score = 1.0 - abs(blue_repeat - expected_blue_repeat)
        
        return (red_repeat_score + blue_repeat_score) / 2
    
    def predict(self, num_combinations=5):
        """进行预测，返回多个组合及其得分"""
        if not self.data:
            print("⚠️ 没有历史数据，无法进行预测")
            return None
        
        # 计算权重
        red_weights, blue_weights = self.calculate_ball_weights()
        
        # 生成多个组合
        combinations = self.generate_multiple_combinations(red_weights, blue_weights, num_combinations)
        
        # 准备返回结果
        result = {
            'best_combination': combinations[0] if combinations else None,
            'all_combinations': combinations,
            'analysis': {
                'total_periods': len(self.data),
                'latest_period': self.data[0]['period'] if self.data else None,
                'repeat_stats': self.repeat_stats,
                'red_weights': red_weights,
                'blue_weights': blue_weights
            }
        }
        
        return result
    
    def format_prediction_output(self, prediction_result):
        """格式化输出预测结果"""
        if not prediction_result:
            print("❌ 预测失败")
            return
        
        print("\n" + "="*80)
        print("🎯 改进马尔科夫链双色球预测系统 🎯")
        print("="*80)
        
        analysis = prediction_result['analysis']
        print(f"\n📊 数据分析:")
        print(f"   历史数据期数: {analysis['total_periods']}")
        print(f"   最新期号: {analysis['latest_period']}")
        print(f"   红球平均重复率: {analysis['repeat_stats']['red_repeat_rate']:.2%}")
        print(f"   蓝球平均重复率: {analysis['repeat_stats']['blue_repeat_rate']:.2%}")
        
        print(f"\n🏆 预测组合排行榜:")
        print("-" * 80)
        
        for i, combo in enumerate(prediction_result['all_combinations']):
            red_str = ' '.join([f"{num:02d}" for num in combo['red']])
            blue_str = f"{combo['blue']:02d}"
            
            print(f"第{combo['rank']}名 (得分: {combo['score']:.3f})")
            print(f"   🔴 红球: {red_str}")
            print(f"   🔵 蓝球: {blue_str}")
            print(f"   📋 组合: {red_str} + {blue_str}")
            
            # 生成格式化输出
            red_format = ''.join([f"{num:02d}" for num in combo['red']])
            formatted = f"00CP#01#{red_format}*{blue_str}#1"
            print(f"   🎯 格式化: {formatted}")
            print()
        
        print("\n💡 算法特点:")
        print("   ✅ 统计期号重复概率，预测同号可能性")
        print("   ✅ 最近一期权重最大，结合马尔科夫链算法")
        print("   ✅ 多种搭配组合，智能评分排序")
        print("   ✅ 避免每次运行结果完全相同")
        
        print("\n⚠️ 风险提示: 仅供参考，请理性购彩")
        print("="*80)

# 主程序
def main():
    """主程序入口"""
    print("🚀 改进马尔科夫链预测系统启动...")
    
    try:
        # 创建预测器
        predictor = ImprovedMarkovPredictor()
        
        # 进行预测
        print("\n🤖 开始预测分析...")
        result = predictor.predict(num_combinations=5)
        
        if result:
            # 输出结果
            predictor.format_prediction_output(result)
            
            # 保存预测记录
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            prediction_record = {
                'timestamp': timestamp,
                'algorithm': 'ImprovedMarkovPredictor',
                'prediction_result': result
            }
            
            try:
                # 转换numpy类型为Python原生类型
                def convert_numpy_types(obj):
                    if isinstance(obj, np.integer):
                        return int(obj)
                    elif isinstance(obj, np.floating):
                        return float(obj)
                    elif isinstance(obj, np.ndarray):
                        return obj.tolist()
                    elif isinstance(obj, dict):
                        return {key: convert_numpy_types(value) for key, value in obj.items()}
                    elif isinstance(obj, list):
                        return [convert_numpy_types(item) for item in obj]
                    return obj
                
                serializable_record = convert_numpy_types(prediction_record)
                
                with open('improved_markov_predictions.json', 'a', encoding='utf-8') as f:
                    f.write(json.dumps(serializable_record, ensure_ascii=False) + '\n')
                print(f"\n💾 预测记录已保存 ({timestamp})")
            except Exception as e:
                print(f"\n❌ 保存预测记录失败: {e}")
        else:
            print("❌ 预测失败")
            
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()