#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能双色球预测算法 - 500期深度回测优化
目标：红球命中率 >= 3/6，蓝球命中率 >= 5/16
"""

import json
import numpy as np
from collections import defaultdict, Counter
import random
from datetime import datetime

class AdvancedSSQPredictor:
    def __init__(self, data):
        self.data = data
        self.red_balls = []
        self.blue_balls = []
        self.parse_data()
        
    def parse_data(self):
        """解析历史数据"""
        for item in self.data:
            # 从API数据格式解析
            red_str = item['number']  # 格式: "04 06 09 14 32 33"
            blue_str = item['refernumber']  # 格式: "11"
            
            # 解析红球
            red_nums = [int(x) for x in red_str.split()]
            self.red_balls.append(sorted(red_nums))
            
            # 解析蓝球
            self.blue_balls.append(int(blue_str))
    
    def advanced_frequency_analysis(self, recent_periods=100):
        """高级频率分析"""
        # 全历史频率
        all_red_freq = Counter()
        all_blue_freq = Counter()
        
        # 近期频率
        recent_red_freq = Counter()
        recent_blue_freq = Counter()
        
        # 全历史统计
        for red_combo in self.red_balls:
            for num in red_combo:
                all_red_freq[num] += 1
        
        for blue in self.blue_balls:
            all_blue_freq[blue] += 1
        
        # 近期统计
        recent_data = min(recent_periods, len(self.red_balls))
        for i in range(recent_data):
            for num in self.red_balls[-(i+1)]:
                recent_red_freq[num] += 1
            recent_blue_freq[self.blue_balls[-(i+1)]] += 1
        
        return {
            'all_red': all_red_freq,
            'all_blue': all_blue_freq,
            'recent_red': recent_red_freq,
            'recent_blue': recent_blue_freq
        }
    
    def position_analysis(self):
        """位置分析 - 分析每个位置上数字的分布"""
        position_freq = [Counter() for _ in range(6)]
        
        for red_combo in self.red_balls:
            sorted_combo = sorted(red_combo)
            for pos, num in enumerate(sorted_combo):
                position_freq[pos][num] += 1
        
        return position_freq
    
    def interval_analysis(self):
        """间隔分析 - 分析数字出现的间隔模式"""
        red_intervals = defaultdict(list)
        blue_intervals = defaultdict(list)
        
        # 红球间隔分析
        for num in range(1, 34):
            last_appear = -1
            for i, red_combo in enumerate(self.red_balls):
                if num in red_combo:
                    if last_appear != -1:
                        red_intervals[num].append(i - last_appear)
                    last_appear = i
        
        # 蓝球间隔分析
        for num in range(1, 17):
            last_appear = -1
            for i, blue in enumerate(self.blue_balls):
                if blue == num:
                    if last_appear != -1:
                        blue_intervals[num].append(i - last_appear)
                    last_appear = i
        
        return red_intervals, blue_intervals
    
    def combination_analysis(self):
        """组合分析 - 分析数字组合模式"""
        # 连号分析
        consecutive_patterns = Counter()
        
        # 奇偶分析
        odd_even_patterns = Counter()
        
        # 区间分析
        zone_patterns = Counter()
        
        for red_combo in self.red_balls:
            sorted_combo = sorted(red_combo)
            
            # 连号统计
            consecutive_count = 0
            for i in range(len(sorted_combo) - 1):
                if sorted_combo[i+1] - sorted_combo[i] == 1:
                    consecutive_count += 1
            consecutive_patterns[consecutive_count] += 1
            
            # 奇偶统计
            odd_count = sum(1 for x in sorted_combo if x % 2 == 1)
            odd_even_patterns[f"{odd_count}奇{6-odd_count}偶"] += 1
            
            # 区间统计 (1-11, 12-22, 23-33)
            zone1 = sum(1 for x in sorted_combo if 1 <= x <= 11)
            zone2 = sum(1 for x in sorted_combo if 12 <= x <= 22)
            zone3 = sum(1 for x in sorted_combo if 23 <= x <= 33)
            zone_patterns[f"{zone1}-{zone2}-{zone3}"] += 1
        
        return {
            'consecutive': consecutive_patterns,
            'odd_even': odd_even_patterns,
            'zones': zone_patterns
        }
    
    def markov_chain_analysis(self, order=2):
        """高阶马尔科夫链分析"""
        red_transitions = defaultdict(lambda: defaultdict(int))
        blue_transitions = defaultdict(lambda: defaultdict(int))
        
        # 红球马尔科夫链
        for i in range(order, len(self.red_balls)):
            current_state = tuple(sorted(self.red_balls[i]))
            prev_states = []
            for j in range(order):
                prev_states.extend(self.red_balls[i-j-1])
            prev_state = tuple(sorted(set(prev_states)))
            
            for num in current_state:
                red_transitions[prev_state][num] += 1
        
        # 蓝球马尔科夫链
        for i in range(order, len(self.blue_balls)):
            current_blue = self.blue_balls[i]
            prev_blues = tuple(self.blue_balls[i-order:i])
            blue_transitions[prev_blues][current_blue] += 1
        
        return red_transitions, blue_transitions
    
    def calculate_prediction_weights(self, num, is_red=True):
        """计算预测权重"""
        freq_analysis = self.advanced_frequency_analysis()
        red_intervals, blue_intervals = self.interval_analysis()
        
        weights = []
        
        if is_red:
            # 历史频率权重
            total_appearances = sum(freq_analysis['all_red'].values())
            freq_weight = freq_analysis['all_red'][num] / total_appearances if total_appearances > 0 else 0
            weights.append(freq_weight * 0.3)
            
            # 近期频率权重
            recent_total = sum(freq_analysis['recent_red'].values())
            recent_weight = freq_analysis['recent_red'][num] / recent_total if recent_total > 0 else 0
            weights.append(recent_weight * 0.25)
            
            # 间隔权重
            if num in red_intervals and red_intervals[num]:
                avg_interval = np.mean(red_intervals[num])
                last_appear = -1
                for i, red_combo in enumerate(reversed(self.red_balls)):
                    if num in red_combo:
                        last_appear = i
                        break
                
                if last_appear != -1:
                    interval_score = min(1.0, last_appear / avg_interval) if avg_interval > 0 else 0.5
                    weights.append(interval_score * 0.2)
                else:
                    weights.append(0.5 * 0.2)
            else:
                weights.append(0.5 * 0.2)
            
            # 位置权重
            position_freq = self.position_analysis()
            position_weight = 0
            for pos_counter in position_freq:
                if num in pos_counter:
                    position_weight += pos_counter[num] / sum(pos_counter.values())
            weights.append((position_weight / 6) * 0.25)
            
        else:  # 蓝球
            # 历史频率权重
            total_appearances = sum(freq_analysis['all_blue'].values())
            freq_weight = freq_analysis['all_blue'][num] / total_appearances if total_appearances > 0 else 0
            weights.append(freq_weight * 0.4)
            
            # 近期频率权重
            recent_total = sum(freq_analysis['recent_blue'].values())
            recent_weight = freq_analysis['recent_blue'][num] / recent_total if recent_total > 0 else 0
            weights.append(recent_weight * 0.3)
            
            # 间隔权重
            if num in blue_intervals and blue_intervals[num]:
                avg_interval = np.mean(blue_intervals[num])
                last_appear = -1
                for i, blue in enumerate(reversed(self.blue_balls)):
                    if blue == num:
                        last_appear = i
                        break
                
                if last_appear != -1:
                    interval_score = min(1.0, last_appear / avg_interval) if avg_interval > 0 else 0.5
                    weights.append(interval_score * 0.3)
                else:
                    weights.append(0.5 * 0.3)
            else:
                weights.append(0.5 * 0.3)
        
        return sum(weights)
    
    def advanced_predict(self):
        """高级预测算法"""
        # 计算所有红球的权重
        red_weights = {}
        for num in range(1, 34):
            red_weights[num] = self.calculate_prediction_weights(num, True)
        
        # 计算所有蓝球的权重
        blue_weights = {}
        for num in range(1, 17):
            blue_weights[num] = self.calculate_prediction_weights(num, False)
        
        # 组合分析结果
        combo_analysis = self.combination_analysis()
        
        # 生成多个候选组合
        candidates = []
        for _ in range(1000):  # 生成1000个候选
            # 基于权重选择红球
            red_candidates = []
            red_probs = list(red_weights.values())
            red_nums = list(red_weights.keys())
            
            # 添加随机性
            red_probs = np.array(red_probs)
            red_probs = red_probs + np.random.normal(0, 0.1, len(red_probs))
            red_probs = np.maximum(red_probs, 0.001)  # 确保非负
            red_probs = red_probs / np.sum(red_probs)  # 归一化
            
            selected_reds = np.random.choice(red_nums, size=6, replace=False, p=red_probs)
            selected_reds = sorted(selected_reds.tolist())
            
            # 基于权重选择蓝球
            blue_probs = list(blue_weights.values())
            blue_nums = list(blue_weights.keys())
            
            blue_probs = np.array(blue_probs)
            blue_probs = blue_probs + np.random.normal(0, 0.1, len(blue_probs))
            blue_probs = np.maximum(blue_probs, 0.001)
            blue_probs = blue_probs / np.sum(blue_probs)
            
            selected_blue = np.random.choice(blue_nums, p=blue_probs)
            
            candidates.append((selected_reds, selected_blue))
        
        # 评估候选组合
        best_combo = None
        best_score = -1
        
        for red_combo, blue_num in candidates:
            score = self.evaluate_combination(red_combo, blue_num, combo_analysis)
            if score > best_score:
                best_score = score
                best_combo = (red_combo, blue_num)
        
        return best_combo
    
    def evaluate_combination(self, red_combo, blue_num, combo_analysis):
        """评估组合质量"""
        score = 0
        
        # 连号评估
        consecutive_count = 0
        sorted_combo = sorted(red_combo)
        for i in range(len(sorted_combo) - 1):
            if sorted_combo[i+1] - sorted_combo[i] == 1:
                consecutive_count += 1
        
        # 根据历史连号分布给分
        if consecutive_count in combo_analysis['consecutive']:
            score += combo_analysis['consecutive'][consecutive_count] / len(self.red_balls) * 100
        
        # 奇偶评估
        odd_count = sum(1 for x in red_combo if x % 2 == 1)
        odd_even_key = f"{odd_count}奇{6-odd_count}偶"
        if odd_even_key in combo_analysis['odd_even']:
            score += combo_analysis['odd_even'][odd_even_key] / len(self.red_balls) * 100
        
        # 区间评估
        zone1 = sum(1 for x in red_combo if 1 <= x <= 11)
        zone2 = sum(1 for x in red_combo if 12 <= x <= 22)
        zone3 = sum(1 for x in red_combo if 23 <= x <= 33)
        zone_key = f"{zone1}-{zone2}-{zone3}"
        if zone_key in combo_analysis['zones']:
            score += combo_analysis['zones'][zone_key] / len(self.red_balls) * 100
        
        # 和值评估
        red_sum = sum(red_combo)
        if 80 <= red_sum <= 140:  # 理想和值范围
            score += 20
        
        # 跨度评估
        span = max(red_combo) - min(red_combo)
        if 15 <= span <= 25:  # 理想跨度范围
            score += 15
        
        return score

def load_data():
    """加载历史数据"""
    try:
        with open('ssq_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("错误：未找到 ssq_data.json 文件")
        return []

def parse_actual_numbers(red_str, blue_str):
    """解析实际开奖号码"""
    red_nums = [int(x) for x in red_str.split()]
    blue_num = int(blue_str)
    return sorted(red_nums), blue_num

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def advanced_backtest_500():
    """500期高性能回测"""
    print("=== 500期高性能双色球预测算法回测 ===")
    print("目标：红球命中率 >= 3/6，蓝球命中率 >= 5/16")
    print()
    
    # 加载数据
    data = load_data()
    if len(data) < 500:
        print(f"警告：数据不足500期，当前只有 {len(data)} 期数据")
        return
    
    print(f"加载了 {len(data)} 期历史数据")
    
    # 回测参数
    test_periods = 500
    training_periods = 300  # 使用300期数据进行训练
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    
    print(f"开始回测最近 {test_periods} 期数据...")
    print()
    
    for i in range(test_periods):
        # 训练数据：当前测试期之前的300期数据
        start_idx = max(0, len(data) - test_periods + i - training_periods)
        end_idx = len(data) - test_periods + i
        
        if end_idx <= start_idx:
            continue
            
        training_data = data[start_idx:end_idx]
        test_data = data[len(data) - test_periods + i]
        
        # 创建预测器并预测
        predictor = AdvancedSSQPredictor(training_data)
        predicted_red, predicted_blue = predictor.advanced_predict()
        
        # 获取实际结果
        actual_red, actual_blue = parse_actual_numbers(test_data['number'], test_data['refernumber'])
        
        # 计算命中率
        red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
        
        red_hits_total += red_hits
        blue_hits_total += blue_hit
        
        results.append({
            'period': test_data['issueno'],
            'predicted_red': predicted_red,
            'predicted_blue': predicted_blue,
            'actual_red': actual_red,
            'actual_blue': actual_blue,
            'red_hits': red_hits,
            'blue_hit': blue_hit
        })
        
        # 显示进度
        if (i + 1) % 50 == 0:
            current_red_avg = red_hits_total / (i + 1)
            current_blue_rate = blue_hits_total / (i + 1)
            print(f"已完成 {i + 1}/{test_periods} 期回测")
            print(f"当前平均红球命中: {current_red_avg:.2f}/6")
            print(f"当前蓝球命中率: {current_blue_rate:.1%}")
            print()
    
    # 统计结果
    print("=== 500期回测结果统计 ===")
    
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = blue_hits_total / test_periods
    
    print(f"平均红球命中数: {avg_red_hits:.2f}/6")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits_total}/{test_periods})")
    print()
    
    # 目标达成情况
    target_red = 3.0
    target_blue = 5/16  # 5/16 ≈ 0.3125
    
    print("=== 目标达成情况 ===")
    print(f"红球目标: >= {target_red}/6, 实际: {avg_red_hits:.2f}/6 {'✓' if avg_red_hits >= target_red else '✗'}")
    print(f"蓝球目标: >= {target_blue:.1%}, 实际: {blue_hit_rate:.1%} {'✓' if blue_hit_rate >= target_blue else '✗'}")
    print()
    
    # 详细分析
    red_hit_distribution = Counter([r['red_hits'] for r in results])
    print("=== 红球命中分布 ===")
    for hits in sorted(red_hit_distribution.keys()):
        count = red_hit_distribution[hits]
        percentage = count / test_periods * 100
        print(f"{hits}个红球命中: {count}次 ({percentage:.1f}%)")
    print()
    
    # 最佳预测记录
    best_results = sorted(results, key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)[:10]
    print("=== 最佳预测记录 (前10期) ===")
    for i, result in enumerate(best_results, 1):
        print(f"{i:2d}. 期号:{result['period']} 红球:{result['red_hits']}/6 蓝球:{'✓' if result['blue_hit'] else '✗'}")
        print(f"    预测红球: {result['predicted_red']}")
        print(f"    实际红球: {result['actual_red']}")
        print(f"    预测蓝球: {result['predicted_blue']}, 实际蓝球: {result['actual_blue']}")
        print()
    
    # 改进建议
    print("=== 算法改进建议 ===")
    if avg_red_hits < target_red:
        print("红球预测需要改进:")
        print("- 增加位置权重分析")
        print("- 优化马尔科夫链阶数")
        print("- 加强组合模式识别")
        print("- 引入机器学习模型")
    
    if blue_hit_rate < target_blue:
        print("蓝球预测需要改进:")
        print("- 分析蓝球与红球和值关系")
        print("- 增加蓝球周期性分析")
        print("- 优化蓝球权重计算")
        print("- 考虑蓝球遗漏值分析")
    
    return results

if __name__ == "__main__":
    results = advanced_backtest_500()