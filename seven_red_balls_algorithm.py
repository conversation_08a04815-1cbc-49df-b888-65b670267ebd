#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
7红球增概率双色球预测算法
策略：选择7个红球号码，增加命中概率
目标：通过多选一个红球，显著提升红球命中率
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations

class SevenRedBallsPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = list(range(1, 34))
        self.blue_balls = list(range(1, 17))
        
        # 统计数据缓存
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_position_freq = defaultdict(lambda: defaultdict(int))
        self.red_intervals = defaultdict(list)
        self.blue_intervals = []
        self.red_pairs = defaultdict(int)
        self.red_triplets = defaultdict(int)
        
        # 7红球策略权重系统
        self.weights = {
            'frequency': 0.25,      # 频率分析
            'position': 0.20,       # 位置分析
            'intervals': 0.20,      # 间隔分析
            'hot_trend': 0.15,      # 热度趋势
            'distribution': 0.10,   # 分布平衡
            'combination': 0.10     # 组合分析
        }
        
    def load_data(self, filename):
        """加载数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print(f"加载了 {len(self.data)} 期数据")
        
    def analyze_data(self, recent_count=250):
        """分析最近数据 - 为7红球策略优化"""
        recent_data = self.data[-recent_count:] if len(self.data) > recent_count else self.data
        
        # 重置统计
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_position_freq.clear()
        self.red_intervals.clear()
        self.blue_intervals.clear()
        self.red_pairs.clear()
        self.red_triplets.clear()
        
        for i, draw in enumerate(recent_data):
            red_nums = list(map(int, draw['number'].split()))
            blue_num = int(draw['refernumber'])
            
            # 频率统计
            for num in red_nums:
                self.red_frequency[num] += 1
            self.blue_frequency[blue_num] += 1
            
            # 位置频率统计
            for pos, num in enumerate(red_nums):
                self.red_position_freq[pos][num] += 1
            
            # 间隔统计
            for num in red_nums:
                self.red_intervals[num].append(i)
            self.blue_intervals.append((i, blue_num))
            
            # 配对和三元组统计
            for pair in combinations(red_nums, 2):
                self.red_pairs[tuple(sorted(pair))] += 1
            
            for triplet in combinations(red_nums, 3):
                self.red_triplets[tuple(sorted(triplet))] += 1
    
    def calculate_red_scores_enhanced(self):
        """计算红球得分 - 7红球增强版"""
        scores = defaultdict(float)
        
        # 频率得分 - 更精细的分析
        if self.red_frequency:
            max_freq = max(self.red_frequency.values())
            avg_freq = sum(self.red_frequency.values()) / len(self.red_frequency)
            std_freq = np.std(list(self.red_frequency.values()))
            
            for num in self.red_balls:
                freq = self.red_frequency[num]
                # 使用标准差进行更精确的评分
                if freq >= avg_freq + std_freq * 0.5:  # 高频
                    freq_score = 85 + (freq / max_freq) * 15
                elif freq >= avg_freq - std_freq * 0.5:  # 中频
                    freq_score = 75 + (freq / max_freq) * 20
                else:  # 低频
                    freq_score = 70 + (freq / max_freq) * 15
                
                scores[num] += freq_score * self.weights['frequency']
        
        # 位置得分 - 增强版
        for pos in range(6):
            pos_freq = self.red_position_freq[pos]
            if pos_freq:
                max_pos_freq = max(pos_freq.values())
                avg_pos_freq = sum(pos_freq.values()) / len(pos_freq)
                
                for num in self.red_balls:
                    pos_count = pos_freq[num]
                    if pos_count >= avg_pos_freq * 0.8:
                        pos_score = 80 + (pos_count / max_pos_freq) * 20
                    else:
                        pos_score = 60 + (pos_count / max_pos_freq) * 20
                    
                    scores[num] += pos_score * self.weights['position'] / 6
        
        # 间隔得分 - 7红球优化
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if len(intervals) >= 2:
                # 计算间隔统计
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                avg_interval = np.mean(interval_diffs)
                std_interval = np.std(interval_diffs) if len(interval_diffs) > 1 else avg_interval * 0.3
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 更精确的间隔评分
                if last_interval <= avg_interval - std_interval * 0.5:  # 提前出现
                    interval_score = 75
                elif last_interval <= avg_interval + std_interval * 0.5:  # 正常范围
                    interval_score = 85
                elif last_interval <= avg_interval + std_interval * 1.5:  # 稍微延迟
                    interval_score = 90
                else:  # 严重延迟
                    interval_score = 95
                
                scores[num] += interval_score * self.weights['intervals']
            else:
                scores[num] += 75 * self.weights['intervals']
        
        # 热度趋势得分 - 多时间窗口分析
        for window in [10, 20, 30]:
            recent_data = self.data[-window:] if len(self.data) >= window else self.data
            hot_nums = Counter()
            for draw in recent_data:
                for num in map(int, draw['number'].split()):
                    hot_nums[num] += 1
            
            if hot_nums:
                max_hot = max(hot_nums.values())
                weight_factor = 1.0 / 3  # 三个时间窗口平均
                
                for num in self.red_balls:
                    hot_count = hot_nums[num]
                    expected_count = window / 33 * 6  # 期望出现次数
                    
                    if hot_count >= expected_count * 1.5:  # 超热
                        hot_score = 80 + (hot_count / max_hot) * 20
                    elif hot_count >= expected_count * 0.8:  # 正常热度
                        hot_score = 85
                    elif hot_count >= expected_count * 0.3:  # 温号
                        hot_score = 80
                    else:  # 冷号
                        hot_score = 90  # 冷号给高分，平衡策略
                    
                    scores[num] += hot_score * self.weights['hot_trend'] * weight_factor
        
        # 分布得分 - 区间和奇偶平衡
        for num in self.red_balls:
            # 区间分布
            if 1 <= num <= 11:  # 小号区间
                dist_score = 80
            elif 12 <= num <= 22:  # 中号区间
                dist_score = 85
            else:  # 大号区间
                dist_score = 80
            
            # 奇偶平衡
            if num % 2 == 1:  # 奇数
                dist_score += 2
            else:  # 偶数
                dist_score += 3
            
            scores[num] += dist_score * self.weights['distribution']
        
        # 组合分析得分 - 新增
        # 分析高频配对
        pair_scores = defaultdict(float)
        if self.red_pairs:
            top_pairs = sorted(self.red_pairs.items(), key=lambda x: x[1], reverse=True)[:20]
            for pair, count in top_pairs:
                for num in pair:
                    pair_scores[num] += count
        
        if pair_scores:
            max_pair_score = max(pair_scores.values())
            for num in self.red_balls:
                pair_score = pair_scores[num]
                combo_score = 70 + (pair_score / max_pair_score) * 30
                scores[num] += combo_score * self.weights['combination']
        
        return scores
    
    def calculate_blue_scores_enhanced(self):
        """计算蓝球得分 - 增强版"""
        scores = defaultdict(float)
        
        # 频率得分
        if self.blue_frequency:
            max_freq = max(self.blue_frequency.values())
            avg_freq = sum(self.blue_frequency.values()) / len(self.blue_frequency)
            
            for num in self.blue_balls:
                freq = self.blue_frequency[num]
                if freq >= avg_freq * 1.2:
                    freq_score = 80 + (freq / max_freq) * 20
                elif freq >= avg_freq * 0.8:
                    freq_score = 85
                else:
                    freq_score = 75 + (freq / max_freq) * 15
                
                scores[num] += freq_score * 0.4
        
        # 间隔得分
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 2:
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                avg_interval = np.mean(interval_diffs)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                if last_interval <= avg_interval * 0.7:
                    interval_score = 75
                elif last_interval <= avg_interval * 1.3:
                    interval_score = 85
                elif last_interval <= avg_interval * 2.0:
                    interval_score = 90
                else:
                    interval_score = 95
                
                scores[num] += interval_score * 0.35
            else:
                scores[num] += 80 * 0.35
        
        # 热度得分
        recent_25 = self.data[-25:] if len(self.data) >= 25 else self.data
        hot_blues = [int(draw['refernumber']) for draw in recent_25]
        hot_count = Counter(hot_blues)
        
        if hot_count:
            for num in self.blue_balls:
                hot_val = hot_count[num]
                expected = len(recent_25) / 16  # 期望出现次数
                
                if hot_val >= expected * 1.5:
                    hot_score = 75
                elif hot_val >= expected * 0.5:
                    hot_score = 85
                else:
                    hot_score = 90  # 冷号给高分
                
                scores[num] += hot_score * 0.25
        
        return scores
    
    def select_seven_red_balls(self, scores):
        """选择7个红球的策略"""
        # 多策略组合
        strategies = []
        
        # 策略1：纯得分排序
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        strategy1 = [num for num, _ in sorted_scores[:7]]
        strategies.append(strategy1)
        
        # 策略2：得分排序 + 区间平衡
        top_candidates = [num for num, _ in sorted_scores[:15]]  # 前15个候选
        
        small_nums = [num for num in top_candidates if 1 <= num <= 11]
        mid_nums = [num for num in top_candidates if 12 <= num <= 22]
        big_nums = [num for num in top_candidates if 23 <= num <= 33]
        
        strategy2 = []
        # 确保每个区间至少有2个号码
        strategy2.extend(small_nums[:3] if len(small_nums) >= 3 else small_nums + [num for num, _ in sorted_scores[:10] if 1 <= num <= 11][:3-len(small_nums)])
        strategy2.extend(mid_nums[:2] if len(mid_nums) >= 2 else mid_nums + [num for num, _ in sorted_scores[:10] if 12 <= num <= 22][:2-len(mid_nums)])
        strategy2.extend(big_nums[:2] if len(big_nums) >= 2 else big_nums + [num for num, _ in sorted_scores[:10] if 23 <= num <= 33][:2-len(big_nums)])
        
        # 如果不足7个，从剩余高分中补充
        if len(strategy2) < 7:
            remaining = [num for num, _ in sorted_scores if num not in strategy2]
            strategy2.extend(remaining[:7-len(strategy2)])
        
        strategies.append(sorted(strategy2[:7]))
        
        # 策略3：奇偶平衡
        odd_candidates = [(num, score) for num, score in sorted_scores if num % 2 == 1]
        even_candidates = [(num, score) for num, score in sorted_scores if num % 2 == 0]
        
        strategy3 = []
        strategy3.extend([num for num, _ in odd_candidates[:4]])
        strategy3.extend([num for num, _ in even_candidates[:3]])
        strategies.append(sorted(strategy3))
        
        # 策略4：混合随机策略
        top_10 = [num for num, _ in sorted_scores[:10]]
        mid_10 = [num for num, _ in sorted_scores[5:15]]
        strategy4 = random.sample(top_10, 5) + random.sample(mid_10, 2)
        strategies.append(sorted(strategy4))
        
        # 随机选择策略
        selected_strategy = random.choice(strategies)
        return selected_strategy
    
    def select_blue_ball_enhanced(self, scores):
        """增强版蓝球选择"""
        # 获取前6个候选
        top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:6]
        candidate_nums = [num for num, _ in top_candidates]
        
        # 加权随机选择
        weights = [score for _, score in top_candidates]
        total_weight = sum(weights)
        weights = [w/total_weight for w in weights]
        
        selected_blue = np.random.choice(candidate_nums, p=weights)
        return int(selected_blue)
    
    def predict(self, train_size=300):
        """7红球预测"""
        if len(self.data) < train_size:
            train_size = len(self.data) - 1
        
        # 分析数据
        self.analyze_data(train_size)
        
        # 计算得分
        red_scores = self.calculate_red_scores_enhanced()
        blue_scores = self.calculate_blue_scores_enhanced()
        
        # 选择号码
        red_prediction = self.select_seven_red_balls(red_scores)
        blue_prediction = self.select_blue_ball_enhanced(blue_scores)
        
        return red_prediction, blue_prediction

def seven_red_balls_backtest(filename, test_periods=500):
    """7红球策略回测"""
    predictor = SevenRedBallsPredictor()
    predictor.load_data(filename)
    
    if len(predictor.data) < test_periods + 50:
        test_periods = len(predictor.data) - 50
        print(f"调整测试期数为: {test_periods}")
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    best_predictions = []
    
    # 7红球命中统计
    red_6_hits = 0  # 7个红球中命中6个的次数
    red_5_hits = 0  # 7个红球中命中5个的次数
    red_4_hits = 0  # 7个红球中命中4个的次数
    
    print(f"开始7红球策略回测，测试 {test_periods} 期...")
    print(f"策略：选择7个红球号码，提升命中概率")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_data = predictor.data[:-(test_periods-i)]
        test_data = predictor.data[-(test_periods-i)]
        
        # 临时设置训练数据
        original_data = predictor.data
        predictor.data = train_data
        
        try:
            # 进行预测
            red_pred, blue_pred = predictor.predict()
            
            # 获取实际结果
            actual_red = list(map(int, test_data['number'].split()))
            actual_blue = int(test_data['refernumber'])
            
            # 计算命中
            red_hits = len(set(red_pred) & set(actual_red))
            blue_hit = 1 if blue_pred == actual_blue else 0
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            # 统计特殊命中情况
            if red_hits >= 6:
                red_6_hits += 1
            elif red_hits >= 5:
                red_5_hits += 1
            elif red_hits >= 4:
                red_4_hits += 1
            
            result = {
                'period': test_data['issueno'],
                'red_hits': red_hits,
                'blue_hit': blue_hit,
                'red_pred': red_pred,
                'red_actual': actual_red,
                'blue_pred': blue_pred,
                'blue_actual': actual_blue
            }
            results.append(result)
            
            # 记录优秀预测
            if red_hits >= 3 or blue_hit == 1:
                best_predictions.append(result)
            
            if (i + 1) % 50 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = (blue_hits_total / (i + 1)) * 100
                print(f"已完成 {i + 1}/{test_periods} 期 - 当前红球平均:{current_red_avg:.2f}/7, 蓝球命中率:{current_blue_rate:.1f}%")
                
        except Exception as e:
            print(f"第 {i+1} 期预测失败: {e}")
            continue
        finally:
            # 恢复原始数据
            predictor.data = original_data
    
    # 统计结果
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = (blue_hits_total / test_periods) * 100
    
    # 红球命中分布
    red_hit_dist = Counter([r['red_hits'] for r in results])
    red_3plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 3) / test_periods * 100
    red_4plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 4) / test_periods * 100
    
    print("\n=== 7红球策略回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hits:.2f}/7 ({avg_red_hits/7*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits_total}/{test_periods})")
    print(f"红球命中≥3个的概率: {red_3plus_rate:.1f}%")
    print(f"红球命中≥4个的概率: {red_4plus_rate:.1f}%")
    
    print("\n=== 红球命中分布 ===")
    for hits in sorted(red_hit_dist.keys()):
        count = red_hit_dist[hits]
        percentage = count / test_periods * 100
        print(f"命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    print("\n=== 7红球策略优势分析 ===")
    print(f"命中6个红球: {red_6_hits}次 ({red_6_hits/test_periods*100:.2f}%)")
    print(f"命中5个红球: {red_5_hits}次 ({red_5_hits/test_periods*100:.2f}%)")
    print(f"命中4个红球: {red_4_hits}次 ({red_4_hits/test_periods*100:.2f}%)")
    
    # 计算理论提升
    # 如果选6个红球，期望命中数约为 6 * (6/33) ≈ 1.09
    # 如果选7个红球，期望命中数约为 7 * (6/33) ≈ 1.27
    theoretical_6_balls = 6 * (6/33)
    theoretical_7_balls = 7 * (6/33)
    improvement = (theoretical_7_balls - theoretical_6_balls) / theoretical_6_balls * 100
    
    print(f"\n=== 理论分析 ===")
    print(f"选择6个红球理论期望: {theoretical_6_balls:.2f}个")
    print(f"选择7个红球理论期望: {theoretical_7_balls:.2f}个")
    print(f"理论提升幅度: {improvement:.1f}%")
    print(f"实际平均命中: {avg_red_hits:.2f}个")
    print(f"实际vs理论7球: {(avg_red_hits/theoretical_7_balls-1)*100:+.1f}%")
    
    print(f"\n=== 前15期最佳预测记录 ===")
    best_predictions.sort(key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
    
    for i, pred in enumerate(best_predictions[:15], 1):
        red_mark = f"{pred['red_hits']}/7"
        blue_mark = "✓" if pred['blue_hit'] else "✗"
        print(f"{i:2d}. 期号:{pred['period']} 红球:{red_mark} 蓝球:{blue_mark}")
        print(f"    预测红球(7个): {pred['red_pred']}")
        print(f"    实际红球(6个): {pred['red_actual']}")
        print(f"    预测蓝球: {pred['blue_pred']}, 实际蓝球: {pred['blue_actual']}")
        print()
    
    print("=== 7红球策略总结 ===")
    print("✅ 优势:")
    print("  - 通过选择7个红球，理论上提升命中概率")
    print("  - 增加了命中高奖级的可能性")
    print("  - 多策略组合，提高稳定性")
    print("\n⚠️ 注意:")
    print("  - 需要购买更多注数(7选6的组合)")
    print("  - 成本会相应增加")
    print("  - 需要权衡成本与收益")
    
    # 成本分析
    combinations_count = 7  # C(7,6) = 7
    print(f"\n=== 成本分析 ===")
    print(f"7选6的组合数: {combinations_count}注")
    print(f"相比单注成本增加: {combinations_count}倍")
    print(f"平均命中提升: {(avg_red_hits/theoretical_6_balls-1)*100:+.1f}%")
    
    return {
        'avg_red_hits': avg_red_hits,
        'blue_hit_rate': blue_hit_rate,
        'red_hit_distribution': red_hit_dist,
        'best_predictions': best_predictions[:10]
    }

if __name__ == "__main__":
    seven_red_balls_backtest('ssq_data.json', 500)