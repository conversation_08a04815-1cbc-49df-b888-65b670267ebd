#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计学蓝球预测器
基于深度统计分析和实际彩票规律的蓝球预测系统
目标：蓝球命中率≥12%

核心策略：
1. 深度频率分析（冷热号平衡）
2. 间隔周期精确建模
3. 历史重现模式挖掘
4. 概率分布优化
5. 多维度权重融合
6. 自适应策略调整
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class StatisticalBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化统计学蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 策略权重（基于实际效果动态调整）
        self.strategy_weights = {
            'cold_hot_balance': 0.25,    # 冷热号平衡
            'interval_prediction': 0.20, # 间隔预测
            'historical_pattern': 0.18,  # 历史模式
            'probability_model': 0.15,   # 概率模型
            'trend_analysis': 0.12,      # 趋势分析
            'distribution_balance': 0.10  # 分布平衡
        }
        
        # 预测历史
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'strategy_performance': {strategy: [] for strategy in self.strategy_weights.keys()}
        }
        
        # 学习参数
        self.learning_rate = 0.03
        self.adaptation_threshold = 20  # 20期后开始自适应
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def cold_hot_balance_strategy(self):
        """冷热号平衡策略 - 精确的冷热号分析"""
        if not self.data or len(self.data) < 30:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 多窗口分析
        windows = [20, 30, 50, 100]
        window_weights = [0.4, 0.3, 0.2, 0.1]
        
        for window, weight in zip(windows, window_weights):
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                frequency = Counter(recent_blues)
                
                # 计算期望频率
                expected_freq = window / 16
                
                for ball in self.blue_range:
                    actual_freq = frequency[ball]
                    
                    # 冷号策略：出现频率低于期望的给高分
                    if actual_freq < expected_freq:
                        cold_score = (expected_freq - actual_freq) / expected_freq
                        scores[ball] += cold_score * weight * 0.7
                    
                    # 热号回调策略：连续出现的热号给予适当分数
                    if actual_freq > expected_freq * 1.5:
                        # 检查最近是否还在热
                        recent_10 = recent_blues[-10:] if len(recent_blues) >= 10 else recent_blues
                        if ball not in recent_10:
                            scores[ball] += 0.3 * weight
                    
                    # 均衡策略：接近期望频率的给予基础分
                    if abs(actual_freq - expected_freq) <= 1:
                        scores[ball] += 0.2 * weight
        
        return scores
    
    def interval_prediction_strategy(self):
        """间隔预测策略 - 基于间隔周期的精确预测"""
        if not self.data or len(self.data) < 20:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            # 找到该球的所有出现位置
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 2:
                # 计算间隔
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                if intervals:
                    # 统计分析
                    avg_interval = np.mean(intervals)
                    std_interval = np.std(intervals) if len(intervals) > 1 else avg_interval * 0.3
                    median_interval = np.median(intervals)
                    
                    # 当前间隔
                    current_interval = len(blue_sequence) - positions[-1]
                    
                    # 多种间隔模型
                    # 1. 基于平均间隔的预测
                    if std_interval > 0:
                        z_score = abs(current_interval - avg_interval) / std_interval
                        avg_score = np.exp(-0.5 * z_score ** 2)  # 高斯分布
                    else:
                        avg_score = 1.0 if current_interval == avg_interval else 0.5
                    
                    # 2. 基于中位数间隔的预测
                    median_score = 1.0 / (1.0 + abs(current_interval - median_interval))
                    
                    # 3. 基于最近间隔的预测
                    recent_intervals = intervals[-3:] if len(intervals) >= 3 else intervals
                    recent_avg = np.mean(recent_intervals)
                    recent_score = 1.0 / (1.0 + abs(current_interval - recent_avg))
                    
                    # 4. 周期性检测
                    period_score = 0.0
                    for period in [7, 14, 21, 28]:  # 常见周期
                        if current_interval % period == 0 or abs(current_interval % period - period) <= 1:
                            period_score = 0.8
                            break
                    
                    # 综合得分
                    scores[ball] = 0.4 * avg_score + 0.25 * median_score + 0.25 * recent_score + 0.1 * period_score
                    
                    # 特殊情况：如果间隔过长，给予更高分数
                    if current_interval > avg_interval * 2:
                        scores[ball] *= 1.5
                        
            elif len(positions) == 1:
                # 只出现过一次的球
                current_interval = len(blue_sequence) - positions[0]
                # 根据间隔长度给分
                if current_interval > 20:
                    scores[ball] = 0.9  # 很久没出现
                elif current_interval > 10:
                    scores[ball] = 0.7
                else:
                    scores[ball] = 0.5
            else:
                # 从未出现的球（理论上不应该存在）
                scores[ball] = 1.0
        
        return scores
    
    def historical_pattern_strategy(self):
        """历史模式策略 - 深度挖掘历史重现模式"""
        if not self.data or len(self.data) < 50:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 模式1：序列模式匹配
        for seq_len in [3, 4, 5, 6]:
            if len(blue_sequence) >= seq_len + 10:
                current_seq = blue_sequence[-seq_len:]
                
                # 寻找历史相似序列
                matches = []
                for i in range(len(blue_sequence) - seq_len - 1):
                    hist_seq = blue_sequence[i:i+seq_len]
                    
                    # 计算序列相似度
                    similarity = 0
                    for j in range(seq_len):
                        if current_seq[j] == hist_seq[j]:
                            similarity += 1
                        elif abs(current_seq[j] - hist_seq[j]) <= 2:  # 相近的数字
                            similarity += 0.5
                    
                    similarity_ratio = similarity / seq_len
                    
                    if similarity_ratio >= 0.6:  # 相似度阈值
                        next_ball = blue_sequence[i + seq_len]
                        matches.append((next_ball, similarity_ratio))
                
                # 计算模式得分
                if matches:
                    pattern_count = defaultdict(float)
                    total_weight = 0
                    
                    for ball, weight in matches:
                        pattern_count[ball] += weight
                        total_weight += weight
                    
                    for ball in self.blue_range:
                        if total_weight > 0:
                            pattern_score = pattern_count[ball] / total_weight
                            scores[ball] += pattern_score * (0.4 / len([3, 4, 5, 6]))
        
        # 模式2：周期性模式
        for period in [7, 14, 21, 28, 35]:
            if len(blue_sequence) >= period * 3:
                current_pos = len(blue_sequence) % period
                
                # 收集相同位置的历史数据
                same_pos_balls = []
                for i in range(period, len(blue_sequence), period):
                    if i + current_pos < len(blue_sequence):
                        same_pos_balls.append(blue_sequence[i + current_pos])
                
                if same_pos_balls:
                    pos_frequency = Counter(same_pos_balls)
                    total_same_pos = len(same_pos_balls)
                    
                    for ball in self.blue_range:
                        period_score = pos_frequency[ball] / total_same_pos
                        scores[ball] += period_score * (0.3 / len([7, 14, 21, 28, 35]))
        
        # 模式3：相邻数字模式
        if len(blue_sequence) >= 10:
            recent_10 = blue_sequence[-10:]
            
            for ball in self.blue_range:
                neighbor_score = 0
                
                # 检查相邻数字的出现情况
                neighbors = []
                if ball > 1:
                    neighbors.append(ball - 1)
                if ball < 16:
                    neighbors.append(ball + 1)
                
                for neighbor in neighbors:
                    if neighbor in recent_10:
                        # 相邻数字最近出现过，给当前数字加分
                        last_pos = len(recent_10) - 1 - recent_10[::-1].index(neighbor)
                        neighbor_score += (10 - last_pos) / 10 * 0.3
                
                scores[ball] += neighbor_score
        
        return scores
    
    def probability_model_strategy(self):
        """概率模型策略 - 基于多种概率分布的建模"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            actual_count = frequency[ball]
            expected_count = total_count / 16
            
            # 1. 泊松分布模型
            if expected_count > 0:
                poisson_prob = self.poisson_pmf(actual_count, expected_count)
                # 反向使用：实际出现少的给高分
                poisson_score = 1.0 / (1.0 + poisson_prob * 10)
            else:
                poisson_score = 0.5
            
            # 2. 二项分布模型
            p = 1.0 / 16  # 理论概率
            n = total_count
            if n > 0:
                binomial_prob = self.binomial_pmf(actual_count, n, p)
                binomial_score = 1.0 / (1.0 + binomial_prob * 10)
            else:
                binomial_score = 0.5
            
            # 3. 均匀分布偏差
            deviation = abs(actual_count - expected_count)
            uniform_score = 1.0 / (1.0 + deviation / expected_count) if expected_count > 0 else 0.5
            
            # 4. 熵模型
            if total_count > 0:
                prob = actual_count / total_count
                if prob > 0:
                    entropy_contribution = -prob * np.log2(prob)
                    entropy_score = entropy_contribution / np.log2(16)  # 归一化
                else:
                    entropy_score = 1.0  # 未出现的给最高分
            else:
                entropy_score = 0.5
            
            # 综合概率得分
            scores[ball] = 0.3 * poisson_score + 0.25 * binomial_score + 0.25 * uniform_score + 0.2 * entropy_score
        
        return scores
    
    def trend_analysis_strategy(self):
        """趋势分析策略 - 多维度趋势分析"""
        if not self.data or len(self.data) < 20:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 趋势窗口
        windows = [10, 20, 30]
        window_weights = [0.5, 0.3, 0.2]
        
        for window, weight in zip(windows, window_weights):
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                
                for ball in self.blue_range:
                    # 1. 出现趋势分析
                    positions = [i for i, blue in enumerate(recent_blues) if blue == ball]
                    
                    if positions:
                        # 计算趋势强度
                        avg_position = np.mean(positions)
                        trend_strength = avg_position / (window - 1)  # 归一化
                        
                        # 最近出现的给低分，很久没出现的给高分
                        trend_score = 1.0 - trend_strength
                        
                        # 出现频率调整
                        frequency = len(positions) / window
                        expected_freq = 1.0 / 16
                        
                        if frequency < expected_freq:
                            # 出现频率低，增加分数
                            trend_score *= 1.5
                        elif frequency > expected_freq * 2:
                            # 出现频率过高，降低分数
                            trend_score *= 0.7
                        
                        scores[ball] += trend_score * weight
                    else:
                        # 在该窗口内未出现，给高分
                        scores[ball] += 0.9 * weight
                
                # 2. 数值趋势分析
                if len(recent_blues) >= 5:
                    recent_5 = recent_blues[-5:]
                    
                    # 计算数值趋势
                    if len(set(recent_5)) > 1:  # 避免除零
                        trend_slope = (recent_5[-1] - recent_5[0]) / 4
                        
                        for ball in self.blue_range:
                            # 基于趋势预测下一个可能的数值
                            predicted_next = recent_5[-1] + trend_slope
                            
                            # 计算与预测值的距离
                            distance = abs(ball - predicted_next)
                            distance_score = 1.0 / (1.0 + distance)
                            
                            scores[ball] += distance_score * weight * 0.3
        
        return scores
    
    def distribution_balance_strategy(self):
        """分布平衡策略 - 确保数字分布的平衡性"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 分析最近的分布情况
        recent_30 = blue_sequence[-30:] if len(blue_sequence) >= 30 else blue_sequence
        
        # 1. 奇偶平衡
        odd_count = sum(1 for x in recent_30 if x % 2 == 1)
        even_count = len(recent_30) - odd_count
        
        odd_ratio = odd_count / len(recent_30) if recent_30 else 0.5
        even_ratio = even_count / len(recent_30) if recent_30 else 0.5
        
        for ball in self.blue_range:
            if ball % 2 == 1:  # 奇数
                if odd_ratio < 0.4:  # 奇数偏少
                    scores[ball] += 0.3
                elif odd_ratio > 0.6:  # 奇数偏多
                    scores[ball] += 0.1
                else:
                    scores[ball] += 0.2
            else:  # 偶数
                if even_ratio < 0.4:  # 偶数偏少
                    scores[ball] += 0.3
                elif even_ratio > 0.6:  # 偶数偏多
                    scores[ball] += 0.1
                else:
                    scores[ball] += 0.2
        
        # 2. 大小号平衡
        big_count = sum(1 for x in recent_30 if x > 8)
        small_count = len(recent_30) - big_count
        
        big_ratio = big_count / len(recent_30) if recent_30 else 0.5
        small_ratio = small_count / len(recent_30) if recent_30 else 0.5
        
        for ball in self.blue_range:
            if ball > 8:  # 大号
                if big_ratio < 0.4:
                    scores[ball] += 0.3
                elif big_ratio > 0.6:
                    scores[ball] += 0.1
                else:
                    scores[ball] += 0.2
            else:  # 小号
                if small_ratio < 0.4:
                    scores[ball] += 0.3
                elif small_ratio > 0.6:
                    scores[ball] += 0.1
                else:
                    scores[ball] += 0.2
        
        # 3. 区间平衡
        ranges = [(1, 4), (5, 8), (9, 12), (13, 16)]
        range_counts = []
        
        for start, end in ranges:
            count = sum(1 for x in recent_30 if start <= x <= end)
            range_counts.append(count)
        
        expected_range_count = len(recent_30) / 4 if recent_30 else 0
        
        for i, (start, end) in enumerate(ranges):
            actual_count = range_counts[i]
            
            for ball in range(start, end + 1):
                if ball in self.blue_range:
                    if actual_count < expected_range_count * 0.7:
                        scores[ball] += 0.2  # 该区间偏少
                    elif actual_count > expected_range_count * 1.3:
                        scores[ball] += 0.05  # 该区间偏多
                    else:
                        scores[ball] += 0.1
        
        return scores
    
    def poisson_pmf(self, k, lam):
        """泊松分布概率质量函数"""
        if lam <= 0:
            return 0
        try:
            return (lam ** k) * np.exp(-lam) / math.factorial(int(k))
        except:
            return 0
    
    def binomial_pmf(self, k, n, p):
        """二项分布概率质量函数"""
        if n <= 0 or p <= 0 or p >= 1:
            return 0
        try:
            from math import comb
            return comb(int(n), int(k)) * (p ** k) * ((1 - p) ** (n - k))
        except:
            return 0
    
    def adaptive_weight_adjustment(self):
        """自适应权重调整"""
        if self.performance_stats['total'] < self.adaptation_threshold:
            return
        
        # 计算各策略的表现
        strategy_performance = {}
        for strategy in self.strategy_weights.keys():
            if self.performance_stats['strategy_performance'][strategy]:
                recent_perf = self.performance_stats['strategy_performance'][strategy][-10:]
                strategy_performance[strategy] = np.mean(recent_perf)
            else:
                strategy_performance[strategy] = 0.0
        
        # 调整权重
        avg_performance = np.mean(list(strategy_performance.values()))
        
        for strategy in self.strategy_weights.keys():
            perf = strategy_performance[strategy]
            
            if perf > avg_performance * 1.2:  # 表现好的策略
                self.strategy_weights[strategy] *= 1.05
            elif perf < avg_performance * 0.8:  # 表现差的策略
                self.strategy_weights[strategy] *= 0.95
        
        # 归一化权重
        total_weight = sum(self.strategy_weights.values())
        for strategy in self.strategy_weights.keys():
            self.strategy_weights[strategy] /= total_weight
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各策略的得分
        strategy_scores = {
            'cold_hot_balance': self.cold_hot_balance_strategy(),
            'interval_prediction': self.interval_prediction_strategy(),
            'historical_pattern': self.historical_pattern_strategy(),
            'probability_model': self.probability_model_strategy(),
            'trend_analysis': self.trend_analysis_strategy(),
            'distribution_balance': self.distribution_balance_strategy()
        }
        
        # 计算最终得分
        final_scores = {ball: 0.0 for ball in self.blue_range}
        
        for strategy, weight in self.strategy_weights.items():
            scores = strategy_scores[strategy]
            for ball in self.blue_range:
                final_scores[ball] += scores[ball] * weight
        
        # 归一化得分
        max_score = max(final_scores.values()) if final_scores.values() else 1
        if max_score > 0:
            for ball in self.blue_range:
                final_scores[ball] /= max_score
        
        # 多种选择策略
        predictions = []
        
        # 策略1：最高分
        best_ball = max(final_scores.items(), key=lambda x: x[1])[0]
        predictions.append(best_ball)
        
        # 策略2：概率采样
        total_score = sum(final_scores.values())
        if total_score > 0:
            probs = [final_scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 策略3：前三名随机选择
        top_3 = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        top_3_ball = random.choice(top_3)[0]
        predictions.append(top_3_ball)
        
        # 策略4：冷热平衡优先
        cold_hot_scores = strategy_scores['cold_hot_balance']
        cold_hot_best = max(cold_hot_scores.items(), key=lambda x: x[1])[0]
        predictions.append(cold_hot_best)
        
        # 策略5：间隔预测优先
        interval_scores = strategy_scores['interval_prediction']
        interval_best = max(interval_scores.items(), key=lambda x: x[1])[0]
        predictions.append(interval_best)
        
        # 投票决策
        vote_count = Counter(predictions)
        most_common = vote_count.most_common(1)[0]
        
        # 如果有明显多数票，选择它；否则选择最高分
        if most_common[1] >= 3:
            final_prediction = most_common[0]
        else:
            final_prediction = best_ball
        
        # 记录预测详情
        self.prediction_history.append({
            'prediction': final_prediction,
            'final_scores': final_scores,
            'strategy_scores': {k: v[final_prediction] for k, v in strategy_scores.items()},
            'vote_results': dict(vote_count),
            'timestamp': datetime.now().isoformat()
        })
        
        return final_prediction
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 更新策略性能
        if self.prediction_history:
            last_prediction = self.prediction_history[-1]
            
            for strategy in self.strategy_weights.keys():
                if strategy in last_prediction['strategy_scores']:
                    strategy_score = last_prediction['strategy_scores'][strategy]
                    
                    # 如果该策略给出高分且预测正确，或给出低分且预测错误，都算好表现
                    if (strategy_score > 0.5 and is_correct) or (strategy_score <= 0.5 and not is_correct):
                        self.performance_stats['strategy_performance'][strategy].append(1.0)
                    else:
                        self.performance_stats['strategy_performance'][strategy].append(0.0)
                    
                    # 保持最近50次记录
                    if len(self.performance_stats['strategy_performance'][strategy]) > 50:
                        self.performance_stats['strategy_performance'][strategy].pop(0)
        
        # 自适应权重调整
        self.adaptive_weight_adjustment()
    
    def get_performance_report(self):
        """获取性能报告"""
        strategy_performance = {}
        for strategy, perf_list in self.performance_stats['strategy_performance'].items():
            if perf_list:
                strategy_performance[strategy] = np.mean(perf_list[-20:])
            else:
                strategy_performance[strategy] = 0.0
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'hit_rate': self.performance_stats['hit_rate'],
            'strategy_weights': self.strategy_weights.copy(),
            'strategy_performance': strategy_performance
        }

def test_statistical_blue_predictor():
    """测试统计学蓝球预测器"""
    print("=== 统计学蓝球预测器测试 ===")
    
    predictor = StatisticalBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(120, len(original_data) - 30)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 10 or i % 30 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    print(f"总预测次数: {report['total_predictions']}")
    print(f"正确预测次数: {report['correct_predictions']}")
    print(f"命中率: {report['hit_rate']:.4f}")
    
    print(f"\n策略权重配置:")
    for strategy, weight in report['strategy_weights'].items():
        print(f"  {strategy}: {weight:.3f}")
    
    print(f"\n策略性能:")
    for strategy, performance in report['strategy_performance'].items():
        print(f"  {strategy}: {performance:.3f}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"各策略得分:")
        for strategy, score in last_prediction['strategy_scores'].items():
            print(f"  {strategy}: {score:.4f}")
        
        print(f"投票结果: {last_prediction['vote_results']}")
        
        print(f"\n最终得分排序:")
        sorted_scores = sorted(last_prediction['final_scores'].items(), key=lambda x: x[1], reverse=True)
        for i, (ball, score) in enumerate(sorted_scores[:8]):
            print(f"  第{i+1}名: 球号{ball}, 得分{score:.4f}")
    
    return predictor

if __name__ == "__main__":
    test_statistical_blue_predictor()