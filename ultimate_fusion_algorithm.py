#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极融合双色球预测算法
融合所有优秀策略：马尔科夫链、近期权重、重号分析、深度特征工程
目标：红球命中率≥1.5/6 (25%)，蓝球命中率≥10%
"""

import json
import random
import math
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations

class UltimateFusionPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = []  # 历史红球数据
        self.blue_balls = []  # 历史蓝球数据
        
        # 马尔科夫链转移矩阵
        self.red_transition_matrix = defaultdict(lambda: defaultdict(int))
        self.blue_transition_matrix = defaultdict(lambda: defaultdict(int))
        
        # 多维度分析参数
        self.recent_periods = 50  # 扩大近期分析范围
        self.weight_decay = 0.92  # 调整权重衰减
        self.deep_analysis_periods = 100  # 深度分析期数
        
        # 高级特征
        self.repeat_probability = {}
        self.position_patterns = defaultdict(list)
        self.interval_patterns = defaultdict(list)
        self.sum_patterns = []
        self.distribution_patterns = []
        
    def load_data(self, filename='ssq_data.json'):
        """加载双色球历史数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            # 提取红球和蓝球数据
            for item in self.data:
                if 'number' in item and 'refernumber' in item:
                    # 解析红球号码
                    red_str = item['number'].strip()
                    red_numbers = [int(x) for x in red_str.split()]
                    self.red_balls.append(sorted(red_numbers))
                    
                    # 解析蓝球号码
                    blue_number = int(item['refernumber'])
                    self.blue_balls.append(blue_number)
            
            print(f"成功加载 {len(self.data)} 期历史数据")
            print(f"红球数据: {len(self.red_balls)} 期")
            print(f"蓝球数据: {len(self.blue_balls)} 期")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def build_advanced_features(self):
        """构建高级特征"""
        print("构建高级特征分析...")
        
        # 1. 马尔科夫链
        self.build_markov_chains()
        
        # 2. 重号概率分析
        self.analyze_repeat_probability()
        
        # 3. 位置模式分析
        self.analyze_position_patterns()
        
        # 4. 间隔模式分析
        self.analyze_interval_patterns()
        
        # 5. 和值分布分析
        self.analyze_sum_patterns()
        
        # 6. 分布模式分析
        self.analyze_distribution_patterns()
        
        print("高级特征构建完成")
    
    def build_markov_chains(self):
        """构建马尔科夫链转移矩阵"""
        # 红球马尔科夫链 - 多阶转移
        for i in range(2, len(self.red_balls)):
            # 一阶转移
            prev_red = tuple(self.red_balls[i-1])
            curr_red = tuple(self.red_balls[i])
            
            for prev_num in prev_red:
                for curr_num in curr_red:
                    self.red_transition_matrix[prev_num][curr_num] += 1
            
            # 二阶转移（考虑前两期）
            if i >= 2:
                prev2_red = tuple(self.red_balls[i-2])
                for prev2_num in prev2_red:
                    for prev_num in prev_red:
                        for curr_num in curr_red:
                            key = f"{prev2_num}-{prev_num}"
                            self.red_transition_matrix[key][curr_num] += 1
        
        # 蓝球马尔科夫链
        for i in range(1, len(self.blue_balls)):
            prev_blue = self.blue_balls[i-1]
            curr_blue = self.blue_balls[i]
            self.blue_transition_matrix[prev_blue][curr_blue] += 1
    
    def analyze_repeat_probability(self):
        """分析重号概率"""
        if len(self.red_balls) < 2:
            self.repeat_probability = {
                'red_repeat_rate': 0.15,
                'blue_repeat_rate': 0.06,
                'red_repeat_nums': {},
                'consecutive_repeat': 0.05
            }
            return
        
        red_repeat_count = 0
        blue_repeat_count = 0
        consecutive_repeat = 0
        total_periods = len(self.red_balls) - 1
        
        red_repeat_nums = defaultdict(int)
        for i in range(1, len(self.red_balls)):
            prev_red = set(self.red_balls[i-1])
            curr_red = set(self.red_balls[i])
            repeat_nums = prev_red & curr_red
            
            if repeat_nums:
                red_repeat_count += 1
                for num in repeat_nums:
                    red_repeat_nums[num] += 1
                
                # 检查连续重号
                if i > 1:
                    prev2_red = set(self.red_balls[i-2])
                    if prev2_red & repeat_nums:
                        consecutive_repeat += 1
        
        for i in range(1, len(self.blue_balls)):
            if self.blue_balls[i-1] == self.blue_balls[i]:
                blue_repeat_count += 1
        
        self.repeat_probability = {
            'red_repeat_rate': red_repeat_count / total_periods if total_periods > 0 else 0.15,
            'blue_repeat_rate': blue_repeat_count / total_periods if total_periods > 0 else 0.06,
            'red_repeat_nums': dict(red_repeat_nums),
            'consecutive_repeat': consecutive_repeat / total_periods if total_periods > 0 else 0.05
        }
    
    def analyze_position_patterns(self):
        """分析位置模式"""
        for i, red_list in enumerate(self.red_balls[-self.deep_analysis_periods:]):
            for pos, num in enumerate(red_list):
                self.position_patterns[pos].append(num)
    
    def analyze_interval_patterns(self):
        """分析间隔模式"""
        for red_list in self.red_balls[-self.deep_analysis_periods:]:
            for i in range(len(red_list) - 1):
                interval = red_list[i+1] - red_list[i]
                self.interval_patterns[red_list[i]].append(interval)
    
    def analyze_sum_patterns(self):
        """分析和值模式"""
        for red_list in self.red_balls[-self.deep_analysis_periods:]:
            self.sum_patterns.append(sum(red_list))
    
    def analyze_distribution_patterns(self):
        """分析分布模式"""
        for red_list in self.red_balls[-self.deep_analysis_periods:]:
            # 区间分布
            zone1 = len([x for x in red_list if 1 <= x <= 11])  # 1-11区
            zone2 = len([x for x in red_list if 12 <= x <= 22])  # 12-22区
            zone3 = len([x for x in red_list if 23 <= x <= 33])  # 23-33区
            
            # 奇偶分布
            odd_count = len([x for x in red_list if x % 2 == 1])
            even_count = 6 - odd_count
            
            self.distribution_patterns.append({
                'zones': [zone1, zone2, zone3],
                'odd_even': [odd_count, even_count]
            })
    
    def calculate_enhanced_weights(self, numbers, is_red=True):
        """计算增强权重"""
        weights = defaultdict(float)
        recent_data = (self.red_balls if is_red else self.blue_balls)[-self.recent_periods:]
        
        # 1. 基础频率权重
        for i, period_data in enumerate(recent_data):
            period_weight = self.weight_decay ** (len(recent_data) - 1 - i)
            
            if is_red:
                for num in period_data:
                    weights[num] += period_weight
            else:
                weights[period_data] += period_weight
        
        # 2. 趋势权重（近期上升趋势加权）
        if is_red:
            for num in range(1, 34):
                recent_10 = sum(1 for period in recent_data[-10:] if num in period)
                recent_20 = sum(1 for period in recent_data[-20:] if num in period)
                if recent_10 > recent_20 * 0.5:  # 近期趋势上升
                    weights[num] += 0.5
        
        return weights
    
    def get_enhanced_markov_probabilities(self, last_numbers, is_red=True):
        """获取增强马尔科夫概率"""
        probabilities = defaultdict(float)
        transition_matrix = self.red_transition_matrix if is_red else self.blue_transition_matrix
        
        if is_red:
            # 一阶转移
            for last_num in last_numbers:
                if last_num in transition_matrix:
                    total_transitions = sum(transition_matrix[last_num].values())
                    if total_transitions > 0:
                        for next_num, count in transition_matrix[last_num].items():
                            probabilities[next_num] += (count / total_transitions) * 0.7
            
            # 二阶转移（如果有足够历史数据）
            if len(self.red_balls) >= 2:
                prev2_numbers = self.red_balls[-2]
                for prev2_num in prev2_numbers:
                    for last_num in last_numbers:
                        key = f"{prev2_num}-{last_num}"
                        if key in transition_matrix:
                            total_transitions = sum(transition_matrix[key].values())
                            if total_transitions > 0:
                                for next_num, count in transition_matrix[key].items():
                                    if isinstance(next_num, int):
                                        probabilities[next_num] += (count / total_transitions) * 0.3
        else:
            if last_numbers in transition_matrix:
                total_transitions = sum(transition_matrix[last_numbers].values())
                if total_transitions > 0:
                    for next_num, count in transition_matrix[last_numbers].items():
                        probabilities[next_num] = count / total_transitions
        
        return probabilities
    
    def predict_red_balls_ultimate(self):
        """终极红球预测"""
        if not self.red_balls:
            return random.sample(range(1, 34), 6)
        
        last_red = self.red_balls[-1]
        
        # 1. 增强权重分析 (25%)
        recent_weights = self.calculate_enhanced_weights(range(1, 34), is_red=True)
        
        # 2. 增强马尔科夫链转移概率 (25%)
        markov_probs = self.get_enhanced_markov_probabilities(last_red, is_red=True)
        
        # 3. 重号概率调整 (15%)
        repeat_bonus = {}
        for num in last_red:
            repeat_bonus[num] = self.repeat_probability.get('red_repeat_rate', 0.15)
        
        # 4. 位置模式分析 (10%)
        position_scores = defaultdict(float)
        for pos, nums in self.position_patterns.items():
            if nums:
                freq_counter = Counter(nums[-20:])  # 近20期位置频率
                for num, freq in freq_counter.items():
                    position_scores[num] += freq / 20
        
        # 5. 间隔模式分析 (10%)
        interval_scores = defaultdict(float)
        for base_num, intervals in self.interval_patterns.items():
            if intervals and base_num in last_red:
                avg_interval = sum(intervals[-10:]) / len(intervals[-10:])
                predicted_next = base_num + int(avg_interval)
                if 1 <= predicted_next <= 33:
                    interval_scores[predicted_next] += 0.5
        
        # 6. 和值约束 (5%)
        sum_scores = defaultdict(float)
        if self.sum_patterns:
            recent_sums = self.sum_patterns[-20:]
            avg_sum = sum(recent_sums) / len(recent_sums)
            target_sum_range = (int(avg_sum * 0.8), int(avg_sum * 1.2))
        else:
            target_sum_range = (80, 150)
        
        # 7. 分布约束 (10%)
        distribution_scores = defaultdict(float)
        if self.distribution_patterns:
            recent_patterns = self.distribution_patterns[-20:]
            avg_zones = [sum(p['zones'][i] for p in recent_patterns) / len(recent_patterns) for i in range(3)]
            avg_odd = sum(p['odd_even'][0] for p in recent_patterns) / len(recent_patterns)
        
        # 综合评分计算
        scores = defaultdict(float)
        for num in range(1, 34):
            # 基础权重 (25%)
            scores[num] += recent_weights.get(num, 0) * 0.25
            
            # 马尔科夫概率 (25%)
            scores[num] += markov_probs.get(num, 0) * 0.25
            
            # 重号加权 (15%)
            scores[num] += repeat_bonus.get(num, 0) * 0.15
            
            # 位置模式 (10%)
            scores[num] += position_scores.get(num, 0) * 0.10
            
            # 间隔模式 (10%)
            scores[num] += interval_scores.get(num, 0) * 0.10
            
            # 分布平衡 (10%)
            zone_bonus = 0
            if 1 <= num <= 11:
                zone_bonus = 0.3 if avg_zones[0] < 2.5 else 0.1
            elif 12 <= num <= 22:
                zone_bonus = 0.3 if avg_zones[1] < 2.5 else 0.1
            else:
                zone_bonus = 0.3 if avg_zones[2] < 2.5 else 0.1
            scores[num] += zone_bonus * 0.05
            
            # 奇偶平衡 (5%)
            odd_bonus = 0.2 if (num % 2 == 1 and avg_odd < 3) or (num % 2 == 0 and avg_odd > 3) else 0
            scores[num] += odd_bonus * 0.05
        
        # 智能选择策略
        candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 多轮选择确保质量
        selected = []
        
        # 第一轮：选择前8个高分候选
        top_candidates = [num for num, score in candidates[:8]]
        selected.extend(random.sample(top_candidates, min(4, len(top_candidates))))
        
        # 第二轮：从中等分数中选择，保持多样性
        mid_candidates = [num for num, score in candidates[8:16] if num not in selected]
        if mid_candidates:
            selected.extend(random.sample(mid_candidates, min(2, len(mid_candidates))))
        
        # 第三轮：填充剩余位置
        while len(selected) < 6:
            remaining = [i for i in range(1, 34) if i not in selected]
            if remaining:
                # 优先选择符合和值约束的号码
                current_sum = sum(selected)
                needed_sum = random.randint(target_sum_range[0], target_sum_range[1])
                remaining_sum = needed_sum - current_sum
                remaining_count = 6 - len(selected)
                
                if remaining_count > 0:
                    avg_remaining = remaining_sum / remaining_count
                    suitable = [num for num in remaining if abs(num - avg_remaining) < 10]
                    
                    if suitable:
                        selected.append(random.choice(suitable))
                    else:
                        selected.append(random.choice(remaining))
        
        return sorted(selected[:6])
    
    def predict_blue_ball_ultimate(self):
        """终极蓝球预测"""
        if not self.blue_balls:
            return random.randint(1, 16)
        
        last_blue = self.blue_balls[-1]
        
        # 1. 增强权重分析 (40%)
        recent_weights = self.calculate_enhanced_weights(range(1, 17), is_red=False)
        
        # 2. 马尔科夫链转移概率 (30%)
        markov_probs = self.get_enhanced_markov_probabilities(last_blue, is_red=False)
        
        # 3. 重号概率 (15%)
        repeat_bonus = self.repeat_probability.get('blue_repeat_rate', 0.06)
        
        # 4. 周期性分析 (15%)
        cycle_scores = defaultdict(float)
        recent_blues = self.blue_balls[-30:]
        for i, blue in enumerate(recent_blues):
            cycle_weight = (i + 1) / len(recent_blues)  # 越近期权重越高
            cycle_scores[blue] += cycle_weight
        
        # 综合评分
        scores = defaultdict(float)
        for num in range(1, 17):
            # 基础权重 (40%)
            scores[num] += recent_weights.get(num, 0) * 0.40
            
            # 马尔科夫概率 (30%)
            scores[num] += markov_probs.get(num, 0) * 0.30
            
            # 重号加权 (15%)
            if num == last_blue:
                scores[num] += repeat_bonus * 0.15
            
            # 周期性 (15%)
            scores[num] += cycle_scores.get(num, 0) * 0.15
        
        # 智能选择
        if scores:
            # 按分数排序，从前6个中加权随机选择
            top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:6]
            weights = [score for num, score in top_candidates]
            
            if sum(weights) > 0:
                # 加权随机选择
                selected_idx = np.random.choice(len(top_candidates), p=np.array(weights)/sum(weights))
                return top_candidates[selected_idx][0]
        
        # 备选方案
        return random.randint(1, 16)
    
    def predict(self):
        """生成终极预测"""
        red_prediction = self.predict_red_balls_ultimate()
        blue_prediction = self.predict_blue_ball_ultimate()
        
        return {
            'red': red_prediction,
            'blue': blue_prediction,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def ultimate_fusion_backtest(periods=500):
    """终极融合算法回测"""
    print("=== 终极融合双色球预测算法回测 ===")
    print(f"回测期数: {periods}")
    print(f"目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("="*50)
    
    predictor = UltimateFusionPredictor()
    
    if not predictor.load_data():
        return
    
    if len(predictor.data) < periods + 100:
        print(f"数据不足，需要至少 {periods + 100} 期数据")
        return
    
    # 回测统计
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    # 使用前面的数据训练，后面的数据测试
    test_start = len(predictor.data) - periods
    
    for i in range(test_start, len(predictor.data)):
        # 使用到当前期之前的数据进行预测
        predictor.red_balls = []
        predictor.blue_balls = []
        
        for item in predictor.data[:i]:
            if 'number' in item and 'refernumber' in item:
                red_str = item['number'].strip()
                red_numbers = [int(x) for x in red_str.split()]
                predictor.red_balls.append(sorted(red_numbers))
                
                blue_number = int(item['refernumber'])
                predictor.blue_balls.append(blue_number)
        
        # 构建高级特征
        predictor.build_advanced_features()
        
        # 生成预测
        prediction = predictor.predict()
        actual = predictor.data[i]
        
        # 解析实际开奖号码
        actual_red_str = actual['number'].strip()
        actual_red = [int(x) for x in actual_red_str.split()]
        actual_blue = int(actual['refernumber'])
        
        # 计算红球命中数
        red_hit_count = len(set(prediction['red']) & set(actual_red))
        red_hits.append(red_hit_count)
        
        # 计算蓝球命中
        blue_hit = 1 if prediction['blue'] == actual_blue else 0
        blue_hits += blue_hit
        
        # 记录优秀预测
        if red_hit_count >= 3 or blue_hit:
            best_predictions.append({
                'period': actual.get('issueno', f'第{i+1}期'),
                'red_hit': red_hit_count,
                'blue_hit': blue_hit,
                'predicted_red': prediction['red'],
                'actual_red': actual_red,
                'predicted_blue': prediction['blue'],
                'actual_blue': actual_blue
            })
        
        # 进度显示
        if (i - test_start + 1) % 50 == 0:
            current_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / len(red_hits)
            print(f"进度: {i - test_start + 1}/{periods}, 当前红球: {current_avg:.2f}/6, 蓝球: {current_blue_rate:.1%}")
    
    # 统计结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / periods
    
    print(f"\n=== 终极融合算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits}/{periods})")
    
    # 红球命中分布
    red_distribution = Counter(red_hits)
    print(f"\n红球命中分布:")
    for hits in sorted(red_distribution.keys()):
        count = red_distribution[hits]
        percentage = count / periods * 100
        print(f"  {hits}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 0.10
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6 ({red_target/6*100:.1f}%), 实际: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%), 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target:.1%}, 实际: {blue_hit_rate:.1%}, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前{min(25, len(best_predictions))}期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:25], 1):
            blue_mark = "✓" if pred['blue_hit'] else "✗"
            print(f"{i:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_mark}")
            print(f"    预测红球: {pred['predicted_red']}")
            print(f"    实际红球: {pred['actual_red']}")
            print(f"    预测蓝球: {pred['predicted_blue']}, 实际蓝球: {pred['actual_blue']}")
            print()
    
    # 最终评估
    print(f"\n=== 终极算法评估 ===")
    if red_achievement >= 90 and blue_achievement >= 90:
        print(f"🎉 终极算法成功！已达成预期目标！")
    elif red_achievement >= 80 or blue_achievement >= 80:
        print(f"📈 终极算法表现优秀，非常接近目标！")
    elif red_achievement >= 70 or blue_achievement >= 70:
        print(f"🔧 终极算法表现良好，还有优化空间")
    else:
        print(f"⚠️ 需要进一步调整算法参数")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement
    }

if __name__ == "__main__":
    ultimate_fusion_backtest(500)