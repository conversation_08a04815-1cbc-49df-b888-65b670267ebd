#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
遗传算法优化的双色球预测算法
使用遗传算法自动优化参数和权重配置
目标：红球≥1.5/6，蓝球≥10%
"""

import json
import numpy as np
import random
from collections import defaultdict, Counter
from datetime import datetime
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class GeneticOptimizedPredictor:
    """遗传算法优化的预测器"""
    
    def __init__(self):
        self.data = []
        # 可优化的参数基因
        self.genes = {
            'frequency_weight': 0.25,      # 频率分析权重
            'markov_weight': 0.20,         # 马尔科夫链权重
            'pattern_weight': 0.15,        # 模式识别权重
            'trend_weight': 0.15,          # 趋势分析权重
            'balance_weight': 0.10,        # 平衡分析权重
            'repeat_weight': 0.15,         # 重号分析权重
            
            'frequency_window': 30,        # 频率分析窗口
            'markov_window': 50,           # 马尔科夫窗口
            'pattern_window': 10,          # 模式分析窗口
            'trend_window': 15,            # 趋势分析窗口
            
            'red_selection_factor': 1.2,  # 红球选择因子
            'blue_selection_factor': 1.5, # 蓝球选择因子
            
            'zone_balance_factor': 0.3,   # 区间平衡因子
            'odd_even_factor': 0.2,       # 奇偶平衡因子
            'sum_range_factor': 0.25,     # 和值范围因子
        }
        
        # 特征缓存
        self.feature_cache = {}
        
    def load_data(self, file_path: str):
        """加载历史数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 期历史数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def parse_numbers(self, number_str: str) -> List[int]:
        """解析号码字符串"""
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def frequency_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """频率分析"""
        window_size = int(self.genes['frequency_window'])
        data_window = recent_data[-window_size:] if len(recent_data) > window_size else recent_data
        
        red_freq = [0] * 33
        blue_freq = [0] * 16
        
        for record in data_window:
            red_nums = self.parse_numbers(record['number'])
            blue_num = int(record['refernumber'])
            
            for num in red_nums:
                if 1 <= num <= 33:
                    red_freq[num-1] += 1
            
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        # 归一化并应用权重衰减
        total_red = sum(red_freq)
        total_blue = sum(blue_freq)
        
        if total_red > 0:
            red_freq = [f / total_red for f in red_freq]
        if total_blue > 0:
            blue_freq = [f / total_blue for f in blue_freq]
        
        # 应用时间衰减权重（越近的数据权重越高）
        for i, record in enumerate(data_window):
            weight = (i + 1) / len(data_window)  # 线性衰减
            # 这里可以进一步优化衰减函数
        
        return red_freq, blue_freq
    
    def markov_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """马尔科夫链分析"""
        window_size = int(self.genes['markov_window'])
        if len(recent_data) < 2:
            return [1/33] * 33, [1/16] * 16
        
        data_window = recent_data[-window_size:] if len(recent_data) > window_size else recent_data
        
        # 构建转移矩阵
        red_transitions = defaultdict(lambda: defaultdict(float))
        blue_transitions = defaultdict(float)
        
        for i in range(len(data_window) - 1):
            current_red = set(self.parse_numbers(data_window[i]['number']))
            next_red = set(self.parse_numbers(data_window[i+1]['number']))
            current_blue = int(data_window[i]['refernumber'])
            next_blue = int(data_window[i+1]['refernumber'])
            
            # 时间权重（越近的转移权重越高）
            time_weight = (i + 1) / len(data_window)
            
            # 红球转移
            for curr_num in current_red:
                for next_num in next_red:
                    red_transitions[curr_num][next_num] += time_weight
            
            # 蓝球转移
            blue_transitions[next_blue] += time_weight
        
        # 计算预测概率
        last_red = set(self.parse_numbers(recent_data[-1]['number']))
        
        red_probs = [0.0] * 33
        for num in range(1, 34):
            total_transitions = 0
            prob_sum = 0
            
            for last_num in last_red:
                if last_num in red_transitions:
                    total = sum(red_transitions[last_num].values())
                    if total > 0:
                        prob_sum += red_transitions[last_num].get(num, 0) / total
                        total_transitions += 1
            
            if total_transitions > 0:
                red_probs[num-1] = prob_sum / total_transitions
            else:
                red_probs[num-1] = 1/33
        
        # 蓝球概率
        blue_probs = [0.0] * 16
        total_blue_trans = sum(blue_transitions.values())
        
        for num in range(1, 17):
            if total_blue_trans > 0:
                blue_probs[num-1] = blue_transitions.get(num, 0) / total_blue_trans
            else:
                blue_probs[num-1] = 1/16
        
        return red_probs, blue_probs
    
    def pattern_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """模式识别分析"""
        window_size = int(self.genes['pattern_window'])
        if len(recent_data) < window_size:
            return [1/33] * 33, [1/16] * 16
        
        recent_window = recent_data[-window_size:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 奇偶模式分析
        odd_count = 0
        even_count = 0
        
        for record in recent_window:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if num % 2 == 1:
                    odd_count += 1
                else:
                    even_count += 1
        
        total_count = odd_count + even_count
        if total_count > 0:
            odd_ratio = odd_count / total_count
            even_ratio = even_count / total_count
            
            # 应用奇偶平衡因子
            odd_even_factor = self.genes['odd_even_factor']
            
            for num in range(1, 34):
                if num % 2 == 1:
                    red_scores[num-1] += odd_ratio * odd_even_factor
                else:
                    red_scores[num-1] += even_ratio * odd_even_factor
        
        # 区间分析
        zone_counts = [0, 0, 0]  # 1-11, 12-22, 23-33
        for record in recent_window:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if 1 <= num <= 11:
                    zone_counts[0] += 1
                elif 12 <= num <= 22:
                    zone_counts[1] += 1
                elif 23 <= num <= 33:
                    zone_counts[2] += 1
        
        total_zone = sum(zone_counts)
        if total_zone > 0:
            zone_ratios = [c / total_zone for c in zone_counts]
            zone_balance_factor = self.genes['zone_balance_factor']
            
            for num in range(1, 34):
                if 1 <= num <= 11:
                    red_scores[num-1] += zone_ratios[0] * zone_balance_factor
                elif 12 <= num <= 22:
                    red_scores[num-1] += zone_ratios[1] * zone_balance_factor
                elif 23 <= num <= 33:
                    red_scores[num-1] += zone_ratios[2] * zone_balance_factor
        
        # 蓝球模式
        blue_freq = [0] * 16
        for record in recent_window:
            blue_num = int(record['refernumber'])
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        total_blue = sum(blue_freq)
        if total_blue > 0:
            blue_scores = [f / total_blue for f in blue_freq]
        else:
            blue_scores = [1/16] * 16
        
        return red_scores, blue_scores
    
    def trend_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """趋势分析"""
        window_size = int(self.genes['trend_window'])
        if len(recent_data) < window_size:
            return [1/33] * 33, [1/16] * 16
        
        recent_window = recent_data[-window_size:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 计算每个号码的出现趋势
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_window):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 2:
                # 计算间隔趋势
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                avg_interval = sum(intervals) / len(intervals)
                
                # 预测下次出现的可能性
                last_appearance = appearances[-1]
                expected_next = last_appearance + avg_interval
                
                # 距离当前期数的差值
                distance = abs(len(recent_window) - expected_next)
                score = max(0, 1 - distance / window_size)
                red_scores[num-1] = score
            elif len(appearances) == 1:
                # 只出现一次
                last_pos = appearances[0]
                score = (window_size - last_pos) / window_size
                red_scores[num-1] = score * 0.5
            else:
                # 未出现，给予基础分数
                red_scores[num-1] = 0.3
        
        # 蓝球趋势分析
        blue_appearances = {}
        for i, record in enumerate(recent_window):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_appearances:
                blue_appearances[blue_num] = []
            blue_appearances[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_appearances and len(blue_appearances[num]) >= 1:
                last_appearance = blue_appearances[num][-1]
                score = (window_size - last_appearance) / window_size
                blue_scores[num-1] = score
            else:
                blue_scores[num-1] = 0.4
        
        return red_scores, blue_scores
    
    def balance_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """平衡分析"""
        red_scores = [1.0] * 33  # 基础分数
        blue_scores = [1.0] * 16
        
        if len(recent_data) < 5:
            return red_scores, blue_scores
        
        recent_5 = recent_data[-5:]
        
        # 和值分析
        sum_values = []
        for record in recent_5:
            red_nums = self.parse_numbers(record['number'])
            sum_values.append(sum(red_nums))
        
        avg_sum = sum(sum_values) / len(sum_values)
        sum_range_factor = self.genes['sum_range_factor']
        
        # 根据历史和值调整分数
        for num in range(1, 34):
            # 如果选择这个号码，对和值的影响
            # 这里简化处理，实际可以更复杂
            if avg_sum < 90:  # 和值偏小，倾向选择大号
                if num > 16:
                    red_scores[num-1] += sum_range_factor
            elif avg_sum > 130:  # 和值偏大，倾向选择小号
                if num <= 16:
                    red_scores[num-1] += sum_range_factor
        
        return red_scores, blue_scores
    
    def repeat_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """重号分析"""
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        if len(recent_data) < 2:
            return [1/33] * 33, [1/16] * 16
        
        # 分析最近几期的重号概率
        last_red = set(self.parse_numbers(recent_data[-1]['number']))
        last_blue = int(recent_data[-1]['refernumber'])
        
        # 统计历史重号概率
        repeat_counts = [0] * 7  # 0-6个重号
        total_periods = 0
        
        for i in range(len(recent_data) - 1):
            if i >= 50:  # 只看最近50期
                break
            
            current_red = set(self.parse_numbers(recent_data[-(i+1)]['number']))
            next_red = set(self.parse_numbers(recent_data[-(i+2)]['number']))
            
            repeat_count = len(current_red & next_red)
            repeat_counts[repeat_count] += 1
            total_periods += 1
        
        if total_periods > 0:
            # 计算各重号数量的概率
            repeat_probs = [c / total_periods for c in repeat_counts]
            
            # 根据重号概率调整分数
            repeat_weight = self.genes['repeat_weight']
            
            # 给上期号码一定的重号概率
            for num in last_red:
                # 根据历史重号概率给分
                prob_sum = sum(repeat_probs[1:])  # 至少1个重号的概率
                red_scores[num-1] = prob_sum * repeat_weight
        
        # 蓝球重号分析
        blue_repeat_count = 0
        blue_total = 0
        
        for i in range(min(20, len(recent_data) - 1)):
            current_blue = int(recent_data[-(i+1)]['refernumber'])
            next_blue = int(recent_data[-(i+2)]['refernumber'])
            
            if current_blue == next_blue:
                blue_repeat_count += 1
            blue_total += 1
        
        if blue_total > 0:
            blue_repeat_prob = blue_repeat_count / blue_total
            blue_scores[last_blue-1] = blue_repeat_prob * repeat_weight
        
        return red_scores, blue_scores
    
    def ensemble_predict(self, recent_data: List[Dict]) -> Tuple[List[int], int]:
        """集成预测"""
        # 获取各种分析结果
        freq_red, freq_blue = self.frequency_analysis(recent_data)
        markov_red, markov_blue = self.markov_analysis(recent_data)
        pattern_red, pattern_blue = self.pattern_analysis(recent_data)
        trend_red, trend_blue = self.trend_analysis(recent_data)
        balance_red, balance_blue = self.balance_analysis(recent_data)
        repeat_red, repeat_blue = self.repeat_analysis(recent_data)
        
        # 集成红球预测
        red_ensemble = [0.0] * 33
        for i in range(33):
            red_ensemble[i] = (
                freq_red[i] * self.genes['frequency_weight'] +
                markov_red[i] * self.genes['markov_weight'] +
                pattern_red[i] * self.genes['pattern_weight'] +
                trend_red[i] * self.genes['trend_weight'] +
                balance_red[i] * self.genes['balance_weight'] +
                repeat_red[i] * self.genes['repeat_weight']
            )
        
        # 集成蓝球预测
        blue_ensemble = [0.0] * 16
        for i in range(16):
            blue_ensemble[i] = (
                freq_blue[i] * self.genes['frequency_weight'] +
                markov_blue[i] * self.genes['markov_weight'] +
                pattern_blue[i] * self.genes['pattern_weight'] +
                trend_blue[i] * self.genes['trend_weight'] +
                balance_blue[i] * self.genes['balance_weight'] +
                repeat_blue[i] * self.genes['repeat_weight']
            )
        
        # 应用选择因子
        red_selection_factor = self.genes['red_selection_factor']
        blue_selection_factor = self.genes['blue_selection_factor']
        
        # 选择红球
        red_with_scores = [(i+1, score * red_selection_factor) for i, score in enumerate(red_ensemble)]
        red_with_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 智能选择策略
        selected_red = []
        candidates = red_with_scores[:15]  # 从前15个候选中选择
        
        for num, score in candidates:
            if len(selected_red) < 6:
                if self._is_combination_valid(selected_red + [num]):
                    selected_red.append(num)
        
        # 如果选择不足6个，补充高分号码
        while len(selected_red) < 6:
            for num, score in red_with_scores:
                if num not in selected_red:
                    selected_red.append(num)
                    break
        
        selected_red = sorted(selected_red[:6])
        
        # 选择蓝球
        blue_with_scores = [(i+1, score * blue_selection_factor) for i, score in enumerate(blue_ensemble)]
        blue_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_scores[0][0]
        
        return selected_red, selected_blue
    
    def _is_combination_valid(self, numbers: List[int]) -> bool:
        """检查号码组合是否有效"""
        if len(numbers) <= 1:
            return True
        
        numbers_sorted = sorted(numbers)
        
        # 检查连号不超过3个
        consecutive_count = 1
        max_consecutive = 1
        
        for i in range(1, len(numbers_sorted)):
            if numbers_sorted[i] == numbers_sorted[i-1] + 1:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 1
        
        if max_consecutive > 3:
            return False
        
        # 检查区间分布
        if len(numbers) >= 4:
            zone1 = sum(1 for n in numbers if 1 <= n <= 11)
            zone2 = sum(1 for n in numbers if 12 <= n <= 22)
            zone3 = sum(1 for n in numbers if 23 <= n <= 33)
            
            # 避免某个区间过于集中
            if zone1 > len(numbers) * 0.8 or zone2 > len(numbers) * 0.8 or zone3 > len(numbers) * 0.8:
                return False
        
        return True
    
    def mutate_genes(self, mutation_rate: float = 0.1):
        """基因突变"""
        for key in self.genes:
            if random.random() < mutation_rate:
                if 'weight' in key:
                    # 权重突变
                    self.genes[key] += random.uniform(-0.05, 0.05)
                    self.genes[key] = max(0.01, min(0.5, self.genes[key]))
                elif 'window' in key:
                    # 窗口大小突变
                    self.genes[key] += random.randint(-5, 5)
                    self.genes[key] = max(5, min(100, int(self.genes[key])))
                elif 'factor' in key:
                    # 因子突变
                    self.genes[key] += random.uniform(-0.1, 0.1)
                    self.genes[key] = max(0.1, min(2.0, self.genes[key]))
        
        # 归一化权重
        weight_keys = [k for k in self.genes.keys() if 'weight' in k]
        total_weight = sum(self.genes[k] for k in weight_keys)
        if total_weight > 0:
            for k in weight_keys:
                self.genes[k] /= total_weight
    
    def copy_genes_from(self, other_predictor):
        """从另一个预测器复制基因"""
        self.genes = other_predictor.genes.copy()
    
    def crossover_genes(self, other_predictor, crossover_rate: float = 0.5):
        """基因交叉"""
        for key in self.genes:
            if random.random() < crossover_rate:
                self.genes[key] = other_predictor.genes[key]

def genetic_algorithm_optimize(data_file: str, population_size: int = 20, generations: int = 10, test_periods: int = 100):
    """遗传算法优化"""
    print("=== 遗传算法优化双色球预测算法 ===")
    print(f"种群大小: {population_size}, 进化代数: {generations}, 测试期数: {test_periods}")
    print("=" * 60)
    
    # 创建初始种群
    population = []
    for i in range(population_size):
        predictor = GeneticOptimizedPredictor()
        if not predictor.load_data(data_file):
            return None
        
        # 随机初始化基因
        predictor.mutate_genes(mutation_rate=1.0)  # 完全随机化
        population.append(predictor)
    
    print(f"初始种群创建完成，开始进化...")
    
    best_fitness_history = []
    
    for generation in range(generations):
        print(f"\n第 {generation + 1} 代进化中...")
        
        # 评估种群适应度
        fitness_scores = []
        for i, predictor in enumerate(population):
            fitness = evaluate_fitness(predictor, test_periods)
            fitness_scores.append((fitness, i))
            
            if (i + 1) % 5 == 0:
                print(f"  评估进度: {i + 1}/{population_size}")
        
        # 按适应度排序
        fitness_scores.sort(reverse=True)
        best_fitness = fitness_scores[0][0]
        best_fitness_history.append(best_fitness)
        
        print(f"第 {generation + 1} 代最佳适应度: {best_fitness:.4f}")
        
        # 选择优秀个体
        elite_count = population_size // 4
        new_population = []
        
        # 保留精英
        for i in range(elite_count):
            idx = fitness_scores[i][1]
            new_population.append(population[idx])
        
        # 生成新个体
        while len(new_population) < population_size:
            # 选择父母（轮盘赌选择）
            parent1_idx = tournament_selection(fitness_scores, 3)
            parent2_idx = tournament_selection(fitness_scores, 3)
            
            # 创建子代
            child = GeneticOptimizedPredictor()
            child.load_data(data_file)
            child.copy_genes_from(population[parent1_idx])
            child.crossover_genes(population[parent2_idx])
            child.mutate_genes(mutation_rate=0.1)
            
            new_population.append(child)
        
        population = new_population
    
    # 返回最佳个体
    final_fitness_scores = []
    for i, predictor in enumerate(population):
        fitness = evaluate_fitness(predictor, test_periods)
        final_fitness_scores.append((fitness, i))
    
    final_fitness_scores.sort(reverse=True)
    best_predictor = population[final_fitness_scores[0][1]]
    
    print(f"\n=== 遗传算法优化完成 ===")
    print(f"最佳适应度: {final_fitness_scores[0][0]:.4f}")
    print(f"适应度进化历程: {[f'{f:.3f}' for f in best_fitness_history]}")
    
    return best_predictor

def tournament_selection(fitness_scores: List[Tuple[float, int]], tournament_size: int) -> int:
    """锦标赛选择"""
    tournament = random.sample(fitness_scores, min(tournament_size, len(fitness_scores)))
    return max(tournament, key=lambda x: x[0])[1]

def evaluate_fitness(predictor: GeneticOptimizedPredictor, test_periods: int) -> float:
    """评估预测器适应度"""
    if len(predictor.data) < test_periods + 50:
        return 0.0
    
    # 使用最后test_periods期进行测试
    test_data = predictor.data[-test_periods:]
    train_data = predictor.data[:-test_periods]
    
    red_hits = []
    blue_hits = 0
    
    for i in range(test_periods):
        # 使用到当前期为止的历史数据
        history_data = predictor.data[:-(test_periods-i)]
        
        try:
            # 预测
            pred_red, pred_blue = predictor.ensemble_predict(history_data)
            
            # 实际结果
            actual_data = test_data[i]
            actual_red = predictor.parse_numbers(actual_data['number'])
            actual_blue = int(actual_data['refernumber'])
            
            # 计算命中
            red_hit_count = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            
            red_hits.append(red_hit_count)
            blue_hits += blue_hit
        except Exception as e:
            # 预测失败，给予惩罚
            red_hits.append(0)
    
    # 计算适应度
    avg_red_hits = sum(red_hits) / len(red_hits) if red_hits else 0
    blue_hit_rate = blue_hits / test_periods * 100 if test_periods > 0 else 0
    
    # 适应度函数：红球命中率 * 0.7 + 蓝球命中率 * 0.3
    fitness = (avg_red_hits / 6) * 0.7 + (blue_hit_rate / 100) * 0.3
    
    return fitness

def genetic_optimized_backtest(predictor: GeneticOptimizedPredictor, test_periods: int = 500):
    """遗传优化算法回测"""
    print("\n=== 遗传优化算法回测 ===")
    print(f"回测期数: {test_periods}")
    print("目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("=" * 50)
    
    if len(predictor.data) < test_periods + 100:
        print(f"数据不足，需要至少 {test_periods + 100} 期数据")
        return
    
    test_data = predictor.data[-test_periods:]
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    print(f"开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用历史数据预测
        history_data = predictor.data[:-(test_periods-i)]
        
        try:
            pred_red, pred_blue = predictor.ensemble_predict(history_data)
            
            # 实际结果
            actual_data = test_data[i]
            actual_red = predictor.parse_numbers(actual_data['number'])
            actual_blue = int(actual_data['refernumber'])
            
            # 计算命中
            red_hit_count = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            
            red_hits.append(red_hit_count)
            blue_hits += blue_hit
            
            # 记录优秀预测
            if red_hit_count >= 3 or blue_hit == 1:
                best_predictions.append({
                    'period': actual_data['issueno'],
                    'red_hit': red_hit_count,
                    'blue_hit': blue_hit,
                    'predicted_red': pred_red,
                    'actual_red': actual_red,
                    'predicted_blue': pred_blue,
                    'actual_blue': actual_blue
                })
        except Exception as e:
            print(f"预测第 {i+1} 期时出错: {e}")
            red_hits.append(0)
        
        # 进度显示
        if (i + 1) % 100 == 0:
            current_red_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / (i + 1) * 100
            print(f"进度: {i+1}/{test_periods}, 当前红球: {current_red_avg:.2f}/6, 蓝球: {current_blue_rate:.1f}%")
    
    # 计算最终结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / test_periods * 100
    
    print(f"\n=== 遗传优化算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits}/{test_periods})")
    
    # 红球命中分布
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_hits.count(i)
        percentage = count / test_periods * 100
        print(f"  {i}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 10.0
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前15期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:15]):
            blue_symbol = "✓" if pred['blue_hit'] else "✗"
            print(f"{i+1:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_symbol}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 显示优化后的基因参数
    print(f"\n=== 优化后的基因参数 ===")
    for key, value in predictor.genes.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.3f}")
        else:
            print(f"  {key}: {value}")
    
    # 算法评估
    print(f"\n=== 遗传优化算法评估 ===")
    if avg_red_hits >= red_target and blue_hit_rate >= blue_target:
        print("🎉 恭喜！算法达到预期目标")
    elif avg_red_hits >= red_target * 0.85 and blue_hit_rate >= blue_target * 0.85:
        print("🔧 算法表现优秀，非常接近目标")
    elif avg_red_hits >= red_target * 0.7 and blue_hit_rate >= blue_target * 0.7:
        print("📈 算法表现良好，还有提升空间")
    else:
        print("⚠️ 算法需要进一步优化")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement,
        'genes': predictor.genes.copy()
    }

if __name__ == "__main__":
    # 遗传算法优化
    print("开始遗传算法优化...")
    best_predictor = genetic_algorithm_optimize(
        data_file='ssq_data.json',
        population_size=15,  # 减小种群以加快速度
        generations=5,       # 减少代数以加快速度
        test_periods=100     # 减少测试期数以加快速度
    )
    
    if best_predictor:
        # 使用优化后的预测器进行完整回测
        results = genetic_optimized_backtest(best_predictor, test_periods=500)
        
        if results:
            print(f"\n=== 遗传算法优化总结 ===")
            print(f"经过遗传算法优化后：")
            print(f"- 红球命中率提升至: {results['red_avg']:.2f}/6")
            print(f"- 蓝球命中率提升至: {results['blue_rate']:.1f}%")
            print(f"- 整体目标达成度: {(results['red_achievement'] + results['blue_achievement'])/2:.1f}%")
            
            if results['red_avg'] >= 1.3 or results['blue_rate'] >= 8.5:
                print("\n🚀 遗传算法优化效果显著！")
            else:
                print("\n🔄 建议增加进化代数或调整适应度函数")
    else:
        print("遗传算法优化失败，请检查数据文件")