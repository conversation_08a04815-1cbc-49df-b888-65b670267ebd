import json
import random
import numpy as np
from main import smart_get_ssq_data, predict_ssq_7red_1blue

# 设置不同的随机种子来测试预测变化
print("=== 测试预测结果变化 ===")

# 第一次预测（使用缺失数据）
print("\n1. 使用当前数据（缺失最新2期）进行预测：")
data1 = smart_get_ssq_data(1000)
print(f"数据量: {len(data1)}, 最新期号: {data1[0]['issueno']}")

# 不设置固定随机种子，让算法使用基于数据的动态种子
prediction1 = predict_ssq_7red_1blue()
print(f"预测结果1: 红球 {prediction1['red']}, 蓝球 {prediction1['blue']}")

# 第二次预测（数据会自动补全）
print("\n2. 再次预测（数据应该已补全）：")
data2 = smart_get_ssq_data(1000)
print(f"数据量: {len(data2)}, 最新期号: {data2[0]['issueno']}")

# 不设置固定随机种子，让算法使用基于数据的动态种子
prediction2 = predict_ssq_7red_1blue()
print(f"预测结果2: 红球 {prediction2['red']}, 蓝球 {prediction2['blue']}")

# 比较结果
if prediction1['red'] != prediction2['red'] or prediction1['blue'] != prediction2['blue']:
    print("\n✅ 预测结果发生了变化！")
else:
    print("\n⚠️ 预测结果没有变化")

print("\n=== 测试完成 ===")