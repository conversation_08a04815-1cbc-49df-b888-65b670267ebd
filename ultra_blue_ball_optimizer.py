#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级蓝球优化器
基于深度学习和量子启发算法的蓝球预测系统
目标：蓝球命中率≥15%

核心创新技术：
1. 量子启发的特征融合
2. 注意力机制的时序建模
3. 多尺度卷积神经网络
4. 强化学习策略优化
5. 集成学习与模型融合
6. 动态权重自适应调整
7. 反向工程预测验证
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter, deque
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class UltraBlueBallOptimizer:
    def __init__(self, data_file='ssq_data.json'):
        """初始化超级蓝球优化器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 多层次特征权重系统
        self.primary_weights = {
            'quantum_fusion': 0.25,     # 量子融合
            'attention_temporal': 0.20, # 注意力时序
            'cnn_multiscale': 0.18,     # 多尺度CNN
            'reinforcement': 0.15,      # 强化学习
            'ensemble_meta': 0.12,      # 元集成学习
            'adaptive_dynamic': 0.10    # 动态自适应
        }
        
        # 二级特征权重
        self.secondary_weights = {
            'frequency_enhanced': 0.15,
            'interval_quantum': 0.15,
            'trend_attention': 0.15,
            'pattern_deep': 0.15,
            'neural_advanced': 0.15,
            'reverse_smart': 0.10,
            'ensemble_voting': 0.10,
            'chaos_theory': 0.05
        }
        
        # 初始化高级神经网络
        self.init_advanced_networks()
        
        # 量子启发参数
        self.quantum_params = {
            'entanglement_strength': 0.7,
            'superposition_factor': 0.5,
            'decoherence_rate': 0.1
        }
        
        # 注意力机制参数
        self.attention_params = {
            'head_count': 8,
            'key_dim': 16,
            'value_dim': 16,
            'sequence_length': 20
        }
        
        # 强化学习参数
        self.rl_params = {
            'learning_rate': 0.01,
            'discount_factor': 0.95,
            'exploration_rate': 0.1,
            'q_table': defaultdict(lambda: defaultdict(float))
        }
        
        # 性能跟踪
        self.performance_history = deque(maxlen=1000)
        self.model_confidence = 0.5
        self.adaptation_rate = 0.02
        
    def load_data(self):
        """加载和预处理数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', ''),
                        'timestamp': self.parse_date(entry.get('date', ''))
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def parse_date(self, date_str):
        """解析日期字符串为时间戳"""
        try:
            if date_str:
                dt = datetime.strptime(date_str, '%Y-%m-%d')
                return dt.timestamp()
        except:
            pass
        return 0
    
    def init_advanced_networks(self):
        """初始化高级神经网络"""
        # 多尺度CNN网络 - 修复维度
        self.cnn_filters = {
            'scale_1': np.random.randn(3) * 0.1,
            'scale_2': np.random.randn(5) * 0.1,
            'scale_3': np.random.randn(7) * 0.1
        }
        
        # 注意力机制权重
        self.attention_weights = {
            'query': np.random.randn(16, 16) * 0.1,
            'key': np.random.randn(16, 16) * 0.1,
            'value': np.random.randn(16, 16) * 0.1,
            'output': np.random.randn(16, 1) * 0.1
        }
        
        # 深度神经网络（更深层次）
        layers = [20, 32, 24, 16, 8, 1]
        self.deep_weights = []
        self.deep_biases = []
        
        for i in range(len(layers) - 1):
            w = np.random.randn(layers[i], layers[i+1]) * np.sqrt(2.0 / layers[i])
            b = np.zeros(layers[i+1])
            self.deep_weights.append(w)
            self.deep_biases.append(b)
        
        # LSTM增强网络
        self.lstm_weights = {
            'forget_gate': np.random.randn(32, 16) * 0.1,
            'input_gate': np.random.randn(32, 16) * 0.1,
            'candidate_gate': np.random.randn(32, 16) * 0.1,
            'output_gate': np.random.randn(32, 16) * 0.1
        }
        
        self.lstm_state = {
            'hidden': np.zeros(16),
            'cell': np.zeros(16)
        }
    
    def quantum_feature_fusion(self, features):
        """量子启发的特征融合"""
        # 量子叠加态模拟
        superposition = np.array(features) * self.quantum_params['superposition_factor']
        
        # 量子纠缠模拟
        entanglement_matrix = np.random.randn(len(features), len(features)) * self.quantum_params['entanglement_strength']
        entangled_features = np.dot(superposition, entanglement_matrix)
        
        # 量子测量（坍缩）
        measurement_prob = np.abs(entangled_features) ** 2
        measurement_prob /= np.sum(measurement_prob)
        
        # 量子退相干
        decoherence = np.random.randn(len(features)) * self.quantum_params['decoherence_rate']
        final_features = measurement_prob + decoherence
        
        return final_features
    
    def multi_head_attention(self, sequence):
        """多头注意力机制"""
        if len(sequence) < self.attention_params['sequence_length']:
            # 填充序列
            padded_sequence = sequence + [0] * (self.attention_params['sequence_length'] - len(sequence))
        else:
            padded_sequence = sequence[-self.attention_params['sequence_length']:]
        
        # 将序列转换为合适的维度
        sequence_array = np.array(padded_sequence).reshape(self.attention_params['sequence_length'], 1)
        
        # 扩展到16维特征
        expanded_features = np.tile(sequence_array, (1, 16)) / 16.0
        
        # 计算Query, Key, Value
        query = np.dot(expanded_features, self.attention_weights['query'])
        key = np.dot(expanded_features, self.attention_weights['key'])
        value = np.dot(expanded_features, self.attention_weights['value'])
        
        # 计算注意力分数
        attention_scores = np.dot(query, key.T) / np.sqrt(self.attention_params['key_dim'])
        attention_weights = self.softmax(attention_scores)
        
        # 应用注意力
        attended_output = np.dot(attention_weights, value)
        
        # 输出投影 - 修复维度
        final_output = np.dot(attended_output.mean(axis=0, keepdims=True), self.attention_weights['output'])
        
        return float(final_output[0, 0])
    
    def multiscale_cnn_features(self, sequence):
        """多尺度卷积特征提取"""
        if len(sequence) < 7:
            sequence = sequence + [0] * (7 - len(sequence))
        
        sequence_array = np.array(sequence[-7:])
        features = []
        
        # 尺度1：3点卷积
        for i in range(len(sequence_array) - 2):
            conv_result = np.dot(sequence_array[i:i+3], self.cnn_filters['scale_1'])
            features.append(self.relu(conv_result))
        
        # 尺度2：5点卷积
        for i in range(len(sequence_array) - 4):
            conv_result = np.dot(sequence_array[i:i+5], self.cnn_filters['scale_2'])
            features.append(self.relu(conv_result))
        
        # 尺度3：7点卷积
        conv_result = np.dot(sequence_array, self.cnn_filters['scale_3'])
        features.append(self.relu(conv_result))
        
        return np.mean(features) if features else 0.0
    
    def reinforcement_learning_score(self, ball, state_features):
        """强化学习评分"""
        # 状态编码
        state = tuple(np.round(state_features, 2))
        
        # Q值查询
        q_value = self.rl_params['q_table'][state][ball]
        
        # ε-贪婪策略
        if random.random() < self.rl_params['exploration_rate']:
            return random.random()
        else:
            return q_value
    
    def update_q_table(self, state, action, reward, next_state):
        """更新Q表"""
        current_q = self.rl_params['q_table'][state][action]
        max_next_q = max(self.rl_params['q_table'][next_state].values()) if self.rl_params['q_table'][next_state] else 0
        
        new_q = current_q + self.rl_params['learning_rate'] * (
            reward + self.rl_params['discount_factor'] * max_next_q - current_q
        )
        
        self.rl_params['q_table'][state][action] = new_q
    
    def chaos_theory_analysis(self, sequence):
        """混沌理论分析"""
        if len(sequence) < 10:
            return 0.5
        
        # 计算李雅普诺夫指数
        lyapunov_sum = 0
        for i in range(1, len(sequence)):
            if sequence[i-1] != 0:
                lyapunov_sum += math.log(abs(sequence[i] / sequence[i-1]))
        
        lyapunov_exponent = lyapunov_sum / (len(sequence) - 1)
        
        # 混沌度量
        chaos_measure = 1 / (1 + math.exp(-lyapunov_exponent))
        
        return chaos_measure
    
    def deep_neural_prediction(self, features):
        """深度神经网络预测"""
        x = np.array(features)
        
        # 前向传播
        for i, (w, b) in enumerate(zip(self.deep_weights, self.deep_biases)):
            x = np.dot(x, w) + b
            if i < len(self.deep_weights) - 1:  # 不在最后一层使用激活函数
                x = self.leaky_relu(x)
            else:
                x = self.sigmoid(x)
        
        return float(x[0])
    
    def lstm_enhanced_prediction(self, sequence):
        """LSTM增强预测"""
        if len(sequence) < 5:
            return 0.5
        
        recent_sequence = sequence[-5:]
        
        for value in recent_sequence:
            # 归一化输入
            normalized_input = np.array([value / 16.0] * 16)
            combined_input = np.concatenate([normalized_input, self.lstm_state['hidden']])
            
            # 计算门控
            forget_gate = self.sigmoid(np.dot(combined_input, self.lstm_weights['forget_gate']))
            input_gate = self.sigmoid(np.dot(combined_input, self.lstm_weights['input_gate']))
            candidate_gate = self.tanh(np.dot(combined_input, self.lstm_weights['candidate_gate']))
            output_gate = self.sigmoid(np.dot(combined_input, self.lstm_weights['output_gate']))
            
            # 更新状态
            self.lstm_state['cell'] = forget_gate * self.lstm_state['cell'] + input_gate * candidate_gate
            self.lstm_state['hidden'] = output_gate * self.tanh(self.lstm_state['cell'])
        
        return np.mean(self.lstm_state['hidden'])
    
    def meta_ensemble_prediction(self, predictions):
        """元集成学习预测"""
        if not predictions:
            return random.randint(1, 16)
        
        # 加权投票
        weighted_votes = defaultdict(float)
        
        for i, pred in enumerate(predictions):
            weight = 1.0 / (i + 1)  # 递减权重
            weighted_votes[pred] += weight
        
        # 选择最高权重的预测
        best_prediction = max(weighted_votes.items(), key=lambda x: x[1])[0]
        
        return best_prediction
    
    def adaptive_weight_evolution(self, performance_feedback):
        """自适应权重进化"""
        if performance_feedback > 0.5:  # 预测正确
            # 增强当前权重配置
            for key in self.primary_weights:
                self.primary_weights[key] *= (1 + self.adaptation_rate)
        else:  # 预测错误
            # 随机扰动权重
            perturbation_key = random.choice(list(self.primary_weights.keys()))
            self.primary_weights[perturbation_key] *= (1 - self.adaptation_rate)
        
        # 归一化权重
        total_weight = sum(self.primary_weights.values())
        for key in self.primary_weights:
            self.primary_weights[key] /= total_weight
    
    def comprehensive_blue_analysis(self):
        """综合蓝球分析"""
        if not self.data:
            return self._default_analysis()
        
        blue_sequence = [entry['blue'] for entry in self.data]
        
        analysis = {
            'sequence': blue_sequence,
            'frequency': Counter(blue_sequence),
            'intervals': self._calculate_intervals(blue_sequence),
            'trends': self._calculate_trends(blue_sequence),
            'patterns': self._extract_patterns(blue_sequence),
            'chaos_measure': self.chaos_theory_analysis(blue_sequence),
            'attention_score': self.multi_head_attention(blue_sequence),
            'cnn_features': self.multiscale_cnn_features(blue_sequence),
            'lstm_prediction': self.lstm_enhanced_prediction(blue_sequence)
        }
        
        return analysis
    
    def _calculate_intervals(self, sequence):
        """计算间隔"""
        intervals = defaultdict(list)
        for ball in self.blue_range:
            last_pos = -1
            for i, blue in enumerate(sequence):
                if blue == ball:
                    if last_pos != -1:
                        intervals[ball].append(i - last_pos)
                    last_pos = i
        return intervals
    
    def _calculate_trends(self, sequence):
        """计算趋势"""
        trends = defaultdict(int)
        recent_sequence = sequence[-30:] if len(sequence) >= 30 else sequence
        for ball in self.blue_range:
            trends[ball] = recent_sequence.count(ball)
        return trends
    
    def _extract_patterns(self, sequence):
        """提取模式"""
        patterns = []
        for i in range(len(sequence) - 2):
            pattern = tuple(sequence[i:i+3])
            patterns.append(pattern)
        return patterns
    
    def _default_analysis(self):
        """默认分析"""
        return {
            'sequence': [],
            'frequency': Counter(),
            'intervals': defaultdict(list),
            'trends': defaultdict(int),
            'patterns': [],
            'chaos_measure': 0.5,
            'attention_score': 0.5,
            'cnn_features': 0.5,
            'lstm_prediction': 0.5
        }
    
    def calculate_ultra_scores(self, analysis):
        """计算超级评分"""
        ultra_scores = {}
        
        for ball in self.blue_range:
            # 基础特征
            freq_score = self._frequency_score(ball, analysis)
            interval_score = self._interval_score(ball, analysis)
            trend_score = self._trend_score(ball, analysis)
            pattern_score = self._pattern_score(ball, analysis)
            
            # 高级特征
            quantum_features = [freq_score, interval_score, trend_score, pattern_score]
            quantum_score = np.mean(self.quantum_feature_fusion(quantum_features))
            
            attention_score = analysis['attention_score'] * (ball / 16.0)
            cnn_score = analysis['cnn_features'] * math.sin(ball * math.pi / 16)
            
            # 强化学习评分
            state_features = [freq_score, interval_score, trend_score]
            rl_score = self.reinforcement_learning_score(ball, state_features)
            
            # 深度神经网络评分
            deep_features = [
                freq_score, interval_score, trend_score, pattern_score,
                quantum_score, attention_score, cnn_score, rl_score,
                ball / 16.0, math.sin(ball * math.pi / 16), math.cos(ball * math.pi / 16),
                analysis['chaos_measure'], analysis['lstm_prediction'],
                random.random() * 0.1, len(analysis['sequence']) / 1000.0,
                self.model_confidence, np.mean(list(analysis['frequency'].values())),
                ball % 2, ball % 3, ball % 4
            ]
            
            deep_score = self.deep_neural_prediction(deep_features)
            
            # 综合评分
            total_score = (
                quantum_score * self.primary_weights['quantum_fusion'] +
                attention_score * self.primary_weights['attention_temporal'] +
                cnn_score * self.primary_weights['cnn_multiscale'] +
                rl_score * self.primary_weights['reinforcement'] +
                deep_score * self.primary_weights['ensemble_meta'] +
                (freq_score + interval_score) * self.primary_weights['adaptive_dynamic']
            )
            
            ultra_scores[ball] = total_score
        
        return ultra_scores
    
    def _frequency_score(self, ball, analysis):
        """频率评分"""
        total_count = sum(analysis['frequency'].values())
        if total_count == 0:
            return 1.0 / 16
        
        freq = analysis['frequency'][ball]
        expected_freq = total_count / 16
        
        # 使用改进的评分函数
        score = math.exp(-0.5 * ((freq - expected_freq) / (expected_freq + 1)) ** 2)
        return score
    
    def _interval_score(self, ball, analysis):
        """间隔评分"""
        intervals = analysis['intervals'][ball]
        if not intervals:
            return 0.6
        
        avg_interval = sum(intervals) / len(intervals)
        current_interval = 0
        
        # 计算当前间隔
        for i in range(len(analysis['sequence']) - 1, -1, -1):
            if analysis['sequence'][i] == ball:
                break
            current_interval += 1
        
        # 改进的间隔评分
        if current_interval >= avg_interval * 0.8:
            score = min(1.0, current_interval / avg_interval)
        else:
            score = 0.4
        
        return score
    
    def _trend_score(self, ball, analysis):
        """趋势评分"""
        recent_count = analysis['trends'][ball]
        expected_count = 30 / 16
        
        if recent_count == 0:
            return 0.8
        elif recent_count >= expected_count * 2:
            return 0.3
        else:
            return 0.6
    
    def _pattern_score(self, ball, analysis):
        """模式评分"""
        if len(analysis['sequence']) < 3:
            return 0.5
        
        recent_pattern = tuple(analysis['sequence'][-2:])
        pattern_scores = defaultdict(int)
        
        for pattern in analysis['patterns']:
            if pattern[:2] == recent_pattern:
                pattern_scores[pattern[2]] += 1
        
        if pattern_scores:
            total_matches = sum(pattern_scores.values())
            return pattern_scores[ball] / total_matches
        else:
            return 1.0 / 16
    
    def ultra_predict_blue_ball(self):
        """超级蓝球预测"""
        if not self.data:
            return random.randint(1, 16)
        
        # 综合分析
        analysis = self.comprehensive_blue_analysis()
        
        # 计算超级评分
        ultra_scores = self.calculate_ultra_scores(analysis)
        
        # 多模型预测
        predictions = []
        
        # 模型1：直接评分排序
        sorted_scores = sorted(ultra_scores.items(), key=lambda x: x[1], reverse=True)
        predictions.append(sorted_scores[0][0])
        
        # 模型2：概率采样
        total_score = sum(ultra_scores.values())
        if total_score > 0:
            probs = [ultra_scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 模型3：温度采样
        temperature = 0.5
        exp_scores = [math.exp(ultra_scores[ball] / temperature) for ball in self.blue_range]
        temp_probs = [score / sum(exp_scores) for score in exp_scores]
        temp_ball = np.random.choice(self.blue_range, p=temp_probs)
        predictions.append(temp_ball)
        
        # 模型4：集成投票
        ensemble_prediction = self.meta_ensemble_prediction(predictions)
        
        return ensemble_prediction
    
    def update_model_performance(self, predicted, actual):
        """更新模型性能"""
        performance = 1.0 if predicted == actual else 0.0
        self.performance_history.append(performance)
        
        # 更新模型置信度
        if len(self.performance_history) >= 10:
            recent_performance = list(self.performance_history)[-10:]
            self.model_confidence = sum(recent_performance) / len(recent_performance)
        
        # 自适应权重调整
        self.adaptive_weight_evolution(performance)
        
        # 更新强化学习
        if len(self.performance_history) >= 2:
            state = tuple([predicted, actual])
            reward = 1.0 if predicted == actual else -0.1
            next_state = tuple([actual, 0])  # 简化的下一状态
            self.update_q_table(state, predicted, reward, next_state)
    
    def get_ultra_performance_report(self):
        """获取超级性能报告"""
        if not self.performance_history:
            return {'hit_rate': 0.0, 'confidence': 0.5, 'total_predictions': 0}
        
        hit_rate = sum(self.performance_history) / len(self.performance_history)
        
        return {
            'hit_rate': hit_rate,
            'confidence': self.model_confidence,
            'total_predictions': len(self.performance_history),
            'primary_weights': self.primary_weights.copy(),
            'secondary_weights': self.secondary_weights.copy(),
            'quantum_params': self.quantum_params.copy(),
            'recent_performance': list(self.performance_history)[-10:] if len(self.performance_history) >= 10 else list(self.performance_history)
        }
    
    # 激活函数
    def relu(self, x):
        return np.maximum(0, x)
    
    def leaky_relu(self, x, alpha=0.01):
        return np.where(x > 0, x, alpha * x)
    
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def tanh(self, x):
        return np.tanh(x)
    
    def softmax(self, x):
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)

def test_ultra_blue_optimizer():
    """测试超级蓝球优化器"""
    print("=== 超级蓝球优化器测试 ===")
    
    optimizer = UltraBlueBallOptimizer()
    
    if not optimizer.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(optimizer.data)}期")
    
    # 回测
    original_data = optimizer.data.copy()
    test_periods = min(150, len(original_data) - 10)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        optimizer.data = test_data
        
        # 预测蓝球
        predicted_blue = optimizer.ultra_predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        optimizer.update_model_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i % 10 == 0 or i < 10:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    optimizer.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 15%")
    print(f"达成度: {hit_rate/0.15*100:.1f}%")
    
    # 性能报告
    report = optimizer.get_ultra_performance_report()
    print(f"\n=== 超级性能报告 ===")
    print(f"模型置信度: {report['confidence']:.3f}")
    print(f"总预测次数: {report['total_predictions']}")
    print(f"最终命中率: {report['hit_rate']:.2%}")
    
    print(f"\n主要权重配置:")
    for key, value in report['primary_weights'].items():
        print(f"  {key}: {value:.3f}")
    
    # 生成新预测
    print(f"\n=== 最新超级预测 ===")
    new_prediction = optimizer.ultra_predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    return optimizer

if __name__ == "__main__":
    test_ultra_blue_optimizer()