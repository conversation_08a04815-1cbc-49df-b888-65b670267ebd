import requests
import json
import time

# 使用之前成功的API调用方式
url = 'https://api.jisuapi.com/caipiao/history'
params = {
    'appkey': 'eb6cc4f9bf4a23b4',
    'caipiaoid': '12',
    'num': '1000'  # 尝试直接获取1000期
}

print("正在获取福彩3D历史数据...")
response = requests.get(url, params=params)
print('API返回状态:', response.status_code)

if response.status_code == 200:
    data = response.json()
    print('API响应:', json.dumps(data, ensure_ascii=False, indent=2)[:500] + '...')
    
    if 'result' in data and data['result']:
        result = data['result']
        
        # 检查数据结构
        if isinstance(result, dict):
            if 'list' in result:
                lottery_data = result['list']
                total_count = result.get('total', len(lottery_data))
            else:
                lottery_data = [result]  # 单条数据
                total_count = 1
        elif isinstance(result, list):
            lottery_data = result
            total_count = len(lottery_data)
        else:
            print("未知的数据格式")
            lottery_data = []
            total_count = 0
        
        print(f'总数据量: {total_count}')
        print(f'实际返回条数: {len(lottery_data)}')
        
        if lottery_data:
            # 保存数据到文件
            with open('fc3d_data_1000.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'total_count': total_count,
                    'actual_count': len(lottery_data),
                    'data': lottery_data
                }, f, ensure_ascii=False, indent=2)
            
            print(f'\n已保存{len(lottery_data)}期数据到 fc3d_data_1000.json')
            
            # 显示最新几期数据
            print('\n最新10期数据:')
            for i, item in enumerate(lottery_data[:10]):
                if isinstance(item, dict):
                    issue = item.get('issueno', '未知')
                    date = item.get('opendate', '未知')
                    number = item.get('number', '未知')
                    print(f'{i+1:2d}. 期号:{issue} 日期:{date} 号码:{number}')
                else:
                    print(f'{i+1:2d}. 数据格式异常: {item}')
        else:
            print('未获取到有效数据')
    else:
        print('API返回数据中没有result字段或result为空')
else:
    print('API请求失败:', response.text)