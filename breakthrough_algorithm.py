#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
突破性双色球预测算法
目标：红球命中率至少3/6，蓝球命中率至少31.25%
策略：采用激进的多维度集成和动态权重调整
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations

class BreakthroughPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = list(range(1, 34))
        self.blue_balls = list(range(1, 17))
        
        # 多维度分析缓存
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_position_freq = defaultdict(lambda: defaultdict(int))
        self.red_pairs = defaultdict(int)
        self.red_triplets = defaultdict(int)
        self.red_intervals = defaultdict(list)
        self.blue_intervals = []
        
        # 动态权重系统
        self.weights = {
            'frequency': 0.25,
            'position': 0.20,
            'pairs': 0.15,
            'intervals': 0.15,
            'hot_cold': 0.10,
            'trend': 0.10,
            'distribution': 0.05
        }
        
    def load_data(self, filename):
        """加载数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print(f"加载了 {len(self.data)} 期数据")
        
    def analyze_comprehensive(self, recent_count=100):
        """全面分析最近数据"""
        recent_data = self.data[-recent_count:] if len(self.data) > recent_count else self.data
        
        # 重置统计
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_position_freq.clear()
        self.red_pairs.clear()
        self.red_triplets.clear()
        self.red_intervals.clear()
        self.blue_intervals.clear()
        
        for i, draw in enumerate(recent_data):
            red_nums = list(map(int, draw['number'].split()))
            blue_num = int(draw['refernumber'])
            
            # 频率统计
            for num in red_nums:
                self.red_frequency[num] += 1
            self.blue_frequency[blue_num] += 1
            
            # 位置频率
            for pos, num in enumerate(red_nums):
                self.red_position_freq[pos][num] += 1
            
            # 配对分析
            for pair in combinations(red_nums, 2):
                self.red_pairs[tuple(sorted(pair))] += 1
            
            # 三元组分析
            for triplet in combinations(red_nums, 3):
                self.red_triplets[tuple(sorted(triplet))] += 1
            
            # 间隔分析
            for num in red_nums:
                self.red_intervals[num].append(i)
            self.blue_intervals.append((i, blue_num))
    
    def calculate_red_scores(self):
        """计算红球综合得分"""
        scores = defaultdict(float)
        
        # 频率得分（激进权重）
        max_freq = max(self.red_frequency.values()) if self.red_frequency else 1
        for num in self.red_balls:
            freq_score = (self.red_frequency[num] / max_freq) * 100
            scores[num] += freq_score * self.weights['frequency']
        
        # 位置得分（激进策略）
        for pos in range(6):
            pos_freq = self.red_position_freq[pos]
            if pos_freq:
                max_pos_freq = max(pos_freq.values())
                for num in self.red_balls:
                    pos_score = (pos_freq[num] / max_pos_freq) * 100
                    scores[num] += pos_score * self.weights['position'] / 6
        
        # 配对得分（激进策略）
        pair_scores = defaultdict(float)
        for pair, count in self.red_pairs.items():
            for num in pair:
                pair_scores[num] += count
        
        if pair_scores:
            max_pair = max(pair_scores.values())
            for num in self.red_balls:
                scores[num] += (pair_scores[num] / max_pair) * 100 * self.weights['pairs']
        
        # 间隔得分（激进策略）
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if len(intervals) >= 2:
                avg_interval = sum(intervals[i] - intervals[i-1] for i in range(1, len(intervals))) / (len(intervals) - 1)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 激进的间隔评分
                if last_interval <= avg_interval * 0.8:  # 即将出现
                    interval_score = 150
                elif last_interval <= avg_interval * 1.2:  # 正常范围
                    interval_score = 100
                else:  # 超期
                    interval_score = 50
                
                scores[num] += interval_score * self.weights['intervals']
        
        # 热冷号得分（激进策略）
        recent_10 = self.data[-10:] if len(self.data) >= 10 else self.data
        hot_nums = set()
        for draw in recent_10:
            hot_nums.update(map(int, draw['number'].split()))
        
        for num in self.red_balls:
            if num in hot_nums:
                scores[num] += 120 * self.weights['hot_cold']  # 激进的热号权重
            else:
                scores[num] += 80 * self.weights['hot_cold']   # 冷号也给一定权重
        
        # 趋势得分（激进策略）
        recent_20 = self.data[-20:] if len(self.data) >= 20 else self.data
        trend_freq = defaultdict(int)
        for draw in recent_20:
            for num in map(int, draw['number'].split()):
                trend_freq[num] += 1
        
        if trend_freq:
            max_trend = max(trend_freq.values())
            for num in self.red_balls:
                trend_score = (trend_freq[num] / max_trend) * 120  # 激进的趋势权重
                scores[num] += trend_score * self.weights['trend']
        
        # 分布得分（激进策略）
        for num in self.red_balls:
            # 根据号码分布给予激进权重
            if 1 <= num <= 11:  # 小号区间
                scores[num] += 110 * self.weights['distribution']
            elif 12 <= num <= 22:  # 中号区间
                scores[num] += 100 * self.weights['distribution']
            else:  # 大号区间
                scores[num] += 105 * self.weights['distribution']
        
        return scores
    
    def calculate_blue_scores(self):
        """计算蓝球综合得分"""
        scores = defaultdict(float)
        
        # 频率得分（激进权重）
        max_freq = max(self.blue_frequency.values()) if self.blue_frequency else 1
        for num in self.blue_balls:
            freq_score = (self.blue_frequency[num] / max_freq) * 100
            scores[num] += freq_score * 0.4  # 激进的频率权重
        
        # 间隔得分（激进策略）
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 2:
                avg_interval = sum(intervals[i] - intervals[i-1] for i in range(1, len(intervals))) / (len(intervals) - 1)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 激进的间隔评分
                if last_interval <= avg_interval * 0.7:  # 即将出现
                    interval_score = 200
                elif last_interval <= avg_interval * 1.3:  # 正常范围
                    interval_score = 150
                else:  # 超期
                    interval_score = 100
                
                scores[num] += interval_score * 0.3
        
        # 热度得分（激进策略）
        recent_15 = self.data[-15:] if len(self.data) >= 15 else self.data
        hot_blues = [int(draw['refernumber']) for draw in recent_15]
        hot_count = Counter(hot_blues)
        
        max_hot = max(hot_count.values()) if hot_count else 1
        for num in self.blue_balls:
            hot_score = (hot_count[num] / max_hot) * 150  # 激进的热度权重
            scores[num] += hot_score * 0.3
        
        return scores
    
    def select_red_balls_aggressive(self, scores):
        """激进的红球选择策略"""
        # 多种策略组合
        strategies = []
        
        # 策略1：纯得分排序（激进）
        sorted_by_score = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        strategies.append([num for num, _ in sorted_by_score[:8]])  # 取前8个
        
        # 策略2：分区平衡（激进）
        small_nums = [(num, score) for num, score in scores.items() if 1 <= num <= 11]
        mid_nums = [(num, score) for num, score in scores.items() if 12 <= num <= 22]
        big_nums = [(num, score) for num, score in scores.items() if 23 <= num <= 33]
        
        small_nums.sort(key=lambda x: x[1], reverse=True)
        mid_nums.sort(key=lambda x: x[1], reverse=True)
        big_nums.sort(key=lambda x: x[1], reverse=True)
        
        balanced = []
        balanced.extend([num for num, _ in small_nums[:3]])
        balanced.extend([num for num, _ in mid_nums[:3]])
        balanced.extend([num for num, _ in big_nums[:2]])
        strategies.append(balanced)
        
        # 策略3：奇偶平衡（激进）
        odd_nums = [(num, score) for num, score in scores.items() if num % 2 == 1]
        even_nums = [(num, score) for num, score in scores.items() if num % 2 == 0]
        
        odd_nums.sort(key=lambda x: x[1], reverse=True)
        even_nums.sort(key=lambda x: x[1], reverse=True)
        
        odd_even = []
        odd_even.extend([num for num, _ in odd_nums[:4]])
        odd_even.extend([num for num, _ in even_nums[:4]])
        strategies.append(odd_even)
        
        # 策略4：高频+间隔组合（激进）
        high_freq = sorted(self.red_frequency.items(), key=lambda x: x[1], reverse=True)
        interval_candidates = []
        
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if intervals:
                last_interval = len(self.data) - intervals[-1]
                if len(intervals) >= 2:
                    avg_interval = sum(intervals[i] - intervals[i-1] for i in range(1, len(intervals))) / (len(intervals) - 1)
                    if last_interval >= avg_interval * 0.8:  # 即将或应该出现
                        interval_candidates.append((num, scores[num]))
        
        interval_candidates.sort(key=lambda x: x[1], reverse=True)
        
        combo = []
        combo.extend([num for num, _ in high_freq[:4]])
        combo.extend([num for num, _ in interval_candidates[:4]])
        strategies.append(combo)
        
        # 集成所有策略
        all_candidates = set()
        for strategy in strategies:
            all_candidates.update(strategy)
        
        # 重新评分并选择最终6个
        final_scores = {num: scores[num] for num in all_candidates}
        
        # 添加随机性以增加突破可能
        for num in final_scores:
            final_scores[num] += random.uniform(-10, 20)  # 激进的随机调整
        
        final_selection = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)[:6]
        return sorted([num for num, _ in final_selection])
    
    def select_blue_ball_aggressive(self, scores):
        """激进的蓝球选择策略"""
        # 多策略集成
        candidates = []
        
        # 策略1：得分最高的前3个
        top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        candidates.extend([num for num, _ in top_scores])
        
        # 策略2：频率最高的前2个
        top_freq = sorted(self.blue_frequency.items(), key=lambda x: x[1], reverse=True)[:2]
        candidates.extend([num for num, _ in top_freq])
        
        # 策略3：间隔分析的前2个
        interval_candidates = []
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 2:
                avg_interval = sum(intervals[i] - intervals[i-1] for i in range(1, len(intervals))) / (len(intervals) - 1)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                if last_interval >= avg_interval * 0.8:  # 应该出现
                    interval_candidates.append((num, scores[num]))
        
        interval_candidates.sort(key=lambda x: x[1], reverse=True)
        candidates.extend([num for num, _ in interval_candidates[:2]])
        
        # 去重并评分
        unique_candidates = list(set(candidates))
        if not unique_candidates:
            unique_candidates = list(range(1, 17))
        
        # 添加激进的随机性
        final_scores = {num: scores.get(num, 0) + random.uniform(-20, 30) for num in unique_candidates}
        
        # 选择得分最高的
        best_blue = max(final_scores.items(), key=lambda x: x[1])[0]
        return best_blue
    
    def predict(self, train_size=400):
        """激进预测"""
        if len(self.data) < train_size:
            train_size = len(self.data) - 1
        
        # 使用最近的数据进行分析
        self.analyze_comprehensive(train_size)
        
        # 计算得分
        red_scores = self.calculate_red_scores()
        blue_scores = self.calculate_blue_scores()
        
        # 激进选择
        red_prediction = self.select_red_balls_aggressive(red_scores)
        blue_prediction = self.select_blue_ball_aggressive(blue_scores)
        
        return red_prediction, blue_prediction

def breakthrough_backtest(filename, test_periods=500):
    """突破性回测"""
    predictor = BreakthroughPredictor()
    predictor.load_data(filename)
    
    if len(predictor.data) < test_periods + 50:
        test_periods = len(predictor.data) - 50
        print(f"调整测试期数为: {test_periods}")
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    best_predictions = []
    
    print(f"开始突破性回测，测试 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_data = predictor.data[:-(test_periods-i)]
        test_data = predictor.data[-(test_periods-i)]
        
        # 临时设置训练数据
        original_data = predictor.data
        predictor.data = train_data
        
        try:
            # 进行预测
            red_pred, blue_pred = predictor.predict()
            
            # 获取实际结果
            actual_red = list(map(int, test_data['number'].split()))
            actual_blue = int(test_data['refernumber'])
            
            # 计算命中
            red_hits = len(set(red_pred) & set(actual_red))
            blue_hit = 1 if blue_pred == actual_blue else 0
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            result = {
                'period': test_data['issueno'],
                'red_hits': red_hits,
                'blue_hit': blue_hit,
                'red_pred': red_pred,
                'red_actual': actual_red,
                'blue_pred': blue_pred,
                'blue_actual': actual_blue
            }
            results.append(result)
            
            # 记录优秀预测
            if red_hits >= 3 or blue_hit == 1:
                best_predictions.append(result)
            
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1}/{test_periods} 期")
                
        except Exception as e:
            print(f"第 {i+1} 期预测失败: {e}")
            continue
        finally:
            # 恢复原始数据
            predictor.data = original_data
    
    # 统计结果
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = (blue_hits_total / test_periods) * 100
    
    # 红球命中分布
    red_hit_dist = Counter([r['red_hits'] for r in results])
    red_3plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 3) / test_periods * 100
    
    print("\n=== 突破性算法回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits_total}/{test_periods})")
    print(f"红球命中>=3个的概率: {red_3plus_rate:.1f}%")
    
    print("\n=== 红球命中分布 ===")
    for hits in sorted(red_hit_dist.keys()):
        count = red_hit_dist[hits]
        percentage = count / test_periods * 100
        print(f"命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    print(f"\n=== 前20期最佳预测记录 ===")
    best_predictions.sort(key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
    
    for i, pred in enumerate(best_predictions[:20], 1):
        red_mark = f"{pred['red_hits']}/6"
        blue_mark = "✓" if pred['blue_hit'] else "✗"
        print(f"{i:2d}. 期号:{pred['period']} 红球:{red_mark} 蓝球:{blue_mark}")
        print(f"    预测红球: {pred['red_pred']}")
        print(f"    实际红球: {pred['red_actual']}")
        print(f"    预测蓝球: {pred['blue_pred']}, 实际蓝球: {pred['blue_actual']}")
        print()
    
    # 综合评估
    target_red_rate = 3/6 * 100  # 50%
    target_blue_rate = 5/16 * 100  # 31.25%
    
    red_achievement = (avg_red_hits/6*100) / target_red_rate * 100
    blue_achievement = blue_hit_rate / target_blue_rate * 100
    
    print("=== 目标达成情况 ===")
    print(f"红球目标: 3/6 (50%), 实际: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%), 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: 5/16 (31.25%), 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    if avg_red_hits >= 3 and blue_hit_rate >= 31.25:
        print("\n🎉 恭喜！已达到目标要求！")
    else:
        print("\n⚠️  未达到目标，需要进一步优化算法")
        print("\n=== 改进建议 ===")
        if avg_red_hits < 3:
            print("- 红球命中率需要提升，建议增强特征工程和权重优化")
        if blue_hit_rate < 31.25:
            print("- 蓝球命中率需要提升，建议改进间隔分析和趋势判断")
        print("- 考虑引入机器学习模型")
        print("- 增加更多历史数据分析")
        print("- 优化激进策略的参数")

if __name__ == "__main__":
    breakthrough_backtest('ssq_data.json', 500)