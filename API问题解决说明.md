# 福彩3D API问题解决说明

## 问题描述

用户反映福彩3D预测程序出现"API错误: 错误的请求KEY"，而双色球程序获取数据正常。

## 问题原因

经过分析发现，问题的根本原因是**使用了不同的API服务商**：

### 原始配置
- **双色球程序**: 使用极速数据API (`api.jisuapi.com`)
- **福彩3D程序**: 使用聚合数据API (`apis.juhe.cn`)

### API服务商对比

| 项目 | 极速数据 | 聚合数据 |
|------|----------|----------|
| 域名 | api.jisuapi.com | apis.juhe.cn |
| 双色球彩种ID | 11 | ssq |
| 福彩3D彩种ID | 35 | fc3d |
| API密钥 | 通用 | 需要单独申请 |
| 数据格式 | 统一格式 | 不同格式 |

## 解决方案

### 方案一：统一使用极速数据API（已实施）

将福彩3D程序修改为使用与双色球相同的极速数据API：

```python
# 修改前（聚合数据API）
url = "http://apis.juhe.cn/lottery/history"
params = {
    'lottery_id': 'fc3d',
    'page_size': count,
    'page': 1,
    'key': APPKEY
}

# 修改后（极速数据API）
url = f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=35&num={min(count, 20)}&start=0'
```

### 方案二：申请聚合数据API密钥（备选）

如果需要使用聚合数据API，需要：
1. 访问 https://www.juhe.cn/
2. 注册账号并申请彩票API
3. 获取专用的API密钥
4. 替换程序中的APPKEY

## 修改内容

### 1. 高级版预测器 (`fc3d_advanced_predictor.py`)
- 修改API调用URL
- 调整数据格式转换
- 统一错误处理

### 2. 基础版预测器 (`fc3d_predictor.py`)
- 同步修改API调用
- 保持数据结构一致

## 测试结果

修改后的程序测试结果：
- ✅ 成功获取20期真实历史数据
- ✅ 预测功能正常运行
- ✅ 生成8个预测组合
- ✅ 提供详细的投注建议

## 技术细节

### API参数对照

| 功能 | 极速数据API | 聚合数据API |
|------|-------------|-------------|
| 福彩3D彩种ID | 35 | fc3d |
| 请求数量参数 | num | page_size |
| 状态码字段 | status | error_code |
| 错误信息字段 | msg | reason |
| 数据列表路径 | result.list | result |

### 数据格式转换

程序自动将极速数据API的响应格式转换为原有的数据结构：

```python
lottery_data.append({
    'lottery_id': 'fc3d',
    'lottery_name': '福彩3D',
    'lottery_no': item.get('issueno', ''),
    'lottery_date': item.get('opendate', ''),
    'lottery_res': item.get('number', ''),
    'lottery_sale_amount': item.get('saleamount', ''),
    'lottery_pool_amount': item.get('totalmoney', '')
})
```

## 优势

1. **统一管理**: 所有彩票程序使用同一个API密钥
2. **降低成本**: 无需申请多个API服务
3. **简化维护**: 统一的错误处理和数据格式
4. **提高稳定性**: 使用已验证可用的API服务

## 注意事项

1. **API限制**: 极速数据API单次最多返回20条数据
2. **请求频率**: 注意控制API调用频率，避免超限
3. **数据延迟**: 开奖结果可能有一定延迟
4. **密钥安全**: 妥善保管API密钥，避免泄露

## 总结

通过统一使用极速数据API，成功解决了福彩3D程序的API错误问题。现在双色球和福彩3D程序都能正常获取数据并进行预测分析。

---

**更新时间**: 2024-12-19  
**状态**: 已解决 ✅