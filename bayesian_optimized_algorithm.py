#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝叶斯优化的双色球预测算法
使用贝叶斯优化自动调优参数，结合深度特征工程
目标：红球≥1.5/6，蓝球≥10%
"""

import json
import numpy as np
import random
from collections import defaultdict, Counter
from datetime import datetime
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class BayesianOptimizedPredictor:
    """贝叶斯优化的预测器"""
    
    def __init__(self):
        self.data = []
        # 优化参数空间
        self.params = {
            # 基础权重
            'frequency_weight': 0.25,
            'markov_weight': 0.20,
            'pattern_weight': 0.15,
            'trend_weight': 0.15,
            'balance_weight': 0.10,
            'repeat_weight': 0.15,
            
            # 高级特征权重
            'correlation_weight': 0.10,
            'entropy_weight': 0.08,
            'momentum_weight': 0.12,
            'volatility_weight': 0.05,
            
            # 窗口参数
            'frequency_window': 30,
            'markov_window': 50,
            'pattern_window': 10,
            'trend_window': 15,
            'correlation_window': 20,
            
            # 选择参数
            'red_selection_factor': 1.2,
            'blue_selection_factor': 1.5,
            'diversity_factor': 0.3,
            'stability_factor': 0.4,
            
            # 高级参数
            'zone_balance_factor': 0.3,
            'odd_even_factor': 0.2,
            'sum_range_factor': 0.25,
            'gap_analysis_factor': 0.15,
            'cycle_factor': 0.2,
        }
        
        # 特征缓存
        self.feature_cache = {}
        
    def load_data(self, file_path: str):
        """加载历史数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 期历史数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def parse_numbers(self, number_str: str) -> List[int]:
        """解析号码字符串"""
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def frequency_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """频率分析"""
        window_size = int(self.params['frequency_window'])
        data_window = recent_data[-window_size:] if len(recent_data) > window_size else recent_data
        
        red_freq = [0] * 33
        blue_freq = [0] * 16
        
        # 时间衰减权重
        for i, record in enumerate(data_window):
            weight = (i + 1) / len(data_window)  # 线性衰减
            
            red_nums = self.parse_numbers(record['number'])
            blue_num = int(record['refernumber'])
            
            for num in red_nums:
                if 1 <= num <= 33:
                    red_freq[num-1] += weight
            
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += weight
        
        # 归一化
        total_red = sum(red_freq)
        total_blue = sum(blue_freq)
        
        if total_red > 0:
            red_freq = [f / total_red for f in red_freq]
        if total_blue > 0:
            blue_freq = [f / total_blue for f in blue_freq]
        
        return red_freq, blue_freq
    
    def markov_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """增强马尔科夫链分析"""
        window_size = int(self.params['markov_window'])
        if len(recent_data) < 2:
            return [1/33] * 33, [1/16] * 16
        
        data_window = recent_data[-window_size:] if len(recent_data) > window_size else recent_data
        
        # 多阶马尔科夫链
        red_transitions_1 = defaultdict(lambda: defaultdict(float))
        red_transitions_2 = defaultdict(lambda: defaultdict(float))
        blue_transitions = defaultdict(float)
        
        for i in range(len(data_window) - 1):
            current_red = set(self.parse_numbers(data_window[i]['number']))
            next_red = set(self.parse_numbers(data_window[i+1]['number']))
            current_blue = int(data_window[i]['refernumber'])
            next_blue = int(data_window[i+1]['refernumber'])
            
            # 时间权重
            time_weight = (i + 1) / len(data_window)
            
            # 一阶转移
            for curr_num in current_red:
                for next_num in next_red:
                    red_transitions_1[curr_num][next_num] += time_weight
            
            # 二阶转移（如果有足够数据）
            if i < len(data_window) - 2:
                next_next_red = set(self.parse_numbers(data_window[i+2]['number']))
                for curr_num in current_red:
                    for next_next_num in next_next_red:
                        red_transitions_2[curr_num][next_next_num] += time_weight * 0.5
            
            # 蓝球转移
            blue_transitions[next_blue] += time_weight
        
        # 计算预测概率
        last_red = set(self.parse_numbers(recent_data[-1]['number']))
        
        red_probs = [0.0] * 33
        for num in range(1, 34):
            prob_1 = 0.0
            prob_2 = 0.0
            count_1 = 0
            count_2 = 0
            
            # 一阶概率
            for last_num in last_red:
                if last_num in red_transitions_1:
                    total = sum(red_transitions_1[last_num].values())
                    if total > 0:
                        prob_1 += red_transitions_1[last_num].get(num, 0) / total
                        count_1 += 1
            
            # 二阶概率
            for last_num in last_red:
                if last_num in red_transitions_2:
                    total = sum(red_transitions_2[last_num].values())
                    if total > 0:
                        prob_2 += red_transitions_2[last_num].get(num, 0) / total
                        count_2 += 1
            
            # 组合概率
            if count_1 > 0:
                prob_1 /= count_1
            else:
                prob_1 = 1/33
            
            if count_2 > 0:
                prob_2 /= count_2
            else:
                prob_2 = 1/33
            
            # 加权组合
            red_probs[num-1] = prob_1 * 0.7 + prob_2 * 0.3
        
        # 蓝球概率
        blue_probs = [0.0] * 16
        total_blue_trans = sum(blue_transitions.values())
        
        for num in range(1, 17):
            if total_blue_trans > 0:
                blue_probs[num-1] = blue_transitions.get(num, 0) / total_blue_trans
            else:
                blue_probs[num-1] = 1/16
        
        return red_probs, blue_probs
    
    def correlation_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """相关性分析"""
        window_size = int(self.params['correlation_window'])
        if len(recent_data) < window_size:
            return [1/33] * 33, [1/16] * 16
        
        data_window = recent_data[-window_size:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 号码共现分析
        co_occurrence = defaultdict(lambda: defaultdict(int))
        
        for record in data_window:
            red_nums = self.parse_numbers(record['number'])
            for i, num1 in enumerate(red_nums):
                for j, num2 in enumerate(red_nums):
                    if i != j:
                        co_occurrence[num1][num2] += 1
        
        # 基于最近一期的号码计算相关性
        last_red = self.parse_numbers(recent_data[-1]['number'])
        
        for num in range(1, 34):
            correlation_score = 0.0
            for last_num in last_red:
                if last_num in co_occurrence:
                    total_cooccur = sum(co_occurrence[last_num].values())
                    if total_cooccur > 0:
                        correlation_score += co_occurrence[last_num].get(num, 0) / total_cooccur
            
            red_scores[num-1] = correlation_score / len(last_red) if last_red else 0
        
        # 蓝球相关性（简化处理）
        blue_freq = [0] * 16
        for record in data_window:
            blue_num = int(record['refernumber'])
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        total_blue = sum(blue_freq)
        if total_blue > 0:
            blue_scores = [f / total_blue for f in blue_freq]
        else:
            blue_scores = [1/16] * 16
        
        return red_scores, blue_scores
    
    def entropy_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """熵分析"""
        if len(recent_data) < 10:
            return [1/33] * 33, [1/16] * 16
        
        recent_10 = recent_data[-10:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 计算每个号码的信息熵
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_10):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 2:
                # 计算间隔的熵
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                if intervals:
                    # 简化的熵计算
                    avg_interval = sum(intervals) / len(intervals)
                    variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                    entropy = np.log(variance + 1)  # 避免log(0)
                    red_scores[num-1] = 1 / (entropy + 1)  # 熵越小，规律性越强
            else:
                red_scores[num-1] = 0.5  # 中等分数
        
        # 蓝球熵分析
        blue_appearances = {}
        for i, record in enumerate(recent_10):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_appearances:
                blue_appearances[blue_num] = []
            blue_appearances[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_appearances and len(blue_appearances[num]) >= 1:
                # 简化处理
                last_pos = blue_appearances[num][-1]
                blue_scores[num-1] = (10 - last_pos) / 10
            else:
                blue_scores[num-1] = 0.5
        
        return red_scores, blue_scores
    
    def momentum_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """动量分析"""
        if len(recent_data) < 6:
            return [1/33] * 33, [1/16] * 16
        
        recent_6 = recent_data[-6:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 计算号码的动量（最近出现频率的变化趋势）
        for num in range(1, 34):
            recent_3_count = 0
            earlier_3_count = 0
            
            # 最近3期
            for record in recent_6[-3:]:
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    recent_3_count += 1
            
            # 之前3期
            for record in recent_6[:3]:
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    earlier_3_count += 1
            
            # 动量 = 最近频率 - 之前频率
            momentum = (recent_3_count - earlier_3_count) / 3
            red_scores[num-1] = max(0, momentum + 0.5)  # 归一化到[0,1]
        
        # 蓝球动量
        recent_3_blues = [int(record['refernumber']) for record in recent_6[-3:]]
        earlier_3_blues = [int(record['refernumber']) for record in recent_6[:3]]
        
        for num in range(1, 17):
            recent_count = recent_3_blues.count(num)
            earlier_count = earlier_3_blues.count(num)
            momentum = (recent_count - earlier_count) / 3
            blue_scores[num-1] = max(0, momentum + 0.5)
        
        return red_scores, blue_scores
    
    def volatility_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """波动性分析"""
        if len(recent_data) < 15:
            return [1/33] * 33, [1/16] * 16
        
        recent_15 = recent_data[-15:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 计算每个号码出现位置的波动性
        for num in range(1, 34):
            positions = []
            for i, record in enumerate(recent_15):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    positions.append(i)
            
            if len(positions) >= 3:
                # 计算位置间隔的标准差
                intervals = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                    std_dev = np.sqrt(variance)
                    # 波动性越小，预测性越强
                    red_scores[num-1] = 1 / (std_dev + 1)
            else:
                red_scores[num-1] = 0.3
        
        # 蓝球波动性（简化）
        blue_positions = {}
        for i, record in enumerate(recent_15):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_positions:
                blue_positions[blue_num] = []
            blue_positions[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_positions and len(blue_positions[num]) >= 2:
                positions = blue_positions[num]
                intervals = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                    std_dev = np.sqrt(variance)
                    blue_scores[num-1] = 1 / (std_dev + 1)
            else:
                blue_scores[num-1] = 0.3
        
        return red_scores, blue_scores
    
    def gap_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """间隔分析"""
        if len(recent_data) < 20:
            return [1/33] * 33, [1/16] * 16
        
        recent_20 = recent_data[-20:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 分析每个号码的间隔模式
        for num in range(1, 34):
            last_appearance = -1
            gaps = []
            
            for i, record in enumerate(recent_20):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    if last_appearance >= 0:
                        gap = i - last_appearance
                        gaps.append(gap)
                    last_appearance = i
            
            if gaps:
                avg_gap = sum(gaps) / len(gaps)
                current_gap = len(recent_20) - 1 - last_appearance if last_appearance >= 0 else len(recent_20)
                
                # 如果当前间隔接近平均间隔，给予高分
                if avg_gap > 0:
                    score = 1 - abs(current_gap - avg_gap) / max(avg_gap, current_gap)
                    red_scores[num-1] = max(0, score)
            else:
                # 从未出现，给予中等分数
                red_scores[num-1] = 0.4
        
        # 蓝球间隔分析
        blue_gaps = {}
        for num in range(1, 17):
            last_appearance = -1
            gaps = []
            
            for i, record in enumerate(recent_20):
                blue_num = int(record['refernumber'])
                if blue_num == num:
                    if last_appearance >= 0:
                        gap = i - last_appearance
                        gaps.append(gap)
                    last_appearance = i
            
            if gaps:
                avg_gap = sum(gaps) / len(gaps)
                current_gap = len(recent_20) - 1 - last_appearance if last_appearance >= 0 else len(recent_20)
                
                if avg_gap > 0:
                    score = 1 - abs(current_gap - avg_gap) / max(avg_gap, current_gap)
                    blue_scores[num-1] = max(0, score)
            else:
                blue_scores[num-1] = 0.4
        
        return red_scores, blue_scores
    
    def cycle_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """周期分析"""
        if len(recent_data) < 30:
            return [1/33] * 33, [1/16] * 16
        
        recent_30 = recent_data[-30:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 寻找周期性模式
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_30):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 3:
                # 检测可能的周期
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                
                # 寻找最常见的间隔
                interval_counts = Counter(intervals)
                if interval_counts:
                    most_common_interval = interval_counts.most_common(1)[0][0]
                    last_appearance = appearances[-1]
                    
                    # 预测下次出现
                    predicted_next = last_appearance + most_common_interval
                    current_position = len(recent_30) - 1
                    
                    # 如果接近预测位置，给予高分
                    distance = abs(current_position - predicted_next)
                    score = max(0, 1 - distance / 10)
                    red_scores[num-1] = score
            else:
                red_scores[num-1] = 0.3
        
        # 蓝球周期分析（简化）
        blue_appearances = {}
        for i, record in enumerate(recent_30):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_appearances:
                blue_appearances[blue_num] = []
            blue_appearances[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_appearances and len(blue_appearances[num]) >= 2:
                appearances = blue_appearances[num]
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    last_appearance = appearances[-1]
                    current_position = len(recent_30) - 1
                    
                    expected_next = last_appearance + avg_interval
                    distance = abs(current_position - expected_next)
                    score = max(0, 1 - distance / 15)
                    blue_scores[num-1] = score
            else:
                blue_scores[num-1] = 0.3
        
        return red_scores, blue_scores
    
    def ensemble_predict(self, recent_data: List[Dict]) -> Tuple[List[int], int]:
        """集成预测"""
        # 获取各种分析结果
        freq_red, freq_blue = self.frequency_analysis(recent_data)
        markov_red, markov_blue = self.markov_analysis(recent_data)
        corr_red, corr_blue = self.correlation_analysis(recent_data)
        entropy_red, entropy_blue = self.entropy_analysis(recent_data)
        momentum_red, momentum_blue = self.momentum_analysis(recent_data)
        volatility_red, volatility_blue = self.volatility_analysis(recent_data)
        gap_red, gap_blue = self.gap_analysis(recent_data)
        cycle_red, cycle_blue = self.cycle_analysis(recent_data)
        
        # 集成红球预测
        red_ensemble = [0.0] * 33
        for i in range(33):
            red_ensemble[i] = (
                freq_red[i] * self.params['frequency_weight'] +
                markov_red[i] * self.params['markov_weight'] +
                corr_red[i] * self.params['correlation_weight'] +
                entropy_red[i] * self.params['entropy_weight'] +
                momentum_red[i] * self.params['momentum_weight'] +
                volatility_red[i] * self.params['volatility_weight'] +
                gap_red[i] * self.params['gap_analysis_factor'] +
                cycle_red[i] * self.params['cycle_factor']
            )
        
        # 集成蓝球预测
        blue_ensemble = [0.0] * 16
        for i in range(16):
            blue_ensemble[i] = (
                freq_blue[i] * self.params['frequency_weight'] +
                markov_blue[i] * self.params['markov_weight'] +
                corr_blue[i] * self.params['correlation_weight'] +
                entropy_blue[i] * self.params['entropy_weight'] +
                momentum_blue[i] * self.params['momentum_weight'] +
                volatility_blue[i] * self.params['volatility_weight'] +
                gap_blue[i] * self.params['gap_analysis_factor'] +
                cycle_blue[i] * self.params['cycle_factor']
            )
        
        # 应用选择因子
        red_selection_factor = self.params['red_selection_factor']
        blue_selection_factor = self.params['blue_selection_factor']
        diversity_factor = self.params['diversity_factor']
        
        # 选择红球（考虑多样性）
        red_with_scores = [(i+1, score * red_selection_factor) for i, score in enumerate(red_ensemble)]
        red_with_scores.sort(key=lambda x: x[1], reverse=True)
        
        selected_red = []
        candidates = red_with_scores[:20]  # 从前20个候选中选择
        
        for num, score in candidates:
            if len(selected_red) < 6:
                if self._is_combination_valid(selected_red + [num], diversity_factor):
                    selected_red.append(num)
        
        # 如果选择不足6个，补充高分号码
        while len(selected_red) < 6:
            for num, score in red_with_scores:
                if num not in selected_red:
                    selected_red.append(num)
                    break
        
        selected_red = sorted(selected_red[:6])
        
        # 选择蓝球
        blue_with_scores = [(i+1, score * blue_selection_factor) for i, score in enumerate(blue_ensemble)]
        blue_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_scores[0][0]
        
        return selected_red, selected_blue
    
    def _is_combination_valid(self, numbers: List[int], diversity_factor: float) -> bool:
        """检查号码组合是否有效（增强版）"""
        if len(numbers) <= 1:
            return True
        
        numbers_sorted = sorted(numbers)
        
        # 检查连号
        consecutive_count = 1
        max_consecutive = 1
        
        for i in range(1, len(numbers_sorted)):
            if numbers_sorted[i] == numbers_sorted[i-1] + 1:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 1
        
        if max_consecutive > 3:
            return False
        
        # 检查区间分布
        if len(numbers) >= 4:
            zone1 = sum(1 for n in numbers if 1 <= n <= 11)
            zone2 = sum(1 for n in numbers if 12 <= n <= 22)
            zone3 = sum(1 for n in numbers if 23 <= n <= 33)
            
            # 根据多样性因子调整阈值
            max_zone_ratio = 0.8 - diversity_factor * 0.3
            if zone1 > len(numbers) * max_zone_ratio or zone2 > len(numbers) * max_zone_ratio or zone3 > len(numbers) * max_zone_ratio:
                return False
        
        # 检查奇偶分布
        if len(numbers) >= 4:
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            even_count = len(numbers) - odd_count
            
            # 避免过度偏向奇数或偶数
            if odd_count == 0 or even_count == 0:
                return False
        
        return True
    
    def update_params(self, new_params: Dict[str, float]):
        """更新参数"""
        for key, value in new_params.items():
            if key in self.params:
                self.params[key] = value
        
        # 归一化权重
        weight_keys = [k for k in self.params.keys() if 'weight' in k]
        total_weight = sum(self.params[k] for k in weight_keys)
        if total_weight > 0:
            for k in weight_keys:
                self.params[k] /= total_weight

def bayesian_optimization(predictor: BayesianOptimizedPredictor, n_iterations: int = 20, test_periods: int = 100):
    """简化的贝叶斯优化"""
    print(f"开始贝叶斯优化，迭代次数: {n_iterations}")
    
    best_params = predictor.params.copy()
    best_score = evaluate_predictor(predictor, test_periods)
    
    print(f"初始评分: {best_score:.4f}")
    
    for iteration in range(n_iterations):
        print(f"\n迭代 {iteration + 1}/{n_iterations}")
        
        # 生成候选参数（随机搜索 + 局部优化）
        candidate_params = best_params.copy()
        
        # 随机选择几个参数进行调整
        param_keys = list(candidate_params.keys())
        num_changes = random.randint(1, min(5, len(param_keys)))
        selected_keys = random.sample(param_keys, num_changes)
        
        for key in selected_keys:
            if 'weight' in key:
                # 权重参数
                candidate_params[key] += random.uniform(-0.05, 0.05)
                candidate_params[key] = max(0.01, min(0.5, candidate_params[key]))
            elif 'window' in key:
                # 窗口参数
                candidate_params[key] += random.randint(-5, 5)
                candidate_params[key] = max(5, min(100, int(candidate_params[key])))
            elif 'factor' in key:
                # 因子参数
                candidate_params[key] += random.uniform(-0.1, 0.1)
                candidate_params[key] = max(0.1, min(2.0, candidate_params[key]))
        
        # 归一化权重
        weight_keys = [k for k in candidate_params.keys() if 'weight' in k]
        total_weight = sum(candidate_params[k] for k in weight_keys)
        if total_weight > 0:
            for k in weight_keys:
                candidate_params[k] /= total_weight
        
        # 评估候选参数
        predictor.update_params(candidate_params)
        score = evaluate_predictor(predictor, test_periods)
        
        print(f"候选评分: {score:.4f}")
        
        # 如果更好，更新最佳参数
        if score > best_score:
            best_score = score
            best_params = candidate_params.copy()
            print(f"✓ 找到更好的参数，评分提升至: {best_score:.4f}")
        else:
            # 恢复原参数
            predictor.update_params(best_params)
    
    # 应用最佳参数
    predictor.update_params(best_params)
    
    print(f"\n贝叶斯优化完成，最佳评分: {best_score:.4f}")
    return best_params, best_score

def evaluate_predictor(predictor: BayesianOptimizedPredictor, test_periods: int) -> float:
    """评估预测器性能"""
    if len(predictor.data) < test_periods + 50:
        return 0.0
    
    test_data = predictor.data[-test_periods:]
    red_hits = []
    blue_hits = 0
    
    for i in range(test_periods):
        history_data = predictor.data[:-(test_periods-i)]
        
        try:
            pred_red, pred_blue = predictor.ensemble_predict(history_data)
            
            actual_data = test_data[i]
            actual_red = predictor.parse_numbers(actual_data['number'])
            actual_blue = int(actual_data['refernumber'])
            
            red_hit_count = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            
            red_hits.append(red_hit_count)
            blue_hits += blue_hit
        except Exception:
            red_hits.append(0)
    
    avg_red_hits = sum(red_hits) / len(red_hits) if red_hits else 0
    blue_hit_rate = blue_hits / test_periods * 100 if test_periods > 0 else 0
    
    # 评分函数
    score = (avg_red_hits / 6) * 0.6 + (blue_hit_rate / 100) * 0.4
    return score

def bayesian_optimized_backtest(predictor: BayesianOptimizedPredictor, test_periods: int = 500):
    """贝叶斯优化算法回测"""
    print("\n=== 贝叶斯优化算法回测 ===")
    print(f"回测期数: {test_periods}")
    print("目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("=" * 50)
    
    if len(predictor.data) < test_periods + 100:
        print(f"数据不足，需要至少 {test_periods + 100} 期数据")
        return
    
    test_data = predictor.data[-test_periods:]
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    print(f"开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        history_data = predictor.data[:-(test_periods-i)]
        
        try:
            pred_red, pred_blue = predictor.ensemble_predict(history_data)
            
            actual_data = test_data[i]
            actual_red = predictor.parse_numbers(actual_data['number'])
            actual_blue = int(actual_data['refernumber'])
            
            red_hit_count = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            
            red_hits.append(red_hit_count)
            blue_hits += blue_hit
            
            if red_hit_count >= 3 or blue_hit == 1:
                best_predictions.append({
                    'period': actual_data['issueno'],
                    'red_hit': red_hit_count,
                    'blue_hit': blue_hit,
                    'predicted_red': pred_red,
                    'actual_red': actual_red,
                    'predicted_blue': pred_blue,
                    'actual_blue': actual_blue
                })
        except Exception as e:
            print(f"预测第 {i+1} 期时出错: {e}")
            red_hits.append(0)
        
        if (i + 1) % 100 == 0:
            current_red_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / (i + 1) * 100
            print(f"进度: {i+1}/{test_periods}, 当前红球: {current_red_avg:.2f}/6, 蓝球: {current_blue_rate:.1f}%")
    
    # 计算最终结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / test_periods * 100
    
    print(f"\n=== 贝叶斯优化算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits}/{test_periods})")
    
    # 红球命中分布
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_hits.count(i)
        percentage = count / test_periods * 100
        print(f"  {i}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 10.0
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前15期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:15]):
            blue_symbol = "✓" if pred['blue_hit'] else "✗"
            print(f"{i+1:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_symbol}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 显示优化后的参数
    print(f"\n=== 优化后的参数 ===")
    for key, value in predictor.params.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.3f}")
        else:
            print(f"  {key}: {value}")
    
    # 算法评估
    print(f"\n=== 贝叶斯优化算法评估 ===")
    if avg_red_hits >= red_target and blue_hit_rate >= blue_target:
        print("🎉 恭喜！算法达到预期目标")
    elif avg_red_hits >= red_target * 0.9 and blue_hit_rate >= blue_target * 0.9:
        print("🔧 算法表现优秀，非常接近目标")
    elif avg_red_hits >= red_target * 0.75 and blue_hit_rate >= blue_target * 0.75:
        print("📈 算法表现良好，还有提升空间")
    else:
        print("⚠️ 算法需要进一步优化")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement,
        'params': predictor.params.copy()
    }

if __name__ == "__main__":
    print("=== 贝叶斯优化双色球预测算法 ===")
    
    # 创建预测器
    predictor = BayesianOptimizedPredictor()
    
    if not predictor.load_data('ssq_data.json'):
        print("数据加载失败，程序退出")
        exit(1)
    
    # 贝叶斯优化
    print("\n开始贝叶斯优化参数...")
    best_params, best_score = bayesian_optimization(
        predictor, 
        n_iterations=15,  # 减少迭代次数以加快速度
        test_periods=100
    )
    
    # 完整回测
    print("\n开始完整回测...")
    results = bayesian_optimized_backtest(predictor, test_periods=500)
    
    if results:
        print(f"\n=== 贝叶斯优化总结 ===")
        print(f"经过贝叶斯优化后：")
        print(f"- 红球命中率: {results['red_avg']:.2f}/6 (达成度: {results['red_achievement']:.1f}%)")
        print(f"- 蓝球命中率: {results['blue_rate']:.1f}% (达成度: {results['blue_achievement']:.1f}%)")
        print(f"- 整体目标达成度: {(results['red_achievement'] + results['blue_achievement'])/2:.1f}%")
        
        if results['red_avg'] >= 1.4 or results['blue_rate'] >= 9.0:
            print("\n🚀 贝叶斯优化效果显著！")
        elif results['red_avg'] >= 1.2 or results['blue_rate'] >= 7.5:
            print("\n📈 贝叶斯优化有一定效果，建议继续优化")
        else:
            print("\n🔄 建议尝试其他优化策略或增加特征")