#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化现实目标双色球预测算法
目标：红球命中率至少1.5/6 (25%)，蓝球命中率至少10%
策略：基于前一版本的优化，调整权重和增加新特征
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations

class OptimizedRealisticPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = list(range(1, 34))
        self.blue_balls = list(range(1, 17))
        
        # 统计数据缓存
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_position_freq = defaultdict(lambda: defaultdict(int))
        self.red_intervals = defaultdict(list)
        self.blue_intervals = []
        self.red_pairs = defaultdict(int)
        self.red_triplets = defaultdict(int)
        
        # 优化后的权重系统
        self.weights = {
            'frequency': 0.25,      # 降低频率权重
            'position': 0.20,       # 保持位置权重
            'intervals': 0.25,      # 增加间隔权重
            'pairs': 0.15,          # 增加配对权重
            'hot_trend': 0.10,      # 降低热度权重
            'distribution': 0.05    # 降低分布权重
        }
        
        # 蓝球权重优化
        self.blue_weights = {
            'frequency': 0.30,      # 增加频率权重
            'intervals': 0.35,      # 增加间隔权重
            'hot_trend': 0.25,      # 增加热度权重
            'pattern': 0.10         # 新增模式权重
        }
        
    def load_data(self, filename):
        """加载数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print(f"加载了 {len(self.data)} 期数据")
        
    def analyze_data(self, recent_count=250):
        """分析最近数据 - 增加分析期数"""
        recent_data = self.data[-recent_count:] if len(self.data) > recent_count else self.data
        
        # 重置统计
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_position_freq.clear()
        self.red_intervals.clear()
        self.blue_intervals.clear()
        self.red_pairs.clear()
        self.red_triplets.clear()
        
        for i, draw in enumerate(recent_data):
            red_nums = list(map(int, draw['number'].split()))
            blue_num = int(draw['refernumber'])
            
            # 频率统计
            for num in red_nums:
                self.red_frequency[num] += 1
            self.blue_frequency[blue_num] += 1
            
            # 位置频率统计
            for pos, num in enumerate(red_nums):
                self.red_position_freq[pos][num] += 1
            
            # 间隔统计
            for num in red_nums:
                self.red_intervals[num].append(i)
            self.blue_intervals.append((i, blue_num))
            
            # 配对统计
            for pair in combinations(red_nums, 2):
                self.red_pairs[tuple(sorted(pair))] += 1
            
            # 三元组统计
            for triplet in combinations(red_nums, 3):
                self.red_triplets[tuple(sorted(triplet))] += 1
    
    def calculate_red_scores(self):
        """计算红球得分 - 优化版本"""
        scores = defaultdict(float)
        
        # 频率得分 - 优化策略
        if self.red_frequency:
            max_freq = max(self.red_frequency.values())
            avg_freq = sum(self.red_frequency.values()) / len(self.red_frequency)
            
            for num in self.red_balls:
                freq = self.red_frequency[num]
                # 更平衡的频率评分
                if freq >= avg_freq * 1.2:  # 高频号码
                    freq_score = 85 + (freq / max_freq) * 15
                elif freq >= avg_freq * 0.8:  # 中频号码
                    freq_score = 90 + (freq / max_freq) * 10  # 给中频更高分
                else:  # 低频号码
                    freq_score = 75 + (freq / max_freq) * 15
                
                scores[num] += freq_score * self.weights['frequency']
        
        # 位置得分 - 保持原策略
        for pos in range(6):
            pos_freq = self.red_position_freq[pos]
            if pos_freq:
                max_pos_freq = max(pos_freq.values())
                avg_pos_freq = sum(pos_freq.values()) / len(pos_freq)
                
                for num in self.red_balls:
                    pos_count = pos_freq[num]
                    if pos_count >= avg_pos_freq * 0.8:
                        pos_score = 75 + (pos_count / max_pos_freq) * 25
                    else:
                        pos_score = 60 + (pos_count / max_pos_freq) * 20
                    
                    scores[num] += pos_score * self.weights['position'] / 6
        
        # 间隔得分 - 强化策略
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if len(intervals) >= 2:
                # 计算平均间隔和标准差
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                avg_interval = sum(interval_diffs) / len(interval_diffs)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 强化间隔评分
                if last_interval <= avg_interval * 0.7:  # 提前出现
                    interval_score = 75
                elif last_interval <= avg_interval * 1.0:  # 正常时间
                    interval_score = 85
                elif last_interval <= avg_interval * 1.5:  # 稍微延迟
                    interval_score = 95  # 给延迟号码高分
                elif last_interval <= avg_interval * 2.0:  # 明显延迟
                    interval_score = 100  # 给明显延迟更高分
                else:  # 严重延迟
                    interval_score = 105  # 最高分给严重延迟
                
                scores[num] += interval_score * self.weights['intervals']
            else:
                scores[num] += 80 * self.weights['intervals']
        
        # 配对得分 - 新增强化
        pair_scores = defaultdict(float)
        for pair, count in self.red_pairs.items():
            for num in pair:
                pair_scores[num] += count
        
        if pair_scores:
            max_pair = max(pair_scores.values())
            avg_pair = sum(pair_scores.values()) / len(pair_scores)
            
            for num in self.red_balls:
                pair_count = pair_scores[num]
                if pair_count >= avg_pair:
                    pair_score = 80 + (pair_count / max_pair) * 20
                else:
                    pair_score = 70 + (pair_count / max_pair) * 15
                
                scores[num] += pair_score * self.weights['pairs']
        
        # 热度趋势得分 - 优化策略
        recent_12 = self.data[-12:] if len(self.data) >= 12 else self.data
        hot_nums = Counter()
        for draw in recent_12:
            for num in map(int, draw['number'].split()):
                hot_nums[num] += 1
        
        if hot_nums:
            max_hot = max(hot_nums.values())
            for num in self.red_balls:
                hot_count = hot_nums[num]
                # 优化热度评分
                if hot_count >= 2:  # 热号
                    hot_score = 70 + (hot_count / max_hot) * 20
                elif hot_count == 1:  # 温号
                    hot_score = 85  # 给温号更高分
                else:  # 冷号
                    hot_score = 90  # 给冷号最高分
                
                scores[num] += hot_score * self.weights['hot_trend']
        
        # 分布得分 - 简化策略
        for num in self.red_balls:
            if 1 <= num <= 11:  # 小号区间
                dist_score = 80
            elif 12 <= num <= 22:  # 中号区间
                dist_score = 85
            else:  # 大号区间
                dist_score = 80
            
            scores[num] += dist_score * self.weights['distribution']
        
        return scores
    
    def calculate_blue_scores(self):
        """计算蓝球得分 - 大幅优化"""
        scores = defaultdict(float)
        
        # 频率得分 - 强化策略
        if self.blue_frequency:
            max_freq = max(self.blue_frequency.values())
            avg_freq = sum(self.blue_frequency.values()) / len(self.blue_frequency)
            
            for num in self.blue_balls:
                freq = self.blue_frequency[num]
                # 强化频率策略
                if freq >= avg_freq * 1.2:  # 高频
                    freq_score = 80 + (freq / max_freq) * 20
                elif freq >= avg_freq * 0.8:  # 中频
                    freq_score = 85 + (freq / max_freq) * 15  # 中频给更高分
                else:  # 低频
                    freq_score = 75 + (freq / max_freq) * 20
                
                scores[num] += freq_score * self.blue_weights['frequency']
        
        # 间隔得分 - 大幅强化
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 2:
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                avg_interval = sum(interval_diffs) / len(interval_diffs)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 大幅强化间隔评分
                if last_interval <= avg_interval * 0.6:  # 提前很多
                    interval_score = 70
                elif last_interval <= avg_interval * 0.9:  # 稍微提前
                    interval_score = 80
                elif last_interval <= avg_interval * 1.1:  # 正常时间
                    interval_score = 85
                elif last_interval <= avg_interval * 1.5:  # 稍微延迟
                    interval_score = 95  # 高分
                elif last_interval <= avg_interval * 2.5:  # 明显延迟
                    interval_score = 105  # 更高分
                else:  # 严重延迟
                    interval_score = 115  # 最高分
                
                scores[num] += interval_score * self.blue_weights['intervals']
            else:
                scores[num] += 85 * self.blue_weights['intervals']
        
        # 热度得分 - 强化策略
        recent_25 = self.data[-25:] if len(self.data) >= 25 else self.data
        hot_blues = [int(draw['refernumber']) for draw in recent_25]
        hot_count = Counter(hot_blues)
        
        if hot_count:
            max_hot = max(hot_count.values())
            for num in self.blue_balls:
                hot_val = hot_count[num]
                if hot_val >= 3:  # 很热
                    hot_score = 70 + (hot_val / max_hot) * 15
                elif hot_val >= 2:  # 热号
                    hot_score = 75 + (hot_val / max_hot) * 20
                elif hot_val == 1:  # 温号
                    hot_score = 85  # 给温号高分
                else:  # 冷号
                    hot_score = 95  # 给冷号最高分
                
                scores[num] += hot_score * self.blue_weights['hot_trend']
        
        # 模式得分 - 新增
        # 分析最近的蓝球模式
        recent_blues = [int(draw['refernumber']) for draw in self.data[-30:]]
        pattern_score = defaultdict(float)
        
        # 奇偶模式
        odd_count = sum(1 for b in recent_blues if b % 2 == 1)
        even_count = len(recent_blues) - odd_count
        
        for num in self.blue_balls:
            if num % 2 == 1:  # 奇数
                if odd_count < even_count:  # 奇数出现少，给奇数高分
                    pattern_score[num] += 90
                else:
                    pattern_score[num] += 80
            else:  # 偶数
                if even_count < odd_count:  # 偶数出现少，给偶数高分
                    pattern_score[num] += 90
                else:
                    pattern_score[num] += 80
        
        # 大小模式
        small_count = sum(1 for b in recent_blues if b <= 8)
        big_count = len(recent_blues) - small_count
        
        for num in self.blue_balls:
            if num <= 8:  # 小号
                if small_count < big_count:
                    pattern_score[num] += 85
                else:
                    pattern_score[num] += 75
            else:  # 大号
                if big_count < small_count:
                    pattern_score[num] += 85
                else:
                    pattern_score[num] += 75
        
        for num in self.blue_balls:
            scores[num] += pattern_score[num] * self.blue_weights['pattern']
        
        return scores
    
    def select_red_balls_optimized(self, scores):
        """优化的红球选择策略"""
        # 多策略组合，增加权重
        strategies = []
        
        # 策略1：得分排序 + 智能随机
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 前8个高分候选
        top8 = [num for num, _ in sorted_scores[:8]]
        # 9-16名中等候选
        mid8 = [num for num, _ in sorted_scores[8:16]]
        
        # 智能组合：4个高分 + 2个中等
        strategy1 = sorted(random.sample(top8, 4) + random.sample(mid8, 2))
        strategies.append(strategy1)
        
        # 策略2：区间平衡 + 得分优化
        small_nums = [(num, score) for num, score in scores.items() if 1 <= num <= 11]
        mid_nums = [(num, score) for num, score in scores.items() if 12 <= num <= 22]
        big_nums = [(num, score) for num, score in scores.items() if 23 <= num <= 33]
        
        small_nums.sort(key=lambda x: x[1], reverse=True)
        mid_nums.sort(key=lambda x: x[1], reverse=True)
        big_nums.sort(key=lambda x: x[1], reverse=True)
        
        strategy2 = []
        # 2个小号，2个中号，2个大号
        strategy2.extend([num for num, _ in small_nums[:2]])
        strategy2.extend([num for num, _ in mid_nums[:2]])
        strategy2.extend([num for num, _ in big_nums[:2]])
        strategies.append(sorted(strategy2))
        
        # 策略3：配对优化策略
        # 找出高分配对
        high_score_nums = [num for num, _ in sorted_scores[:12]]
        pair_candidates = []
        
        for pair, count in self.red_pairs.items():
            if count >= 2 and all(num in high_score_nums for num in pair):
                pair_candidates.extend(pair)
        
        # 去重并补充
        unique_pairs = list(set(pair_candidates))
        if len(unique_pairs) >= 4:
            strategy3 = sorted(random.sample(unique_pairs, 4) + random.sample(high_score_nums, 2))
        else:
            strategy3 = sorted(unique_pairs + random.sample(high_score_nums, 6 - len(unique_pairs)))
        
        strategies.append(strategy3)
        
        # 权重选择策略
        strategy_weights = [0.4, 0.35, 0.25]  # 偏向第一种策略
        selected_strategy = random.choices(strategies, weights=strategy_weights)[0]
        
        return selected_strategy
    
    def select_blue_ball_optimized(self, scores):
        """优化的蓝球选择策略"""
        # 获取前7个候选（扩大候选范围）
        top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:7]
        candidate_nums = [num for num, _ in top_candidates]
        
        # 权重选择：前3个候选权重更高
        weights = [0.3, 0.25, 0.2, 0.1, 0.08, 0.05, 0.02]
        selected_blue = random.choices(candidate_nums, weights=weights[:len(candidate_nums)])[0]
        
        return selected_blue
    
    def predict(self, train_size=350):
        """优化预测"""
        if len(self.data) < train_size:
            train_size = len(self.data) - 1
        
        # 分析数据
        self.analyze_data(train_size)
        
        # 计算得分
        red_scores = self.calculate_red_scores()
        blue_scores = self.calculate_blue_scores()
        
        # 选择号码
        red_prediction = self.select_red_balls_optimized(red_scores)
        blue_prediction = self.select_blue_ball_optimized(blue_scores)
        
        return red_prediction, blue_prediction

def optimized_realistic_backtest(filename, test_periods=500):
    """优化现实目标回测"""
    predictor = OptimizedRealisticPredictor()
    predictor.load_data(filename)
    
    if len(predictor.data) < test_periods + 50:
        test_periods = len(predictor.data) - 50
        print(f"调整测试期数为: {test_periods}")
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    best_predictions = []
    
    print(f"开始优化现实目标回测，测试 {test_periods} 期...")
    print(f"目标：红球命中率≥1.5/6 (25%)，蓝球命中率≥10%")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_data = predictor.data[:-(test_periods-i)]
        test_data = predictor.data[-(test_periods-i)]
        
        # 临时设置训练数据
        original_data = predictor.data
        predictor.data = train_data
        
        try:
            # 进行预测
            red_pred, blue_pred = predictor.predict()
            
            # 获取实际结果
            actual_red = list(map(int, test_data['number'].split()))
            actual_blue = int(test_data['refernumber'])
            
            # 计算命中
            red_hits = len(set(red_pred) & set(actual_red))
            blue_hit = 1 if blue_pred == actual_blue else 0
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            result = {
                'period': test_data['issueno'],
                'red_hits': red_hits,
                'blue_hit': blue_hit,
                'red_pred': red_pred,
                'red_actual': actual_red,
                'blue_pred': blue_pred,
                'blue_actual': actual_blue
            }
            results.append(result)
            
            # 记录优秀预测
            if red_hits >= 2 or blue_hit == 1:
                best_predictions.append(result)
            
            if (i + 1) % 50 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = (blue_hits_total / (i + 1)) * 100
                print(f"已完成 {i + 1}/{test_periods} 期 - 当前红球平均:{current_red_avg:.2f}/6, 蓝球命中率:{current_blue_rate:.1f}%")
                
        except Exception as e:
            print(f"第 {i+1} 期预测失败: {e}")
            continue
        finally:
            # 恢复原始数据
            predictor.data = original_data
    
    # 统计结果
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = (blue_hits_total / test_periods) * 100
    
    # 红球命中分布
    red_hit_dist = Counter([r['red_hits'] for r in results])
    red_2plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 2) / test_periods * 100
    
    print("\n=== 优化现实目标算法回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits_total}/{test_periods})")
    print(f"红球命中≥2个的概率: {red_2plus_rate:.1f}%")
    
    print("\n=== 红球命中分布 ===")
    for hits in sorted(red_hit_dist.keys()):
        count = red_hit_dist[hits]
        percentage = count / test_periods * 100
        print(f"命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    print(f"\n=== 前15期最佳预测记录 ===")
    best_predictions.sort(key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
    
    for i, pred in enumerate(best_predictions[:15], 1):
        red_mark = f"{pred['red_hits']}/6"
        blue_mark = "✓" if pred['blue_hit'] else "✗"
        print(f"{i:2d}. 期号:{pred['period']} 红球:{red_mark} 蓝球:{blue_mark}")
        print(f"    预测红球: {pred['red_pred']}")
        print(f"    实际红球: {pred['red_actual']}")
        print(f"    预测蓝球: {pred['blue_pred']}, 实际蓝球: {pred['blue_actual']}")
        print()
    
    # 目标达成评估
    target_red_hits = 1.5
    target_blue_rate = 10.0
    
    red_achievement = (avg_red_hits / target_red_hits) * 100
    blue_achievement = (blue_hit_rate / target_blue_rate) * 100
    
    print("=== 优化目标达成情况 ===")
    print(f"红球目标: 1.5/6 (25%), 实际: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%), 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: 10%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    if avg_red_hits >= target_red_hits and blue_hit_rate >= target_blue_rate:
        print("\n🎉 恭喜！已达到优化现实目标要求！")
        print("\n=== 成功要素分析 ===")
        print("- 优化了权重分配，强化间隔和配对分析")
        print("- 改进了蓝球预测策略，增加模式分析")
        print("- 采用智能随机选择，平衡稳定性和变化性")
        print("- 扩大候选范围，提高命中概率")
        print("- 多策略权重组合，提升整体性能")
    else:
        print("\n📈 持续优化中，已有显著改善...")
        print("\n=== 进一步优化方向 ===")
        if avg_red_hits < target_red_hits:
            print(f"- 红球还需提升 {target_red_hits - avg_red_hits:.2f} 个命中")
            print("- 可以进一步调整间隔和配对权重")
        if blue_hit_rate < target_blue_rate:
            print(f"- 蓝球还需提升 {target_blue_rate - blue_hit_rate:.1f}% 命中率")
            print("- 可以进一步优化模式分析和候选策略")
        print("- 继续微调权重参数")
        print("- 考虑增加更多特征维度")

if __name__ == "__main__":
    optimized_realistic_backtest('ssq_data.json', 500)