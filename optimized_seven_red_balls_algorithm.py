#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化7红球增概率双色球预测算法
策略：基于原7红球策略的深度优化版本
目标：红球命中率≥1.5/7 (21.4%)，蓝球命中率≥10%
优化点：
1. 动态权重调整系统
2. 多层特征融合
3. 智能策略选择
4. 强化学习优化
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations
import math

class OptimizedSevenRedBallsPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = list(range(1, 34))
        self.blue_balls = list(range(1, 17))
        
        # 统计数据缓存
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_position_freq = defaultdict(lambda: defaultdict(int))
        self.red_intervals = defaultdict(list)
        self.blue_intervals = []
        self.red_pairs = defaultdict(int)
        self.red_triplets = defaultdict(int)
        self.red_sequences = defaultdict(int)
        
        # 动态权重系统 - 根据历史表现自适应调整
        self.base_weights = {
            'frequency': 0.22,      # 频率分析
            'position': 0.18,       # 位置分析
            'intervals': 0.20,      # 间隔分析
            'hot_trend': 0.15,      # 热度趋势
            'distribution': 0.10,   # 分布平衡
            'combination': 0.08,    # 组合分析
            'sequence': 0.07        # 序列模式
        }
        
        # 性能追踪
        self.performance_history = []
        self.weight_adjustment_factor = 0.05
        
    def load_data(self, filename):
        """加载数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print(f"加载了 {len(self.data)} 期数据")
        
    def adjust_weights_dynamically(self):
        """根据历史表现动态调整权重"""
        if len(self.performance_history) < 10:
            return self.base_weights.copy()
        
        # 分析最近10期的表现
        recent_performance = self.performance_history[-10:]
        avg_red_hits = np.mean([p['red_hits'] for p in recent_performance])
        avg_blue_hits = np.mean([p['blue_hit'] for p in recent_performance])
        
        adjusted_weights = self.base_weights.copy()
        
        # 如果红球命中率低，增加频率和间隔权重
        if avg_red_hits < 1.3:
            adjusted_weights['frequency'] += self.weight_adjustment_factor
            adjusted_weights['intervals'] += self.weight_adjustment_factor
            adjusted_weights['hot_trend'] -= self.weight_adjustment_factor * 0.5
            adjusted_weights['distribution'] -= self.weight_adjustment_factor * 0.5
        
        # 如果蓝球命中率低，调整相关权重
        if avg_blue_hits < 0.08:
            # 蓝球权重在蓝球计算中单独处理
            pass
        
        # 归一化权重
        total_weight = sum(adjusted_weights.values())
        for key in adjusted_weights:
            adjusted_weights[key] /= total_weight
        
        return adjusted_weights
        
    def analyze_data_advanced(self, recent_count=300):
        """高级数据分析 - 多维度特征提取"""
        recent_data = self.data[-recent_count:] if len(self.data) > recent_count else self.data
        
        # 重置统计
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_position_freq.clear()
        self.red_intervals.clear()
        self.blue_intervals.clear()
        self.red_pairs.clear()
        self.red_triplets.clear()
        self.red_sequences.clear()
        
        for i, draw in enumerate(recent_data):
            red_nums = list(map(int, draw['number'].split()))
            blue_num = int(draw['refernumber'])
            
            # 基础频率统计
            for num in red_nums:
                self.red_frequency[num] += 1
            self.blue_frequency[blue_num] += 1
            
            # 位置频率统计（加权）
            for pos, num in enumerate(red_nums):
                # 近期数据给更高权重
                weight = 1.0 + (i / len(recent_data)) * 0.5
                self.red_position_freq[pos][num] += weight
            
            # 间隔统计
            for num in red_nums:
                self.red_intervals[num].append(i)
            self.blue_intervals.append((i, blue_num))
            
            # 配对和三元组统计（加权）
            weight = 1.0 + (i / len(recent_data)) * 0.3
            for pair in combinations(red_nums, 2):
                self.red_pairs[tuple(sorted(pair))] += weight
            
            for triplet in combinations(red_nums, 3):
                self.red_triplets[tuple(sorted(triplet))] += weight
            
            # 序列模式分析
            sorted_reds = sorted(red_nums)
            for j in range(len(sorted_reds) - 1):
                if sorted_reds[j+1] - sorted_reds[j] <= 3:  # 连续或接近的号码
                    self.red_sequences[(sorted_reds[j], sorted_reds[j+1])] += weight
    
    def calculate_red_scores_optimized(self):
        """优化红球得分计算 - 多层特征融合"""
        scores = defaultdict(float)
        weights = self.adjust_weights_dynamically()
        
        # 第一层：基础特征
        # 1. 频率得分 - 使用正态分布建模
        if self.red_frequency:
            freq_values = list(self.red_frequency.values())
            mean_freq = np.mean(freq_values)
            std_freq = np.std(freq_values)
            
            for num in self.red_balls:
                freq = self.red_frequency[num]
                # 使用Z-score标准化
                z_score = (freq - mean_freq) / (std_freq + 1e-6)
                
                # 转换为得分（0-100）
                if z_score > 1.5:  # 超高频
                    freq_score = 85 + min(15, z_score * 5)
                elif z_score > 0.5:  # 高频
                    freq_score = 80 + z_score * 10
                elif z_score > -0.5:  # 中频
                    freq_score = 75 + z_score * 10
                else:  # 低频
                    freq_score = 70 + max(-10, z_score * 5)
                
                scores[num] += freq_score * weights['frequency']
        
        # 2. 位置得分 - 加权位置分析
        position_weights = [0.15, 0.18, 0.20, 0.20, 0.18, 0.09]  # 中间位置权重更高
        for pos in range(6):
            pos_freq = self.red_position_freq[pos]
            if pos_freq:
                max_pos_freq = max(pos_freq.values())
                avg_pos_freq = sum(pos_freq.values()) / len(pos_freq)
                
                for num in self.red_balls:
                    pos_count = pos_freq[num]
                    if pos_count >= avg_pos_freq * 1.2:
                        pos_score = 85 + (pos_count / max_pos_freq) * 15
                    elif pos_count >= avg_pos_freq * 0.8:
                        pos_score = 80
                    else:
                        pos_score = 70 + (pos_count / max_pos_freq) * 15
                    
                    scores[num] += pos_score * weights['position'] * position_weights[pos]
        
        # 3. 间隔得分 - 预测性间隔分析
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if len(intervals) >= 3:
                # 计算间隔的趋势
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                
                # 使用线性回归预测下次出现时间
                x = np.arange(len(interval_diffs))
                if len(interval_diffs) > 1:
                    slope = np.polyfit(x, interval_diffs, 1)[0]
                    predicted_interval = interval_diffs[-1] + slope
                else:
                    predicted_interval = interval_diffs[0]
                
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 基于预测间隔评分
                if last_interval >= predicted_interval * 0.8:
                    interval_score = 90 + min(10, (last_interval / predicted_interval - 0.8) * 20)
                else:
                    interval_score = 70 + (last_interval / predicted_interval) * 20
                
                scores[num] += interval_score * weights['intervals']
            else:
                scores[num] += 75 * weights['intervals']
        
        # 第二层：高级特征
        # 4. 多时间窗口热度趋势
        time_windows = [5, 10, 15, 25, 40]
        window_weights = [0.3, 0.25, 0.2, 0.15, 0.1]
        
        for window, w_weight in zip(time_windows, window_weights):
            recent_data = self.data[-window:] if len(self.data) >= window else self.data
            hot_nums = Counter()
            for draw in recent_data:
                for num in map(int, draw['number'].split()):
                    hot_nums[num] += 1
            
            if hot_nums:
                max_hot = max(hot_nums.values())
                expected_count = window / 33 * 6
                
                for num in self.red_balls:
                    hot_count = hot_nums[num]
                    
                    # 动态评分策略
                    if hot_count == 0:  # 完全冷号
                        hot_score = 95
                    elif hot_count >= expected_count * 2:  # 超热号
                        hot_score = 75
                    elif hot_count >= expected_count * 1.2:  # 热号
                        hot_score = 80
                    elif hot_count >= expected_count * 0.5:  # 温号
                        hot_score = 85
                    else:  # 冷号
                        hot_score = 90
                    
                    scores[num] += hot_score * weights['hot_trend'] * w_weight
        
        # 5. 智能分布平衡
        for num in self.red_balls:
            # 区间分布优化
            if 1 <= num <= 11:  # 小号区间
                region_score = 82
            elif 12 <= num <= 22:  # 中号区间
                region_score = 88  # 中号区间稍微偏好
            else:  # 大号区间
                region_score = 80
            
            # 奇偶平衡优化
            if num % 2 == 1:  # 奇数
                parity_score = 3
            else:  # 偶数
                parity_score = 2
            
            # 尾数分布
            tail = num % 10
            if tail in [1, 2, 3, 7, 8, 9]:  # 常见尾数
                tail_score = 2
            else:
                tail_score = 4  # 不常见尾数给高分
            
            total_dist_score = region_score + parity_score + tail_score
            scores[num] += total_dist_score * weights['distribution']
        
        # 6. 高级组合分析
        # 分析高频配对和三元组
        pair_influence = defaultdict(float)
        triplet_influence = defaultdict(float)
        
        if self.red_pairs:
            top_pairs = sorted(self.red_pairs.items(), key=lambda x: x[1], reverse=True)[:15]
            for pair, count in top_pairs:
                for num in pair:
                    pair_influence[num] += count * 0.5
        
        if self.red_triplets:
            top_triplets = sorted(self.red_triplets.items(), key=lambda x: x[1], reverse=True)[:10]
            for triplet, count in top_triplets:
                for num in triplet:
                    triplet_influence[num] += count * 0.3
        
        # 组合得分
        max_combo_influence = max(max(pair_influence.values(), default=0), 
                                max(triplet_influence.values(), default=0))
        
        if max_combo_influence > 0:
            for num in self.red_balls:
                combo_score = 70 + ((pair_influence[num] + triplet_influence[num]) / max_combo_influence) * 30
                scores[num] += combo_score * weights['combination']
        
        # 7. 序列模式分析
        sequence_influence = defaultdict(float)
        if self.red_sequences:
            for (num1, num2), count in self.red_sequences.items():
                sequence_influence[num1] += count * 0.4
                sequence_influence[num2] += count * 0.4
        
        if sequence_influence:
            max_seq_influence = max(sequence_influence.values())
            for num in self.red_balls:
                seq_score = 70 + (sequence_influence[num] / max_seq_influence) * 30
                scores[num] += seq_score * weights['sequence']
        
        return scores
    
    def calculate_blue_scores_optimized(self):
        """优化蓝球得分计算"""
        scores = defaultdict(float)
        
        # 1. 频率分析 - 使用贝叶斯方法
        if self.blue_frequency:
            total_draws = sum(self.blue_frequency.values())
            prior_prob = 1.0 / 16  # 先验概率
            
            for num in self.blue_balls:
                freq = self.blue_frequency[num]
                # 贝叶斯更新
                posterior_prob = (freq + 1) / (total_draws + 16)  # 拉普拉斯平滑
                
                if posterior_prob > prior_prob * 1.5:
                    freq_score = 75
                elif posterior_prob > prior_prob * 1.1:
                    freq_score = 85
                elif posterior_prob > prior_prob * 0.9:
                    freq_score = 90
                else:
                    freq_score = 95  # 低频给高分
                
                scores[num] += freq_score * 0.35
        
        # 2. 多时间窗口间隔分析
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 2:
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                
                # 使用指数加权移动平均
                weights_exp = [0.5 ** i for i in range(len(interval_diffs))]
                weights_exp.reverse()
                weights_exp = np.array(weights_exp) / sum(weights_exp)
                
                avg_interval = np.average(interval_diffs, weights=weights_exp)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 预测性评分
                if last_interval >= avg_interval * 1.2:
                    interval_score = 90 + min(10, (last_interval / avg_interval - 1.2) * 25)
                elif last_interval >= avg_interval * 0.8:
                    interval_score = 85
                else:
                    interval_score = 75
                
                scores[num] += interval_score * 0.4
            else:
                scores[num] += 85 * 0.4
        
        # 3. 多层热度分析
        for window, weight in [(10, 0.4), (20, 0.35), (30, 0.25)]:
            recent_data = self.data[-window:] if len(self.data) >= window else self.data
            hot_blues = [int(draw['refernumber']) for draw in recent_data]
            hot_count = Counter(hot_blues)
            
            if hot_count:
                expected = len(recent_data) / 16
                for num in self.blue_balls:
                    hot_val = hot_count[num]
                    
                    if hot_val == 0:  # 完全冷号
                        hot_score = 95
                    elif hot_val >= expected * 2:
                        hot_score = 70
                    elif hot_val >= expected * 1.2:
                        hot_score = 80
                    else:
                        hot_score = 90
                    
                    scores[num] += hot_score * 0.25 * weight
        
        return scores
    
    def select_seven_red_balls_optimized(self, scores):
        """优化7红球选择策略"""
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 策略池
        strategies = []
        
        # 策略1：智能得分排序（70%概率）
        top_12 = [num for num, _ in sorted_scores[:12]]
        # 从前12名中选择7个，增加一些随机性
        strategy1 = sorted(random.sample(top_12, 7))
        strategies.extend([strategy1] * 7)  # 增加权重
        
        # 策略2：区间平衡优化（20%概率）
        top_candidates = [num for num, _ in sorted_scores[:18]]
        small_nums = [num for num in top_candidates if 1 <= num <= 11]
        mid_nums = [num for num in top_candidates if 12 <= num <= 22]
        big_nums = [num for num in top_candidates if 23 <= num <= 33]
        
        strategy2 = []
        # 智能区间分配：2-3-2 或 3-2-2
        if len(small_nums) >= 3 and len(mid_nums) >= 2:
            strategy2.extend(random.sample(small_nums[:6], min(3, len(small_nums))))
            strategy2.extend(random.sample(mid_nums[:6], min(2, len(mid_nums))))
            strategy2.extend(random.sample(big_nums[:6], min(2, len(big_nums))))
        else:
            # 备选方案
            strategy2.extend(random.sample(small_nums[:4], min(2, len(small_nums))))
            strategy2.extend(random.sample(mid_nums[:6], min(3, len(mid_nums))))
            strategy2.extend(random.sample(big_nums[:4], min(2, len(big_nums))))
        
        if len(strategy2) < 7:
            remaining = [num for num, _ in sorted_scores[:15] if num not in strategy2]
            strategy2.extend(random.sample(remaining, 7 - len(strategy2)))
        
        strategies.extend([sorted(strategy2[:7])] * 2)
        
        # 策略3：奇偶平衡（10%概率）
        odd_candidates = [num for num, _ in sorted_scores if num % 2 == 1][:8]
        even_candidates = [num for num, _ in sorted_scores if num % 2 == 0][:8]
        
        strategy3 = []
        strategy3.extend(random.sample(odd_candidates, min(4, len(odd_candidates))))
        strategy3.extend(random.sample(even_candidates, min(3, len(even_candidates))))
        strategies.append(sorted(strategy3))
        
        # 随机选择策略
        selected_strategy = random.choice(strategies)
        return selected_strategy
    
    def select_blue_ball_optimized(self, scores):
        """优化蓝球选择"""
        # 获取前8个候选
        top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:8]
        
        # 使用softmax进行概率分布
        candidate_nums = [num for num, _ in top_candidates]
        raw_scores = [score for _, score in top_candidates]
        
        # Softmax转换
        exp_scores = np.exp(np.array(raw_scores) / 10)  # 温度参数为10
        probabilities = exp_scores / np.sum(exp_scores)
        
        # 加权随机选择
        selected_blue = np.random.choice(candidate_nums, p=probabilities)
        return int(selected_blue)
    
    def predict(self, train_size=350):
        """优化预测方法"""
        if len(self.data) < train_size:
            train_size = len(self.data) - 1
        
        # 高级数据分析
        self.analyze_data_advanced(train_size)
        
        # 计算优化得分
        red_scores = self.calculate_red_scores_optimized()
        blue_scores = self.calculate_blue_scores_optimized()
        
        # 选择号码
        red_prediction = self.select_seven_red_balls_optimized(red_scores)
        blue_prediction = self.select_blue_ball_optimized(blue_scores)
        
        return red_prediction, blue_prediction
    
    def update_performance(self, red_hits, blue_hit):
        """更新性能历史"""
        self.performance_history.append({
            'red_hits': red_hits,
            'blue_hit': blue_hit,
            'timestamp': len(self.performance_history)
        })
        
        # 保持最近50期的记录
        if len(self.performance_history) > 50:
            self.performance_history = self.performance_history[-50:]

def optimized_seven_red_balls_backtest(filename, test_periods=500):
    """优化7红球策略回测"""
    predictor = OptimizedSevenRedBallsPredictor()
    predictor.load_data(filename)
    
    if len(predictor.data) < test_periods + 50:
        test_periods = len(predictor.data) - 50
        print(f"调整测试期数为: {test_periods}")
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    best_predictions = []
    
    # 7红球命中统计
    red_6_hits = 0  # 7个红球中命中6个的次数
    red_5_hits = 0  # 7个红球中命中5个的次数
    red_4_hits = 0  # 7个红球中命中4个的次数
    
    print(f"开始优化7红球策略回测，测试 {test_periods} 期...")
    print(f"策略：优化版7红球选择，动态权重调整，多层特征融合")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_data = predictor.data[:-(test_periods-i)]
        test_data = predictor.data[-(test_periods-i)]
        
        # 临时设置训练数据
        original_data = predictor.data
        predictor.data = train_data
        
        try:
            # 进行预测
            red_pred, blue_pred = predictor.predict()
            
            # 获取实际结果
            actual_red = list(map(int, test_data['number'].split()))
            actual_blue = int(test_data['refernumber'])
            
            # 计算命中
            red_hits = len(set(red_pred) & set(actual_red))
            blue_hit = 1 if blue_pred == actual_blue else 0
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            # 更新性能历史
            predictor.update_performance(red_hits, blue_hit)
            
            # 统计特殊命中情况
            if red_hits >= 6:
                red_6_hits += 1
            elif red_hits >= 5:
                red_5_hits += 1
            elif red_hits >= 4:
                red_4_hits += 1
            
            result = {
                'period': test_data['issueno'],
                'red_hits': red_hits,
                'blue_hit': blue_hit,
                'red_pred': red_pred,
                'red_actual': actual_red,
                'blue_pred': blue_pred,
                'blue_actual': actual_blue
            }
            results.append(result)
            
            # 记录优秀预测
            if red_hits >= 3 or blue_hit == 1:
                best_predictions.append(result)
            
            if (i + 1) % 50 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = (blue_hits_total / (i + 1)) * 100
                print(f"已完成 {i + 1}/{test_periods} 期 - 当前红球平均:{current_red_avg:.2f}/7, 蓝球命中率:{current_blue_rate:.1f}%")
                
        except Exception as e:
            print(f"第 {i+1} 期预测失败: {e}")
            continue
        finally:
            # 恢复原始数据
            predictor.data = original_data
    
    # 统计结果
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = (blue_hits_total / test_periods) * 100
    
    # 红球命中分布
    red_hit_dist = Counter([r['red_hits'] for r in results])
    red_3plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 3) / test_periods * 100
    red_4plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 4) / test_periods * 100
    
    # 目标达成度计算
    red_target = 1.5  # 目标红球命中率
    blue_target = 10.0  # 目标蓝球命中率
    
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    overall_achievement = (red_achievement + blue_achievement) / 2
    
    print("\n=== 优化7红球策略回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hits:.2f}/7 ({avg_red_hits/7*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits_total}/{test_periods})")
    print(f"红球命中≥3个的概率: {red_3plus_rate:.1f}%")
    print(f"红球命中≥4个的概率: {red_4plus_rate:.1f}%")
    
    print("\n=== 目标达成度分析 ===")
    print(f"红球目标: {red_target}/7, 实际: {avg_red_hits:.2f}/7, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    print(f"综合达成度: {overall_achievement:.1f}%")
    
    print("\n=== 红球命中分布 ===")
    for hits in sorted(red_hit_dist.keys()):
        count = red_hit_dist[hits]
        percentage = count / test_periods * 100
        print(f"命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    print("\n=== 优化策略优势分析 ===")
    print(f"命中6个红球: {red_6_hits}次 ({red_6_hits/test_periods*100:.2f}%)")
    print(f"命中5个红球: {red_5_hits}次 ({red_5_hits/test_periods*100:.2f}%)")
    print(f"命中4个红球: {red_4_hits}次 ({red_4_hits/test_periods*100:.2f}%)")
    
    # 计算理论提升
    theoretical_6_balls = 6 * (6/33)
    theoretical_7_balls = 7 * (6/33)
    improvement = (theoretical_7_balls - theoretical_6_balls) / theoretical_6_balls * 100
    
    print(f"\n=== 理论vs实际分析 ===")
    print(f"选择6个红球理论期望: {theoretical_6_balls:.2f}个")
    print(f"选择7个红球理论期望: {theoretical_7_balls:.2f}个")
    print(f"理论提升幅度: {improvement:.1f}%")
    print(f"实际平均命中: {avg_red_hits:.2f}个")
    print(f"实际vs理论7球: {(avg_red_hits/theoretical_7_balls-1)*100:+.1f}%")
    
    print(f"\n=== 前15期最佳预测记录 ===")
    best_predictions.sort(key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
    for i, pred in enumerate(best_predictions[:15], 1):
        red_symbol = "✓" if pred['blue_hit'] else "✗"
        print(f"{i:2d}. 期号:{pred['period']} 红球:{pred['red_hits']}/7 蓝球:{red_symbol}")
        print(f"    预测红球(7个): {pred['red_pred']}")
        print(f"    实际红球(6个): {pred['red_actual']}")
        print(f"    预测蓝球: {pred['blue_pred']}, 实际蓝球: {pred['blue_actual']}")
        print()
    
    print("=== 优化7红球策略总结 ===")
    print("✅ 优化亮点:")
    print("  - 动态权重调整系统，根据历史表现自适应")
    print("  - 多层特征融合，包含序列模式和预测性分析")
    print("  - 智能策略选择，平衡稳定性和多样性")
    print("  - 贝叶斯方法优化蓝球预测")
    
    print("\n⚠️ 成本效益:")
    print("  - 需要购买7注(7选6的组合)")
    print("  - 成本增加7倍")
    print(f"  - 平均命中提升: {(avg_red_hits/theoretical_6_balls-1)*100:+.1f}%")
    
    if overall_achievement >= 80:
        print("\n🎯 目标达成: 优秀! 策略表现超出预期")
    elif overall_achievement >= 70:
        print("\n📈 目标达成: 良好，接近预期目标")
    else:
        print("\n🔧 需要进一步优化策略")
    
    return {
        'avg_red_hits': avg_red_hits,
        'blue_hit_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement,
        'overall_achievement': overall_achievement,
        'best_predictions': best_predictions[:10]
    }

if __name__ == "__main__":
    # 运行优化7红球策略回测
    result = optimized_seven_red_balls_backtest('ssq_data.json', 500)
    print(f"\n最终结果: 红球{result['avg_red_hits']:.2f}/7, 蓝球{result['blue_hit_rate']:.1f}%, 综合达成度{result['overall_achievement']:.1f}%")