#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝球预测问题修复方案

问题分析:
1. 蓝球11在最新一期出现，获得最高马尔科夫权重(1.0)
2. 重复概率调整逻辑有问题：上期蓝球权重被过度放大
3. 蓝球重复率只有7%，但算法给予上期蓝球过高权重

修复方案:
1. 降低上期蓝球的权重加成
2. 增加随机性，避免固定结果
3. 平衡马尔科夫权重和重复概率
"""

import json
import numpy as np
import random
from collections import defaultdict

def load_data():
    """加载数据"""
    with open('ssq_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 转换数据格式
    processed_data = []
    for entry in data:
        red_balls = [int(x) for x in entry['number'].split()]
        blue_ball = int(entry['refernumber'])
        processed_data.append({
            'red': red_balls,
            'blue': blue_ball,
            'period': entry['issueno']
        })
    
    return processed_data

def calculate_blue_repeat_rate(data):
    """计算蓝球真实重复率"""
    if len(data) < 2:
        return 0.07  # 默认7%
    
    repeats = 0
    total = 0
    
    for i in range(1, len(data)):
        if data[i]['blue'] == data[i-1]['blue']:
            repeats += 1
        total += 1
    
    return repeats / total if total > 0 else 0.07

def build_improved_blue_weights(data, blue_range):
    """构建改进的蓝球权重"""
    if not data:
        return {str(i): 1/16 for i in blue_range}
    
    weights = {}
    recent_periods = min(50, len(data))
    
    # 计算基础马尔科夫权重
    for num in blue_range:
        weight = 0
        for j in range(recent_periods):
            if data[j]['blue'] == num:
                # 使用平方根衰减，减少最近期的过度影响
                period_weight = 1.0 / math.sqrt(j + 1)
                weight += period_weight
        weights[str(num)] = weight
    
    return weights

def improved_blue_prediction(data, blue_range):
    """改进的蓝球预测"""
    if not data:
        return random.choice(blue_range)
    
    # 计算真实重复率
    repeat_rate = calculate_blue_repeat_rate(data)
    print(f"蓝球真实重复率: {repeat_rate:.2%}")
    
    # 构建改进的权重
    base_weights = build_improved_blue_weights(data, blue_range)
    
    # 获取上期蓝球
    last_blue = data[0]['blue']
    print(f"上期蓝球: {last_blue}")
    
    # 调整权重
    adjusted_weights = []
    for num in blue_range:
        base_weight = base_weights.get(str(num), 0.1)
        
        if num == last_blue:
            # 根据真实重复率调整，而不是过度放大
            repeat_factor = 1 + repeat_rate  # 只增加7%左右
            adjusted_weight = base_weight * repeat_factor
        else:
            # 非上期蓝球稍微增加权重
            adjusted_weight = base_weight * 1.02
        
        adjusted_weights.append(adjusted_weight)
    
    # 归一化权重
    total_weight = sum(adjusted_weights)
    if total_weight > 0:
        normalized_weights = [w / total_weight for w in adjusted_weights]
    else:
        normalized_weights = [1/len(blue_range)] * len(blue_range)
    
    # 打印权重信息
    print("\n蓝球权重分布:")
    for i, (num, weight) in enumerate(zip(blue_range, normalized_weights)):
        print(f"蓝球 {num:2d}: {weight:.4f} {'⭐' if num == last_blue else ''}")
    
    # 使用权重随机选择
    selected_blue = np.random.choice(blue_range, p=normalized_weights)
    
    return int(selected_blue)

def test_improved_prediction(data, blue_range, num_tests=10):
    """测试改进的预测"""
    print(f"\n=== 测试改进的蓝球预测 (运行{num_tests}次) ===")
    
    results = []
    for i in range(num_tests):
        # 设置不同的随机种子
        np.random.seed(i + 1000)
        random.seed(i + 1000)
        
        predicted_blue = improved_blue_prediction(data, blue_range)
        results.append(predicted_blue)
        print(f"第{i+1:2d}次预测: {predicted_blue}")
    
    # 统计结果
    from collections import Counter
    result_counts = Counter(results)
    
    print("\n预测结果统计:")
    for blue, count in sorted(result_counts.items()):
        percentage = count / num_tests * 100
        print(f"蓝球 {blue:2d}: {count:2d} 次 ({percentage:5.1f}%)")
    
    # 检查是否还是总是预测11
    if result_counts.get(11, 0) == num_tests:
        print("\n⚠️ 问题仍然存在：预测结果仍然总是11")
    elif result_counts.get(11, 0) > num_tests * 0.5:
        print(f"\n⚠️ 蓝球11出现频率过高: {result_counts.get(11, 0)}/{num_tests}")
    else:
        print("\n✅ 预测结果已经多样化，问题得到解决")
    
    return results

def main():
    print("🔧 蓝球预测问题修复测试")
    print("=" * 50)
    
    # 加载数据
    data = load_data()
    print(f"✅ 加载了 {len(data)} 期数据")
    
    blue_range = list(range(1, 17))
    
    # 测试改进的预测
    test_improved_prediction(data, blue_range, 20)
    
    print("\n=== 修复建议 ===")
    print("1. 在main.py中修改蓝球权重调整逻辑")
    print("2. 降低上期蓝球的权重加成")
    print("3. 使用平方根衰减代替线性衰减")
    print("4. 增加随机种子的变化")

if __name__ == '__main__':
    import math
    main()