#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级高性能双色球预测算法
目标：红球命中率 >= 3/6，蓝球命中率 >= 5/16 (31.25%)
采用多重集成学习、深度模式挖掘和自适应权重优化
"""

import json
import numpy as np
from collections import defaultdict, Counter
import random
from datetime import datetime
import itertools
from scipy import stats
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class UltraAdvancedSSQPredictor:
    def __init__(self, data):
        self.data = data
        self.red_balls = []
        self.blue_balls = []
        self.periods = []
        self.parse_data()
        
        # 高级分析结果缓存
        self.freq_analysis = None
        self.pattern_analysis = None
        self.ml_features = None
        
    def parse_data(self):
        """解析历史数据"""
        for item in self.data:
            red_str = item['number']
            blue_str = item['refernumber']
            
            red_nums = [int(x) for x in red_str.split()]
            self.red_balls.append(sorted(red_nums))
            self.blue_balls.append(int(blue_str))
            self.periods.append(item['issueno'])
    
    def deep_frequency_analysis(self):
        """深度频率分析"""
        if self.freq_analysis is not None:
            return self.freq_analysis
            
        analysis = {
            'red_freq': Counter(),
            'blue_freq': Counter(),
            'red_position_freq': [Counter() for _ in range(6)],
            'red_pair_freq': Counter(),
            'red_triple_freq': Counter(),
            'blue_after_red_sum': defaultdict(list),
            'red_after_blue': defaultdict(list),
            'consecutive_patterns': [],
            'gap_patterns': defaultdict(list)
        }
        
        # 基础频率统计
        for i, (red_combo, blue) in enumerate(zip(self.red_balls, self.blue_balls)):
            # 红球频率
            for num in red_combo:
                analysis['red_freq'][num] += 1
            
            # 蓝球频率
            analysis['blue_freq'][blue] += 1
            
            # 位置频率
            for pos, num in enumerate(red_combo):
                analysis['red_position_freq'][pos][num] += 1
            
            # 红球配对频率
            for pair in itertools.combinations(red_combo, 2):
                analysis['red_pair_freq'][pair] += 1
            
            # 红球三元组频率
            for triple in itertools.combinations(red_combo, 3):
                analysis['red_triple_freq'][triple] += 1
            
            # 蓝球与红球和值关系
            red_sum = sum(red_combo)
            analysis['blue_after_red_sum'][red_sum].append(blue)
            
            # 红球与前期蓝球关系
            if i > 0:
                prev_blue = self.blue_balls[i-1]
                analysis['red_after_blue'][prev_blue].extend(red_combo)
        
        # 连号模式分析
        for red_combo in self.red_balls:
            consecutive_groups = []
            current_group = [red_combo[0]]
            
            for i in range(1, len(red_combo)):
                if red_combo[i] - red_combo[i-1] == 1:
                    current_group.append(red_combo[i])
                else:
                    if len(current_group) > 1:
                        consecutive_groups.append(current_group)
                    current_group = [red_combo[i]]
            
            if len(current_group) > 1:
                consecutive_groups.append(current_group)
            
            analysis['consecutive_patterns'].append(consecutive_groups)
        
        # 间隔模式分析
        for num in range(1, 34):
            appearances = []
            for i, red_combo in enumerate(self.red_balls):
                if num in red_combo:
                    appearances.append(i)
            
            gaps = []
            for i in range(1, len(appearances)):
                gaps.append(appearances[i] - appearances[i-1])
            
            if gaps:
                analysis['gap_patterns'][num] = gaps
        
        self.freq_analysis = analysis
        return analysis
    
    def advanced_pattern_mining(self):
        """高级模式挖掘"""
        if self.pattern_analysis is not None:
            return self.pattern_analysis
            
        patterns = {
            'hot_cold_cycles': {},
            'seasonal_patterns': {},
            'trend_analysis': {},
            'correlation_matrix': {},
            'fibonacci_patterns': [],
            'prime_patterns': [],
            'mathematical_sequences': []
        }
        
        # 热冷号周期分析
        window_size = 50
        for num in range(1, 34):
            hot_periods = []
            cold_periods = []
            
            for i in range(0, len(self.red_balls) - window_size, 10):
                window_data = self.red_balls[i:i+window_size]
                count = sum(1 for combo in window_data if num in combo)
                
                if count >= window_size * 0.2:  # 热号阈值
                    hot_periods.append(i)
                elif count <= window_size * 0.05:  # 冷号阈值
                    cold_periods.append(i)
            
            patterns['hot_cold_cycles'][num] = {
                'hot_periods': hot_periods,
                'cold_periods': cold_periods
            }
        
        # 趋势分析
        recent_periods = min(100, len(self.red_balls))
        for num in range(1, 34):
            recent_appearances = []
            for i in range(recent_periods):
                combo = self.red_balls[-(i+1)]
                if num in combo:
                    recent_appearances.append(recent_periods - i)
            
            if len(recent_appearances) >= 3:
                # 计算趋势斜率
                x = np.arange(len(recent_appearances))
                y = np.array(recent_appearances)
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
                patterns['trend_analysis'][num] = {
                    'slope': slope,
                    'r_value': r_value,
                    'trend': 'increasing' if slope > 0 else 'decreasing'
                }
        
        # 数字相关性矩阵
        correlation_matrix = np.zeros((33, 33))
        for i in range(1, 34):
            for j in range(1, 34):
                if i != j:
                    co_occurrences = 0
                    total_i = 0
                    for combo in self.red_balls:
                        if i in combo:
                            total_i += 1
                            if j in combo:
                                co_occurrences += 1
                    
                    if total_i > 0:
                        correlation_matrix[i-1][j-1] = co_occurrences / total_i
        
        patterns['correlation_matrix'] = correlation_matrix
        
        # 斐波那契数列模式
        fib_numbers = [1, 1, 2, 3, 5, 8, 13, 21]
        for combo in self.red_balls:
            fib_count = sum(1 for num in combo if num in fib_numbers)
            patterns['fibonacci_patterns'].append(fib_count)
        
        # 质数模式
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]
        for combo in self.red_balls:
            prime_count = sum(1 for num in combo if num in primes)
            patterns['prime_patterns'].append(prime_count)
        
        self.pattern_analysis = patterns
        return patterns
    
    def build_ml_features(self):
        """构建机器学习特征"""
        if self.ml_features is not None:
            return self.ml_features
            
        features = []
        targets_red = []
        targets_blue = []
        
        window_size = 20
        
        for i in range(window_size, len(self.red_balls)):
            feature_vector = []
            
            # 历史窗口特征
            window_data = self.red_balls[i-window_size:i]
            window_blues = self.blue_balls[i-window_size:i]
            
            # 红球频率特征
            red_freq = Counter()
            for combo in window_data:
                for num in combo:
                    red_freq[num] += 1
            
            for num in range(1, 34):
                feature_vector.append(red_freq[num])
            
            # 蓝球频率特征
            blue_freq = Counter(window_blues)
            for num in range(1, 17):
                feature_vector.append(blue_freq[num])
            
            # 和值特征
            recent_sums = [sum(combo) for combo in window_data[-5:]]
            feature_vector.extend([
                np.mean(recent_sums),
                np.std(recent_sums),
                max(recent_sums),
                min(recent_sums)
            ])
            
            # 跨度特征
            recent_spans = [max(combo) - min(combo) for combo in window_data[-5:]]
            feature_vector.extend([
                np.mean(recent_spans),
                np.std(recent_spans)
            ])
            
            # 奇偶特征
            recent_odds = [sum(1 for x in combo if x % 2 == 1) for combo in window_data[-5:]]
            feature_vector.extend([
                np.mean(recent_odds),
                np.std(recent_odds)
            ])
            
            # 连号特征
            recent_consecutives = []
            for combo in window_data[-5:]:
                consecutive_count = 0
                for j in range(len(combo) - 1):
                    if combo[j+1] - combo[j] == 1:
                        consecutive_count += 1
                recent_consecutives.append(consecutive_count)
            
            feature_vector.extend([
                np.mean(recent_consecutives),
                np.std(recent_consecutives)
            ])
            
            # 蓝球趋势特征
            feature_vector.extend([
                np.mean(window_blues[-5:]),
                np.std(window_blues[-5:]),
                max(window_blues[-5:]),
                min(window_blues[-5:])
            ])
            
            features.append(feature_vector)
            targets_red.append(self.red_balls[i])
            targets_blue.append(self.blue_balls[i])
        
        self.ml_features = {
            'features': np.array(features),
            'targets_red': targets_red,
            'targets_blue': np.array(targets_blue)
        }
        
        return self.ml_features
    
    def ensemble_predict(self):
        """集成预测算法"""
        freq_analysis = self.deep_frequency_analysis()
        pattern_analysis = self.advanced_pattern_mining()
        ml_features = self.build_ml_features()
        
        # 多种预测方法
        predictions = []
        
        # 方法1: 高级频率加权预测
        pred1 = self.frequency_weighted_predict(freq_analysis)
        predictions.append(pred1)
        
        # 方法2: 模式识别预测
        pred2 = self.pattern_based_predict(pattern_analysis, freq_analysis)
        predictions.append(pred2)
        
        # 方法3: 机器学习预测
        pred3 = self.ml_based_predict(ml_features)
        predictions.append(pred3)
        
        # 方法4: 数学序列预测
        pred4 = self.mathematical_sequence_predict(pattern_analysis)
        predictions.append(pred4)
        
        # 方法5: 相关性预测
        pred5 = self.correlation_based_predict(pattern_analysis, freq_analysis)
        predictions.append(pred5)
        
        # 集成所有预测结果
        final_prediction = self.ensemble_predictions(predictions)
        
        return final_prediction
    
    def frequency_weighted_predict(self, freq_analysis):
        """频率加权预测"""
        # 计算综合权重
        red_weights = {}
        for num in range(1, 34):
            weight = 0
            
            # 历史频率权重
            total_freq = sum(freq_analysis['red_freq'].values())
            freq_weight = freq_analysis['red_freq'][num] / total_freq
            weight += freq_weight * 0.3
            
            # 位置权重
            position_weight = 0
            for pos_freq in freq_analysis['red_position_freq']:
                if num in pos_freq:
                    pos_total = sum(pos_freq.values())
                    position_weight += pos_freq[num] / pos_total if pos_total > 0 else 0
            weight += (position_weight / 6) * 0.2
            
            # 配对权重
            pair_weight = 0
            for pair, count in freq_analysis['red_pair_freq'].items():
                if num in pair:
                    pair_weight += count
            total_pairs = sum(freq_analysis['red_pair_freq'].values())
            weight += (pair_weight / total_pairs) * 0.2 if total_pairs > 0 else 0
            
            # 间隔权重
            if num in freq_analysis['gap_patterns']:
                gaps = freq_analysis['gap_patterns'][num]
                if gaps:
                    avg_gap = np.mean(gaps)
                    last_appearance = -1
                    for i, combo in enumerate(reversed(self.red_balls)):
                        if num in combo:
                            last_appearance = i
                            break
                    
                    if last_appearance != -1:
                        gap_score = min(1.0, last_appearance / avg_gap) if avg_gap > 0 else 0.5
                        weight += gap_score * 0.3
            
            red_weights[num] = weight
        
        # 选择红球
        sorted_reds = sorted(red_weights.items(), key=lambda x: x[1], reverse=True)
        selected_reds = [num for num, _ in sorted_reds[:6]]
        
        # 蓝球预测
        blue_weights = {}
        for num in range(1, 17):
            weight = freq_analysis['blue_freq'][num] / len(self.blue_balls)
            
            # 与红球和值关系
            red_sum = sum(selected_reds)
            if red_sum in freq_analysis['blue_after_red_sum']:
                blues_after_sum = freq_analysis['blue_after_red_sum'][red_sum]
                if blues_after_sum:
                    sum_weight = blues_after_sum.count(num) / len(blues_after_sum)
                    weight += sum_weight * 0.5
            
            blue_weights[num] = weight
        
        selected_blue = max(blue_weights.items(), key=lambda x: x[1])[0]
        
        return sorted(selected_reds), selected_blue
    
    def pattern_based_predict(self, pattern_analysis, freq_analysis):
        """基于模式的预测"""
        red_scores = defaultdict(float)
        
        # 热冷号周期分析
        for num, cycles in pattern_analysis['hot_cold_cycles'].items():
            current_period = len(self.red_balls)
            
            # 检查是否处于热号周期
            for hot_period in cycles['hot_periods']:
                if abs(current_period - hot_period) < 50:
                    red_scores[num] += 0.3
            
            # 检查是否刚结束冷号周期
            for cold_period in cycles['cold_periods']:
                if abs(current_period - cold_period) < 30:
                    red_scores[num] += 0.4
        
        # 趋势分析
        for num, trend_info in pattern_analysis['trend_analysis'].items():
            if trend_info['trend'] == 'increasing' and trend_info['r_value'] > 0.5:
                red_scores[num] += 0.3
        
        # 相关性分析
        correlation_matrix = pattern_analysis['correlation_matrix']
        recent_combo = self.red_balls[-1]
        
        for num in range(1, 34):
            correlation_score = 0
            for recent_num in recent_combo:
                correlation_score += correlation_matrix[num-1][recent_num-1]
            red_scores[num] += correlation_score * 0.2
        
        # 数学模式加分
        fib_avg = np.mean(pattern_analysis['fibonacci_patterns'][-20:])
        prime_avg = np.mean(pattern_analysis['prime_patterns'][-20:])
        
        fib_numbers = [1, 1, 2, 3, 5, 8, 13, 21]
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]
        
        for num in fib_numbers:
            if num <= 33:
                red_scores[num] += 0.1 * (fib_avg / 6)
        
        for num in primes:
            if num <= 33:
                red_scores[num] += 0.1 * (prime_avg / 6)
        
        # 选择得分最高的6个红球
        sorted_reds = sorted(red_scores.items(), key=lambda x: x[1], reverse=True)
        selected_reds = [num for num, _ in sorted_reds[:6]]
        
        # 蓝球预测基于最近趋势
        recent_blues = self.blue_balls[-10:]
        blue_trend = np.mean(recent_blues)
        
        # 选择接近趋势值的蓝球
        blue_candidates = list(range(1, 17))
        selected_blue = min(blue_candidates, key=lambda x: abs(x - blue_trend))
        
        return sorted(selected_reds), selected_blue
    
    def ml_based_predict(self, ml_features):
        """基于机器学习的预测"""
        if len(ml_features['features']) < 50:
            # 数据不足，返回随机预测
            return sorted(random.sample(range(1, 34), 6)), random.randint(1, 16)
        
        features = ml_features['features']
        targets_blue = ml_features['targets_blue']
        
        # 训练蓝球预测模型
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features[:-1])
        
        rf_blue = RandomForestRegressor(n_estimators=100, random_state=42)
        rf_blue.fit(features_scaled, targets_blue[1:])
        
        # 预测蓝球
        last_features = scaler.transform(features[-1:])  
        predicted_blue = int(round(rf_blue.predict(last_features)[0]))
        predicted_blue = max(1, min(16, predicted_blue))
        
        # 红球预测使用特征重要性
        feature_importance = rf_blue.feature_importances_[:33]  # 前33个特征是红球频率
        
        # 结合重要性和最近频率
        red_scores = {}
        recent_window = self.red_balls[-20:]
        recent_freq = Counter()
        for combo in recent_window:
            for num in combo:
                recent_freq[num] += 1
        
        for num in range(1, 34):
            importance_score = feature_importance[num-1]
            frequency_score = recent_freq[num] / len(recent_window)
            red_scores[num] = importance_score * 0.6 + frequency_score * 0.4
        
        sorted_reds = sorted(red_scores.items(), key=lambda x: x[1], reverse=True)
        selected_reds = [num for num, _ in sorted_reds[:6]]
        
        return sorted(selected_reds), predicted_blue
    
    def mathematical_sequence_predict(self, pattern_analysis):
        """数学序列预测"""
        # 黄金分割比例
        phi = 1.618
        
        # 基于数学规律的预测
        selected_reds = []
        
        # 等差数列模式
        arithmetic_sequences = []
        for d in range(1, 6):  # 公差1-5
            for start in range(1, 34-5*d):
                seq = [start + i*d for i in range(6)]
                if all(x <= 33 for x in seq):
                    arithmetic_sequences.append(seq)
        
        # 选择最符合历史模式的等差数列
        best_seq = None
        best_score = -1
        
        for seq in arithmetic_sequences:
            score = 0
            for combo in self.red_balls[-50:]:  # 检查最近50期
                common = len(set(seq) & set(combo))
                score += common
            
            if score > best_score:
                best_score = score
                best_seq = seq
        
        if best_seq:
            selected_reds = best_seq
        else:
            # 备选方案：黄金分割比例
            selected_reds = []
            for i in range(6):
                num = int((i + 1) * phi * 5) % 33 + 1
                if num not in selected_reds:
                    selected_reds.append(num)
            
            while len(selected_reds) < 6:
                num = random.randint(1, 33)
                if num not in selected_reds:
                    selected_reds.append(num)
        
        # 蓝球使用黄金分割
        recent_blue_avg = np.mean(self.blue_balls[-10:]) if len(self.blue_balls) >= 10 else 8
        predicted_blue = int(recent_blue_avg * phi) % 16 + 1
        
        return sorted(selected_reds), predicted_blue
    
    def correlation_based_predict(self, pattern_analysis, freq_analysis):
        """基于相关性的预测"""
        correlation_matrix = pattern_analysis['correlation_matrix']
        
        # 从最近出现的号码开始
        recent_combo = self.red_balls[-1]
        red_scores = defaultdict(float)
        
        # 基于相关性计算分数
        for num in range(1, 34):
            for recent_num in recent_combo:
                red_scores[num] += correlation_matrix[num-1][recent_num-1]
        
        # 避免选择最近已出现的号码
        for num in recent_combo:
            red_scores[num] *= 0.3
        
        # 选择相关性最高的6个号码
        sorted_reds = sorted(red_scores.items(), key=lambda x: x[1], reverse=True)
        selected_reds = [num for num, _ in sorted_reds[:6]]
        
        # 蓝球基于与红球和值的相关性
        red_sum = sum(selected_reds)
        if red_sum in freq_analysis['blue_after_red_sum']:
            blues_after_sum = freq_analysis['blue_after_red_sum'][red_sum]
            if blues_after_sum:
                selected_blue = max(set(blues_after_sum), key=blues_after_sum.count)
            else:
                selected_blue = random.randint(1, 16)
        else:
            selected_blue = random.randint(1, 16)
        
        return sorted(selected_reds), selected_blue
    
    def ensemble_predictions(self, predictions):
        """集成多个预测结果"""
        # 红球投票机制
        red_votes = defaultdict(int)
        blue_votes = defaultdict(int)
        
        weights = [0.25, 0.2, 0.2, 0.15, 0.2]  # 各方法权重
        
        for i, (red_pred, blue_pred) in enumerate(predictions):
            weight = weights[i]
            
            for num in red_pred:
                red_votes[num] += weight
            
            blue_votes[blue_pred] += weight
        
        # 选择得票最高的红球
        sorted_red_votes = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)
        final_reds = [num for num, _ in sorted_red_votes[:6]]
        
        # 选择得票最高的蓝球
        final_blue = max(blue_votes.items(), key=lambda x: x[1])[0]
        
        return sorted(final_reds), final_blue

def load_data():
    """加载历史数据"""
    try:
        with open('ssq_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("错误：未找到 ssq_data.json 文件")
        return []

def parse_actual_numbers(red_str, blue_str):
    """解析实际开奖号码"""
    red_nums = [int(x) for x in red_str.split()]
    blue_num = int(blue_str)
    return sorted(red_nums), blue_num

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def ultra_advanced_backtest():
    """超级高性能回测"""
    print("=== 超级高性能双色球预测算法回测 ===")
    print("目标：红球命中率 >= 3/6，蓝球命中率 >= 31.25%")
    print()
    
    data = load_data()
    if len(data) < 500:
        print(f"警告：数据不足500期，当前只有 {len(data)} 期数据")
        return
    
    print(f"加载了 {len(data)} 期历史数据")
    
    test_periods = 200  # 测试200期
    training_periods = 400  # 使用400期数据进行训练
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    
    print(f"开始回测最近 {test_periods} 期数据...")
    print()
    
    for i in range(test_periods):
        start_idx = max(0, len(data) - test_periods + i - training_periods)
        end_idx = len(data) - test_periods + i
        
        if end_idx <= start_idx:
            continue
            
        training_data = data[start_idx:end_idx]
        test_data = data[len(data) - test_periods + i]
        
        try:
            predictor = UltraAdvancedSSQPredictor(training_data)
            predicted_red, predicted_blue = predictor.ensemble_predict()
            
            actual_red, actual_blue = parse_actual_numbers(test_data['number'], test_data['refernumber'])
            
            red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            results.append({
                'period': test_data['issueno'],
                'predicted_red': predicted_red,
                'predicted_blue': predicted_blue,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'red_hits': red_hits,
                'blue_hit': blue_hit
            })
            
            if (i + 1) % 25 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = blue_hits_total / (i + 1)
                print(f"已完成 {i + 1}/{test_periods} 期回测")
                print(f"当前平均红球命中: {current_red_avg:.2f}/6")
                print(f"当前蓝球命中率: {current_blue_rate:.1%}")
                print()
                
        except Exception as e:
            print(f"期号 {test_data['issueno']} 预测失败: {e}")
            continue
    
    # 统计结果
    print("=== 超级算法回测结果统计 ===")
    
    avg_red_hits = red_hits_total / len(results) if results else 0
    blue_hit_rate = blue_hits_total / len(results) if results else 0
    
    print(f"平均红球命中数: {avg_red_hits:.2f}/6")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits_total}/{len(results)})")
    print()
    
    # 目标达成情况
    target_red = 3.0
    target_blue = 5/16
    
    print("=== 目标达成情况 ===")
    print(f"红球目标: >= {target_red}/6, 实际: {avg_red_hits:.2f}/6 {'✓' if avg_red_hits >= target_red else '✗'}")
    print(f"蓝球目标: >= {target_blue:.1%}, 实际: {blue_hit_rate:.1%} {'✓' if blue_hit_rate >= target_blue else '✗'}")
    
    return results

if __name__ == "__main__":
    try:
        results = ultra_advanced_backtest()
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请安装: pip install scikit-learn scipy")