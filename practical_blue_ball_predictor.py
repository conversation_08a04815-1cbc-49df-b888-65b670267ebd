#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实用蓝球预测器
基于统计学和实际规律的蓝球预测系统
目标：蓝球命中率≥10%

核心策略：
1. 冷热号分析
2. 间隔周期预测
3. 分布均衡原理
4. 历史重现模式
5. 概率权重优化
6. 多策略集成
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class PracticalBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化实用蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 策略权重
        self.strategy_weights = {
            'cold_hot_analysis': 0.25,      # 冷热号分析
            'interval_prediction': 0.20,    # 间隔预测
            'distribution_balance': 0.15,   # 分布均衡
            'historical_pattern': 0.15,     # 历史模式
            'probability_weight': 0.15,     # 概率权重
            'trend_following': 0.10         # 趋势跟随
        }
        
        # 预测历史
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0
        }
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def analyze_cold_hot_numbers(self, recent_periods=50):
        """分析冷热号码"""
        if not self.data:
            return {'cold': [], 'hot': [], 'normal': list(self.blue_range)}
        
        # 获取最近期数的蓝球
        recent_blues = [entry['blue'] for entry in self.data[-recent_periods:]]
        frequency = Counter(recent_blues)
        
        # 计算平均出现次数
        avg_frequency = len(recent_blues) / 16
        
        cold_numbers = []  # 冷号：出现次数 < 平均次数 * 0.7
        hot_numbers = []   # 热号：出现次数 > 平均次数 * 1.3
        normal_numbers = [] # 正常号：介于两者之间
        
        for ball in self.blue_range:
            freq = frequency[ball]
            if freq < avg_frequency * 0.7:
                cold_numbers.append(ball)
            elif freq > avg_frequency * 1.3:
                hot_numbers.append(ball)
            else:
                normal_numbers.append(ball)
        
        return {
            'cold': cold_numbers,
            'hot': hot_numbers,
            'normal': normal_numbers,
            'frequency': frequency
        }
    
    def calculate_interval_scores(self):
        """计算间隔得分"""
        if not self.data:
            return {ball: 0.5 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        interval_scores = {}
        
        for ball in self.blue_range:
            # 找到该号码的所有出现位置
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) < 2:
                interval_scores[ball] = 0.5
                continue
            
            # 计算间隔
            intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
            avg_interval = sum(intervals) / len(intervals)
            
            # 计算当前间隔
            last_position = positions[-1]
            current_interval = len(blue_sequence) - 1 - last_position
            
            # 间隔得分：当前间隔越接近平均间隔，得分越高
            if avg_interval > 0:
                score = 1.0 - abs(current_interval - avg_interval) / (avg_interval + 10)
                score = max(0.1, min(1.0, score))
            else:
                score = 0.5
            
            interval_scores[ball] = score
        
        return interval_scores
    
    def analyze_distribution_balance(self):
        """分析分布均衡"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        total_count = len(blue_sequence)
        
        # 计算每个号码的理论出现次数
        expected_count = total_count / 16
        
        balance_scores = {}
        frequency = Counter(blue_sequence)
        
        for ball in self.blue_range:
            actual_count = frequency[ball]
            
            # 计算偏差
            deviation = abs(actual_count - expected_count)
            
            # 偏差越小，得分越高
            score = 1.0 / (1.0 + deviation / expected_count)
            balance_scores[ball] = score
        
        return balance_scores
    
    def find_historical_patterns(self, pattern_length=3):
        """寻找历史模式"""
        if not self.data or len(self.data) < pattern_length + 1:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        
        # 获取最近的模式
        recent_pattern = tuple(blue_sequence[-(pattern_length-1):])
        
        # 在历史中寻找相同模式
        pattern_followers = []
        
        for i in range(len(blue_sequence) - pattern_length):
            historical_pattern = tuple(blue_sequence[i:i+pattern_length-1])
            if historical_pattern == recent_pattern:
                next_ball = blue_sequence[i+pattern_length-1]
                pattern_followers.append(next_ball)
        
        # 计算模式得分
        pattern_scores = {ball: 0.0 for ball in self.blue_range}
        
        if pattern_followers:
            follower_count = Counter(pattern_followers)
            total_followers = len(pattern_followers)
            
            for ball in self.blue_range:
                pattern_scores[ball] = follower_count[ball] / total_followers
        else:
            # 如果没有找到模式，使用均匀分布
            for ball in self.blue_range:
                pattern_scores[ball] = 1.0 / 16
        
        return pattern_scores
    
    def calculate_probability_weights(self):
        """计算概率权重"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        
        # 基于频率的概率权重
        prob_weights = {}
        
        for ball in self.blue_range:
            # 实际概率
            actual_prob = frequency[ball] / total_count
            
            # 理论概率
            theoretical_prob = 1.0 / 16
            
            # 权重：实际概率与理论概率的比值
            weight = actual_prob / theoretical_prob
            prob_weights[ball] = weight
        
        return prob_weights
    
    def analyze_trend_following(self, trend_periods=10):
        """分析趋势跟随"""
        if not self.data or len(self.data) < trend_periods:
            return {ball: 0.5 for ball in self.blue_range}
        
        recent_blues = [entry['blue'] for entry in self.data[-trend_periods:]]
        
        # 计算趋势得分
        trend_scores = {}
        
        for ball in self.blue_range:
            # 计算在趋势期内的出现次数
            count_in_trend = recent_blues.count(ball)
            
            # 计算趋势强度
            if count_in_trend == 0:
                # 未出现的号码给予较高分数（反转策略）
                trend_scores[ball] = 0.8
            elif count_in_trend == 1:
                # 出现一次的号码给予中等分数
                trend_scores[ball] = 0.6
            elif count_in_trend >= 2:
                # 出现多次的号码给予较低分数
                trend_scores[ball] = 0.3
            else:
                trend_scores[ball] = 0.5
        
        return trend_scores
    
    def comprehensive_score_calculation(self):
        """综合得分计算"""
        # 获取各策略得分
        cold_hot = self.analyze_cold_hot_numbers()
        interval_scores = self.calculate_interval_scores()
        balance_scores = self.analyze_distribution_balance()
        pattern_scores = self.find_historical_patterns()
        prob_weights = self.calculate_probability_weights()
        trend_scores = self.analyze_trend_following()
        
        # 计算综合得分
        final_scores = {}
        
        for ball in self.blue_range:
            # 冷热号得分
            if ball in cold_hot['cold']:
                cold_hot_score = 0.8  # 冷号给高分
            elif ball in cold_hot['hot']:
                cold_hot_score = 0.3  # 热号给低分
            else:
                cold_hot_score = 0.6  # 正常号给中等分
            
            # 综合得分
            total_score = (
                cold_hot_score * self.strategy_weights['cold_hot_analysis'] +
                interval_scores[ball] * self.strategy_weights['interval_prediction'] +
                balance_scores[ball] * self.strategy_weights['distribution_balance'] +
                pattern_scores[ball] * self.strategy_weights['historical_pattern'] +
                prob_weights[ball] * self.strategy_weights['probability_weight'] +
                trend_scores[ball] * self.strategy_weights['trend_following']
            )
            
            final_scores[ball] = total_score
        
        return final_scores
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 计算综合得分
        scores = self.comprehensive_score_calculation()
        
        # 多种选择策略
        predictions = []
        
        # 策略1：直接选择最高分
        best_ball = max(scores.items(), key=lambda x: x[1])[0]
        predictions.append(best_ball)
        
        # 策略2：概率采样
        total_score = sum(scores.values())
        if total_score > 0:
            probs = [scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 策略3：前三名随机选择
        top_3 = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        top_3_ball = random.choice(top_3)[0]
        predictions.append(top_3_ball)
        
        # 策略4：冷号优先
        cold_hot = self.analyze_cold_hot_numbers()
        if cold_hot['cold']:
            cold_scores = {ball: scores[ball] for ball in cold_hot['cold']}
            best_cold = max(cold_scores.items(), key=lambda x: x[1])[0]
            predictions.append(best_cold)
        
        # 投票决策
        vote_count = Counter(predictions)
        final_prediction = vote_count.most_common(1)[0][0]
        
        # 记录预测
        self.prediction_history.append({
            'prediction': final_prediction,
            'scores': scores,
            'timestamp': datetime.now().isoformat()
        })
        
        return final_prediction
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        if predicted == actual:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
    
    def get_performance_report(self):
        """获取性能报告"""
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'hit_rate': self.performance_stats['hit_rate'],
            'strategy_weights': self.strategy_weights.copy()
        }
    
    def get_detailed_analysis(self):
        """获取详细分析"""
        if not self.data:
            return {}
        
        cold_hot = self.analyze_cold_hot_numbers()
        interval_scores = self.calculate_interval_scores()
        balance_scores = self.analyze_distribution_balance()
        pattern_scores = self.find_historical_patterns()
        
        return {
            'cold_numbers': cold_hot['cold'],
            'hot_numbers': cold_hot['hot'],
            'normal_numbers': cold_hot['normal'],
            'frequency_distribution': dict(cold_hot['frequency']),
            'interval_scores': interval_scores,
            'balance_scores': balance_scores,
            'pattern_scores': pattern_scores,
            'total_periods': len(self.data)
        }

def test_practical_blue_predictor():
    """测试实用蓝球预测器"""
    print("=== 实用蓝球预测器测试 ===")
    
    predictor = PracticalBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 详细分析
    analysis = predictor.get_detailed_analysis()
    print(f"\n=== 详细分析 ===")
    print(f"冷号 (出现较少): {analysis['cold_numbers']}")
    print(f"热号 (出现较多): {analysis['hot_numbers']}")
    print(f"正常号: {analysis['normal_numbers']}")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(100, len(original_data) - 10)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 10 or i % 20 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 10%")
    print(f"达成度: {hit_rate/0.10*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    for key, value in report.items():
        if key == 'strategy_weights':
            print(f"策略权重配置:")
            for weight_key, weight_value in value.items():
                print(f"  {weight_key}: {weight_value:.3f}")
        else:
            print(f"{key}: {value}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测依据
    final_analysis = predictor.get_detailed_analysis()
    print(f"\n=== 预测依据 ===")
    print(f"当前冷号: {final_analysis['cold_numbers']}")
    print(f"当前热号: {final_analysis['hot_numbers']}")
    
    return predictor

if __name__ == "__main__":
    test_practical_blue_predictor()