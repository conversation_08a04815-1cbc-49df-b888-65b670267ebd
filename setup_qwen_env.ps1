# Qwen Code 环境变量快速配置脚本
# 作者：AI助手
# 日期：$(Get-Date -Format 'yyyy-MM-dd')

Write-Host "=== Qwen Code 环境变量配置脚本 ===" -ForegroundColor Cyan
Write-Host ""

# 检查当前环境变量状态
Write-Host "检查当前环境变量状态..." -ForegroundColor Yellow
Write-Host "OPENAI_API_KEY: $env:OPENAI_API_KEY" -ForegroundColor Gray
Write-Host "OPENAI_BASE_URL: $env:OPENAI_BASE_URL" -ForegroundColor Gray
Write-Host "OPENAI_MODEL: $env:OPENAI_MODEL" -ForegroundColor Gray
Write-Host ""

# 设置环境变量
Write-Host "正在设置 Qwen Code 环境变量..." -ForegroundColor Green

try {
    # 设置用户级环境变量（永久生效）
    [System.Environment]::SetEnvironmentVariable('OPENAI_API_KEY', 'sk-bb0127d0f09c412096ce6af3144e5e39', 'User')
    [System.Environment]::SetEnvironmentVariable('OPENAI_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1', 'User')
    [System.Environment]::SetEnvironmentVariable('OPENAI_MODEL', 'qwen-coder-plus', 'User')
    
    # 设置当前会话环境变量（立即生效）
    $env:OPENAI_API_KEY = 'sk-bb0127d0f09c412096ce6af3144e5e39'
    $env:OPENAI_BASE_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
    $env:OPENAI_MODEL = 'qwen-coder-plus'
    
    Write-Host "✓ 环境变量设置成功！" -ForegroundColor Green
} catch {
    Write-Host "✗ 环境变量设置失败：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "验证设置结果：" -ForegroundColor Cyan
Write-Host "OPENAI_API_KEY = $env:OPENAI_API_KEY" -ForegroundColor White
Write-Host "OPENAI_BASE_URL = $env:OPENAI_BASE_URL" -ForegroundColor White
Write-Host "OPENAI_MODEL = $env:OPENAI_MODEL" -ForegroundColor White

Write-Host ""
Write-Host "测试 qwen 命令..." -ForegroundColor Cyan
try {
    $version = qwen --version 2>$null
    if ($version) {
        Write-Host "✓ qwen 命令测试成功！版本：$version" -ForegroundColor Green
    } else {
        Write-Host "✗ qwen 命令未找到或无法执行" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ qwen 命令测试失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "配置完成！" -ForegroundColor Green
Write-Host "注意：用户级环境变量在新终端中自动生效" -ForegroundColor Yellow
Write-Host "现在可以在任意目录使用 qwen 命令了！" -ForegroundColor Cyan
Write-Host ""
Write-Host "使用方法：" -ForegroundColor White
Write-Host "  qwen --version          # 查看版本" -ForegroundColor Gray
Write-Host "  echo \"问题\" | qwen       # 管道输入" -ForegroundColor Gray
Write-Host "  qwen                    # 交互模式" -ForegroundColor Gray
Write-Host ""