import requests
import json
import time

# API配置
url = 'https://api.jisuapi.com/caipiao/history'
appkey = 'eb6cc4f9bf4a23b4'
caipiaoid = '12'

# 存储所有数据
all_data = []
target_count = 1000
max_per_request = 20  # API每次最多返回20条

print(f"开始分批获取福彩3D历史数据，目标：{target_count}期")

# 分批获取数据
for start_pos in range(0, target_count, max_per_request):
    # 计算本次需要获取的数量
    remaining = target_count - len(all_data)
    current_num = min(max_per_request, remaining)
    
    if remaining <= 0:
        break
    
    params = {
        'appkey': appkey,
        'caipiaoid': caipiaoid,
        'start': start_pos,
        'num': current_num
    }
    
    try:
        print(f"正在获取第 {start_pos + 1} 到 {start_pos + current_num} 期数据...")
        response = requests.get(url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 0 and 'result' in data:
                result = data['result']
                
                if 'list' in result and result['list']:
                    batch_data = result['list']
                    all_data.extend(batch_data)
                    
                    print(f"成功获取 {len(batch_data)} 期数据，累计：{len(all_data)} 期")
                    
                    # 如果返回的数据少于请求的数量，说明已经到底了
                    if len(batch_data) < current_num:
                        print("已获取所有可用数据")
                        break
                else:
                    print("本次请求返回空数据，可能已到数据末尾")
                    break
            else:
                print(f"API返回错误：{data.get('msg', '未知错误')}")
                break
        else:
            print(f"HTTP请求失败，状态码：{response.status_code}")
            print(f"响应内容：{response.text[:200]}")
            break
            
    except Exception as e:
        print(f"请求出错：{str(e)}")
        break
    
    # 避免请求过于频繁
    time.sleep(0.2)
    
    # 如果已经获取到目标数量，退出循环
    if len(all_data) >= target_count:
        break

print(f"\n数据获取完成！")
print(f"总共获取：{len(all_data)} 期数据")

if all_data:
    # 截取前1000期数据
    final_data = all_data[:target_count]
    
    # 保存到文件
    output_file = "fc3d_data_1000.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'total_count': len(final_data),
            'data': final_data,
            'latest_issue': final_data[0]['issueno'] if final_data else None,
            'oldest_issue': final_data[-1]['issueno'] if final_data else None
        }, f, ensure_ascii=False, indent=2)
    
    print(f"数据已保存到：{output_file}")
    
    # 显示数据统计
    print(f"\n数据统计：")
    print(f"最新期号：{final_data[0]['issueno']} ({final_data[0]['opendate']})")
    print(f"最旧期号：{final_data[-1]['issueno']} ({final_data[-1]['opendate']})")
    
    # 显示最新10期数据
    print("\n最新10期数据：")
    for i, item in enumerate(final_data[:10]):
        print(f"{i+1:2d}. 期号:{item['issueno']} 日期:{item['opendate']} 号码:{item['number']}")
    
    # 验证8月5日的数据
    print("\n验证8月5日数据：")
    for item in final_data:
        if item['opendate'] == '2025-08-05':
            print(f"找到8月5日数据：期号 {item['issueno']}, 号码 {item['number']}")
            break
    else:
        print("未找到8月5日的数据")
else:
    print("未获取到任何数据")