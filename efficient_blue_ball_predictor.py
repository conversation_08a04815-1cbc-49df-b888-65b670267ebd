#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效蓝球预测器
基于真实彩票规律的简化高效预测系统
目标：蓝球命中率≥12%

核心理念：
1. 简化策略，专注最有效的方法
2. 基于真实数据的统计规律
3. 动态权重调整
4. 智能候选选择
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EfficientBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化高效蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 核心策略权重（基于实际效果优化）
        self.strategy_weights = {
            'smart_gap_prediction': 0.50,    # 智能间隔预测
            'balanced_cold_analysis': 0.30,  # 平衡冷号分析
            'frequency_compensation': 0.20   # 频率补偿
        }
        
        # 预测历史和性能统计
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'recent_hits': [],
            'strategy_success': {strategy: 0 for strategy in self.strategy_weights.keys()}
        }
        
        # 自适应参数
        self.adaptation_threshold = 0.08
        self.weight_adjustment_factor = 0.1
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def smart_gap_prediction(self):
        """智能间隔预测策略"""
        if not self.data or len(self.data) < 50:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            # 找到所有出现位置
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 2:
                # 计算间隔
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                current_gap = len(blue_sequence) - positions[-1]
                
                # 基础统计
                avg_interval = np.mean(intervals)
                median_interval = np.median(intervals)
                max_interval = max(intervals)
                
                score = 0.0
                
                # 1. 基于平均间隔的预测（主要策略）
                if current_gap >= avg_interval * 0.9:
                    gap_ratio = current_gap / avg_interval
                    score += min(4.0, gap_ratio * 1.5)
                
                # 2. 基于中位数间隔的预测
                if current_gap >= median_interval:
                    score += min(2.0, current_gap / median_interval)
                
                # 3. 超长间隔的特殊处理
                if current_gap > max_interval:
                    score += 3.0  # 超历史最大间隔
                elif current_gap >= max_interval * 0.9:
                    score += 2.0
                
                # 4. 间隔稳定性奖励
                if len(intervals) >= 3:
                    std_interval = np.std(intervals)
                    if std_interval < avg_interval * 0.4:  # 间隔相对稳定
                        if abs(current_gap - avg_interval) <= std_interval:
                            score += 1.5
                
                # 5. 最近趋势调整
                if len(intervals) >= 3:
                    recent_intervals = intervals[-3:]
                    recent_avg = np.mean(recent_intervals)
                    
                    if current_gap >= recent_avg * 0.8:
                        score += 1.0
                
                scores[ball] = score
                
            elif len(positions) == 1:
                # 只出现过一次的特殊处理
                current_gap = len(blue_sequence) - positions[0]
                theoretical_avg = len(blue_sequence) / 16
                
                if current_gap > theoretical_avg * 1.5:
                    scores[ball] = 3.5
                elif current_gap > theoretical_avg:
                    scores[ball] = 2.5
                else:
                    scores[ball] = 1.5
            else:
                # 从未出现（理论上不存在）
                scores[ball] = 5.0
        
        return scores
    
    def balanced_cold_analysis(self):
        """平衡冷号分析策略"""
        if not self.data:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 多窗口冷热分析
        analysis_windows = [
            {'size': 16, 'weight': 0.4, 'name': 'short'},
            {'size': 32, 'weight': 0.35, 'name': 'medium'},
            {'size': 64, 'weight': 0.25, 'name': 'long'}
        ]
        
        for window in analysis_windows:
            window_size = window['size']
            weight = window['weight']
            
            if len(blue_sequence) >= window_size:
                recent_blues = blue_sequence[-window_size:]
                frequency = Counter(recent_blues)
                expected_freq = window_size / 16
                
                for ball in self.blue_range:
                    actual_freq = frequency[ball]
                    
                    # 冷号评分策略
                    if actual_freq == 0:
                        # 完全未出现
                        scores[ball] += 4.0 * weight
                    elif actual_freq == 1 and expected_freq > 1.5:
                        # 严重偏少
                        scores[ball] += 3.0 * weight
                    elif actual_freq < expected_freq * 0.6:
                        # 明显偏少
                        scores[ball] += 2.0 * weight
                    elif actual_freq < expected_freq:
                        # 轻微偏少
                        scores[ball] += 1.0 * weight
                    elif actual_freq == expected_freq:
                        # 正常频率
                        scores[ball] += 0.3 * weight
        
        # 连续未出现分析
        for ball in self.blue_range:
            consecutive_miss = 0
            for i in range(min(25, len(blue_sequence))):
                if blue_sequence[-(i+1)] != ball:
                    consecutive_miss += 1
                else:
                    break
            
            # 连续未出现奖励
            if consecutive_miss >= 20:
                scores[ball] += 3.0
            elif consecutive_miss >= 15:
                scores[ball] += 2.0
            elif consecutive_miss >= 10:
                scores[ball] += 1.0
        
        return scores
    
    def frequency_compensation(self):
        """频率补偿策略"""
        if not self.data:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        expected_count = total_count / 16
        
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 计算每个球号的频率偏差
        deviations = {}
        for ball in self.blue_range:
            actual_count = frequency[ball]
            deviation = expected_count - actual_count
            deviations[ball] = deviation
        
        # 找出偏差最大的几个号码
        sorted_deviations = sorted(deviations.items(), key=lambda x: x[1], reverse=True)
        
        for i, (ball, deviation) in enumerate(sorted_deviations):
            if deviation > 0:  # 出现次数少于期望
                # 基础补偿分数
                base_score = min(3.0, deviation / expected_count * 3.0)
                
                # 排名奖励（偏差越大排名越前，奖励越高）
                rank_bonus = max(0, (10 - i) / 10) if i < 10 else 0
                
                scores[ball] = base_score + rank_bonus
                
                # 特殊奖励：严重偏少的号码
                if deviation > expected_count * 0.3:
                    scores[ball] += 1.5
            else:
                # 出现次数多于期望，给予很低分数
                scores[ball] = 0.1
        
        # 最近表现调整
        recent_100 = blue_sequence[-100:] if len(blue_sequence) >= 100 else blue_sequence
        recent_freq = Counter(recent_100)
        recent_expected = len(recent_100) / 16
        
        for ball in self.blue_range:
            recent_actual = recent_freq[ball]
            if recent_actual < recent_expected * 0.5:
                scores[ball] += 1.0  # 最近表现不佳的额外奖励
        
        return scores
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各策略得分
        gap_scores = self.smart_gap_prediction()
        cold_scores = self.balanced_cold_analysis()
        freq_scores = self.frequency_compensation()
        
        # 计算加权总分
        final_scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            final_scores[ball] = (
                gap_scores[ball] * self.strategy_weights['smart_gap_prediction'] +
                cold_scores[ball] * self.strategy_weights['balanced_cold_analysis'] +
                freq_scores[ball] * self.strategy_weights['frequency_compensation']
            )
        
        # 归一化
        max_score = max(final_scores.values()) if final_scores.values() else 1
        if max_score > 0:
            for ball in self.blue_range:
                final_scores[ball] /= max_score
        
        # 智能候选选择策略
        sorted_candidates = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 分析得分分布
        top_score = sorted_candidates[0][1]
        second_score = sorted_candidates[1][1] if len(sorted_candidates) > 1 else 0
        score_gap = top_score - second_score
        
        # 根据得分分布选择候选池大小
        if score_gap > 0.4:  # 有明显的最优选择
            candidate_pool_size = 3
            selection_strategy = 'top_weighted'
        elif score_gap > 0.2:  # 有较明显的优势选择
            candidate_pool_size = 5
            selection_strategy = 'weighted_random'
        else:  # 得分比较接近
            candidate_pool_size = 8
            selection_strategy = 'balanced_random'
        
        candidates = sorted_candidates[:candidate_pool_size]
        
        # 根据选择策略进行预测
        if selection_strategy == 'top_weighted':
            # 70%概率选择第一名，30%概率从前3名随机选择
            if random.random() < 0.7:
                predicted_ball = candidates[0][0]
            else:
                weights = [score for ball, score in candidates]
                if sum(weights) > 0:
                    normalized_weights = [w / sum(weights) for w in weights]
                    predicted_ball = np.random.choice([ball for ball, score in candidates], p=normalized_weights)
                else:
                    predicted_ball = candidates[0][0]
        
        elif selection_strategy == 'weighted_random':
            # 按权重随机选择
            weights = [score for ball, score in candidates]
            if sum(weights) > 0:
                normalized_weights = [w / sum(weights) for w in weights]
                predicted_ball = np.random.choice([ball for ball, score in candidates], p=normalized_weights)
            else:
                predicted_ball = candidates[0][0]
        
        else:  # balanced_random
            # 平衡随机选择（给予每个候选相对平等的机会）
            weights = [score + 0.3 for ball, score in candidates]  # 增加基础权重
            normalized_weights = [w / sum(weights) for w in weights]
            predicted_ball = np.random.choice([ball for ball, score in candidates], p=normalized_weights)
        
        # 记录预测详情
        self.prediction_history.append({
            'prediction': predicted_ball,
            'final_scores': final_scores,
            'strategy_scores': {
                'smart_gap_prediction': gap_scores[predicted_ball],
                'balanced_cold_analysis': cold_scores[predicted_ball],
                'frequency_compensation': freq_scores[predicted_ball]
            },
            'candidates': candidates,
            'selection_strategy': selection_strategy,
            'score_gap': score_gap,
            'timestamp': datetime.now().isoformat()
        })
        
        return predicted_ball
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 记录最近命中情况
        self.performance_stats['recent_hits'].append(1.0 if is_correct else 0.0)
        if len(self.performance_stats['recent_hits']) > 30:
            self.performance_stats['recent_hits'].pop(0)
        
        # 策略成功统计
        if self.prediction_history and is_correct:
            last_prediction = self.prediction_history[-1]
            strategy_contributions = last_prediction['strategy_scores']
            
            # 找出贡献最大的策略
            max_contributor = max(strategy_contributions.items(), key=lambda x: x[1])[0]
            self.performance_stats['strategy_success'][max_contributor] += 1
        
        # 自适应权重调整
        if self.performance_stats['total'] % 20 == 0 and self.performance_stats['total'] > 0:
            self.adaptive_weight_adjustment()
    
    def adaptive_weight_adjustment(self):
        """自适应权重调整"""
        if len(self.performance_stats['recent_hits']) < 10:
            return
        
        recent_hit_rate = np.mean(self.performance_stats['recent_hits'])
        
        # 如果最近表现不佳，调整策略权重
        if recent_hit_rate < self.adaptation_threshold:
            total_success = sum(self.performance_stats['strategy_success'].values())
            
            if total_success > 0:
                # 计算各策略的成功率
                strategy_success_rates = {}
                for strategy, success_count in self.performance_stats['strategy_success'].items():
                    strategy_success_rates[strategy] = success_count / total_success
                
                # 调整权重
                for strategy in self.strategy_weights.keys():
                    success_rate = strategy_success_rates.get(strategy, 0)
                    
                    if success_rate > 0.4:  # 表现好的策略
                        self.strategy_weights[strategy] *= (1 + self.weight_adjustment_factor)
                    elif success_rate < 0.2:  # 表现差的策略
                        self.strategy_weights[strategy] *= (1 - self.weight_adjustment_factor)
                
                # 重新归一化权重
                total_weight = sum(self.strategy_weights.values())
                if total_weight > 0:
                    for strategy in self.strategy_weights.keys():
                        self.strategy_weights[strategy] /= total_weight
    
    def get_performance_report(self):
        """获取性能报告"""
        recent_hit_rate = 0.0
        if self.performance_stats['recent_hits']:
            recent_hit_rate = np.mean(self.performance_stats['recent_hits'])
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'hit_rate': self.performance_stats['hit_rate'],
            'recent_hit_rate': recent_hit_rate,
            'strategy_weights': self.strategy_weights.copy(),
            'strategy_success': self.performance_stats['strategy_success'].copy()
        }

def test_efficient_blue_predictor():
    """测试高效蓝球预测器"""
    print("=== 高效蓝球预测器测试 ===")
    
    predictor = EfficientBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(120, len(original_data) - 80)  # 确保有足够的训练数据
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 5 or i % 30 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    print(f"总预测次数: {report['total_predictions']}")
    print(f"正确预测次数: {report['correct_predictions']}")
    print(f"整体命中率: {report['hit_rate']:.4f}")
    print(f"最近命中率: {report['recent_hit_rate']:.4f}")
    
    print(f"\n策略权重配置:")
    for strategy, weight in report['strategy_weights'].items():
        print(f"  {strategy}: {weight:.3f}")
    
    print(f"\n策略成功次数:")
    for strategy, success in report['strategy_success'].items():
        print(f"  {strategy}: {success}次")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"选择策略: {last_prediction['selection_strategy']}")
        print(f"得分差距: {last_prediction['score_gap']:.4f}")
        
        print(f"\n各策略得分:")
        for strategy, score in last_prediction['strategy_scores'].items():
            print(f"  {strategy}: {score:.4f}")
        
        print(f"\n候选球号:")
        for i, (ball, score) in enumerate(last_prediction['candidates']):
            print(f"  第{i+1}名: 球号{ball}, 得分{score:.4f}")
    
    return predictor

if __name__ == "__main__":
    test_efficient_blue_predictor()