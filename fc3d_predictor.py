import requests
import json
import time
import random
import math
import os
from datetime import datetime
from collections import defaultdict, Counter
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class FC3DPredictor:
    """福彩3D预测器 - 基于高级算法预测3位数字"""
    
    def __init__(self, data=None):
        """初始化福彩3D预测器"""
        self.data = data if data else []
        self.digit_range = list(range(0, 10))  # 每位数字范围0-9
        
        # 神经网络参数
        self.input_dim = 30
        self.hidden_dim = 64
        self.output_dim = 10
        
        # 初始化神经网络权重
        self.init_neural_networks()
        
        # 特征权重（动态调整）
        self.feature_weights = {
            'frequency_analysis': 0.20,
            'position_pattern': 0.18,
            'sequence_learning': 0.16,
            'sum_distribution': 0.14,
            'span_analysis': 0.12,
            'neural_prediction': 0.10,
            'trend_analysis': 0.10
        }
        
        # 预测历史和性能统计
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'exact_match': 0,  # 直选命中
            'group_match': 0,  # 组选命中
            'position_match': 0,  # 位置命中
            'hit_rate': 0.0
        }
        
        # 学习率
        self.learning_rate = 0.01
    
    def init_neural_networks(self):
        """初始化神经网络"""
        # 前馈神经网络权重
        self.ffnn_weights = {
            'hidden1': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'hidden2': np.random.randn(self.hidden_dim, self.hidden_dim) * 0.1,
            'output': np.random.randn(self.hidden_dim, self.output_dim) * 0.1
        }
        
        # 位置特定的权重
        self.position_weights = {
            'hundreds': np.random.randn(self.input_dim, self.output_dim) * 0.1,
            'tens': np.random.randn(self.input_dim, self.output_dim) * 0.1,
            'units': np.random.randn(self.input_dim, self.output_dim) * 0.1
        }
    
    def extract_features(self, sequence_length=30):
        """提取特征向量"""
        if not self.data or len(self.data) < sequence_length:
            return np.zeros(self.input_dim)
        
        recent_data = self.data[-sequence_length:]
        features = []
        
        # 提取每期的3位数字
        numbers = []
        for entry in recent_data:
            if 'number' in entry:
                # 假设number字段包含3位数字，如"123"
                num_str = entry['number'].replace(' ', '').replace('-', '')
                if len(num_str) >= 3:
                    hundreds = int(num_str[0])
                    tens = int(num_str[1]) 
                    units = int(num_str[2])
                    numbers.append([hundreds, tens, units])
        
        if not numbers:
            return np.zeros(self.input_dim)
        
        # 1. 频率特征 (10维) - 0-9每个数字的出现频率
        all_digits = [digit for num in numbers for digit in num]
        frequency = Counter(all_digits)
        freq_features = [frequency[i] / len(all_digits) for i in range(10)]
        features.extend(freq_features)
        
        # 2. 位置特征 (10维) - 各位置数字分布
        pos_features = []
        for pos in range(3):  # 百位、十位、个位
            pos_digits = [num[pos] for num in numbers]
            pos_counter = Counter(pos_digits)
            # 计算该位置的平均值和方差
            pos_mean = np.mean(pos_digits)
            pos_std = np.std(pos_digits) if len(pos_digits) > 1 else 0
            pos_features.extend([pos_mean, pos_std])
        
        # 补充到10维
        while len(pos_features) < 10:
            pos_features.append(0)
        features.extend(pos_features[:10])
        
        # 3. 和值特征 (5维)
        sums = [sum(num) for num in numbers]
        sum_features = [
            np.mean(sums),
            np.std(sums) if len(sums) > 1 else 0,
            np.max(sums),
            np.min(sums),
            len([s for s in sums if 10 <= s <= 20]) / len(sums)  # 中等和值比例
        ]
        features.extend(sum_features)
        
        # 4. 跨度特征 (5维)
        spans = [max(num) - min(num) for num in numbers]
        span_features = [
            np.mean(spans),
            np.std(spans) if len(spans) > 1 else 0,
            np.max(spans),
            np.min(spans),
            len([s for s in spans if s <= 5]) / len(spans)  # 小跨度比例
        ]
        features.extend(span_features)
        
        # 确保特征向量长度为input_dim
        while len(features) < self.input_dim:
            features.append(0)
        
        return np.array(features[:self.input_dim])
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def frequency_analysis(self):
        """频率分析预测"""
        if len(self.data) < 10:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 分析每个位置的数字频率
        position_counters = [Counter(), Counter(), Counter()]
        
        for entry in self.data[-100:]:  # 分析最近100期
            if 'number' in entry:
                num_str = entry['number'].replace(' ', '').replace('-', '')
                if len(num_str) >= 3:
                    for i in range(3):
                        digit = int(num_str[i])
                        position_counters[i][digit] += 1
        
        # 选择每个位置频率最高的数字
        prediction = []
        for counter in position_counters:
            if counter:
                most_common = counter.most_common(3)  # 取前3个高频数字
                weights = [count for digit, count in most_common]
                digits = [digit for digit, count in most_common]
                
                # 加权随机选择
                if weights:
                    total_weight = sum(weights)
                    rand_val = random.uniform(0, total_weight)
                    cumulative = 0
                    for i, weight in enumerate(weights):
                        cumulative += weight
                        if rand_val <= cumulative:
                            prediction.append(digits[i])
                            break
                    else:
                        prediction.append(digits[0])
                else:
                    prediction.append(random.randint(0, 9))
            else:
                prediction.append(random.randint(0, 9))
        
        return prediction
    
    def position_pattern_analysis(self):
        """位置模式分析"""
        if len(self.data) < 20:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 分析相邻期数的位置变化模式
        patterns = []
        for i in range(len(self.data) - 1):
            if i >= len(self.data) - 50:  # 只分析最近50期
                curr_entry = self.data[i]
                next_entry = self.data[i + 1]
                
                if 'number' in curr_entry and 'number' in next_entry:
                    curr_num = curr_entry['number'].replace(' ', '').replace('-', '')
                    next_num = next_entry['number'].replace(' ', '').replace('-', '')
                    
                    if len(curr_num) >= 3 and len(next_num) >= 3:
                        pattern = []
                        for j in range(3):
                            diff = (int(next_num[j]) - int(curr_num[j])) % 10
                            pattern.append(diff)
                        patterns.append(pattern)
        
        if not patterns:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 找到最常见的变化模式
        pattern_counter = Counter([tuple(p) for p in patterns])
        most_common_pattern = pattern_counter.most_common(1)[0][0]
        
        # 基于最新一期应用模式
        if self.data:
            last_entry = self.data[-1]
            if 'number' in last_entry:
                last_num = last_entry['number'].replace(' ', '').replace('-', '')
                if len(last_num) >= 3:
                    prediction = []
                    for i in range(3):
                        new_digit = (int(last_num[i]) + most_common_pattern[i]) % 10
                        prediction.append(new_digit)
                    return prediction
        
        return [random.randint(0, 9) for _ in range(3)]
    
    def neural_prediction(self):
        """神经网络预测"""
        features = self.extract_features()
        
        # 前向传播
        hidden1 = self.relu(np.dot(features, self.ffnn_weights['hidden1']))
        hidden2 = self.relu(np.dot(hidden1, self.ffnn_weights['hidden2']))
        output = self.softmax(np.dot(hidden2, self.ffnn_weights['output']))
        
        # 为每个位置生成预测
        prediction = []
        for pos in range(3):
            pos_output = self.softmax(np.dot(features, self.position_weights[['hundreds', 'tens', 'units'][pos]]))
            predicted_digit = np.argmax(pos_output)
            prediction.append(predicted_digit)
        
        return prediction
    
    def sum_distribution_analysis(self):
        """和值分布分析"""
        if len(self.data) < 20:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 分析历史和值分布
        sums = []
        for entry in self.data[-100:]:
            if 'number' in entry:
                num_str = entry['number'].replace(' ', '').replace('-', '')
                if len(num_str) >= 3:
                    total = sum(int(digit) for digit in num_str[:3])
                    sums.append(total)
        
        if not sums:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 找到最常见的和值范围
        sum_counter = Counter(sums)
        target_sum = sum_counter.most_common(1)[0][0]
        
        # 生成和值接近目标的组合
        attempts = 0
        while attempts < 100:
            prediction = [random.randint(0, 9) for _ in range(3)]
            if abs(sum(prediction) - target_sum) <= 2:
                return prediction
            attempts += 1
        
        return [random.randint(0, 9) for _ in range(3)]
    
    def predict_fc3d(self):
        """综合预测福彩3D号码"""
        if not self.data:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 获取各种预测方法的结果
        predictions = {
            'frequency': self.frequency_analysis(),
            'position_pattern': self.position_pattern_analysis(),
            'neural': self.neural_prediction(),
            'sum_distribution': self.sum_distribution_analysis()
        }
        
        # 投票机制选择最终预测
        final_prediction = []
        for pos in range(3):
            votes = Counter()
            for method, pred in predictions.items():
                weight = self.feature_weights.get(method.replace('_', '_'), 0.25)
                votes[pred[pos]] += weight
            
            # 选择得票最高的数字
            if votes:
                final_prediction.append(votes.most_common(1)[0][0])
            else:
                final_prediction.append(random.randint(0, 9))
        
        return final_prediction
    
    def generate_multiple_predictions(self, count=5):
        """生成多组预测"""
        predictions = []
        
        for i in range(count):
            # 添加随机性
            random.seed(int(time.time() * 1000) + i)
            
            # 调整权重增加多样性
            original_weights = self.feature_weights.copy()
            for key in self.feature_weights:
                self.feature_weights[key] *= (0.8 + random.random() * 0.4)
            
            prediction = self.predict_fc3d()
            predictions.append(prediction)
            
            # 恢复原始权重
            self.feature_weights = original_weights
        
        return predictions
    
    def format_prediction(self, prediction):
        """格式化预测结果"""
        return ''.join(map(str, prediction))
    
    def analyze_prediction_types(self, prediction):
        """分析预测类型（组选、直选等）"""
        digits = list(map(str, prediction))
        unique_digits = set(digits)
        
        if len(unique_digits) == 3:
            return "组选6", "三个数字都不相同"
        elif len(unique_digits) == 2:
            return "组选3", "有两个数字相同"
        else:
            return "豹子号", "三个数字都相同"

# 福彩3D数据获取相关函数
APPKEY = 'eb6cc4f9bf4a23b4'  # API密钥
FC3D_DATA_FILE = 'fc3d_data.json'  # 福彩3D数据文件

def get_fc3d_data_from_api(count=200):
    """从API获取福彩3D数据 - 使用极速数据API"""
    # 使用与双色球相同的API服务商
    url = f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=35&num={min(count, 20)}&start=0'
    
    try:
        response = requests.get(url, timeout=15)
        response.raise_for_status()
        result = response.json()
        
        if result.get('status') == 0:
            # 转换数据格式以匹配原有结构
            lottery_data = []
            for item in result.get('result', {}).get('list', []):
                lottery_data.append({
                    'lottery_id': 'fc3d',
                    'lottery_name': '福彩3D',
                    'lottery_no': item.get('issueno', ''),
                    'lottery_date': item.get('opendate', ''),
                    'lottery_res': item.get('number', ''),
                    'lottery_sale_amount': item.get('saleamount', ''),
                    'lottery_pool_amount': item.get('totalmoney', '')
                })
            return lottery_data
        else:
            print(f"API错误: {result.get('msg', '未知错误')}")
            return []
    except Exception as e:
        print(f"获取数据失败: {str(e)}")
        return []

def load_fc3d_data():
    """加载本地福彩3D数据"""
    try:
        # 首先尝试加载1000期数据文件
        fc3d_1000_file = 'fc3d_data_1000.json'
        if os.path.exists(fc3d_1000_file):
            with open(fc3d_1000_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
                data = file_data['data']  # 获取实际的开奖数据
                print(f"成功加载1000期数据: {len(data)} 期")
                return data
        # 如果1000期数据文件不存在，则使用原始数据文件
        if os.path.exists(FC3D_DATA_FILE):
            with open(FC3D_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载本地数据失败: {str(e)}")
    return []

def save_fc3d_data(data):
    """保存福彩3D数据到本地"""
    try:
        with open(FC3D_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到 {FC3D_DATA_FILE}")
    except Exception as e:
        print(f"保存数据失败: {str(e)}")

def main():
    """主函数"""
    print("=== 福彩3D智能预测系统 ===")
    print("正在加载数据...")
    
    # 尝试加载本地数据
    data = load_fc3d_data()
    
    # 如果本地数据不足，从API获取
    if len(data) < 50:
        print("本地数据不足，正在从API获取...")
        api_data = get_fc3d_data_from_api(200)
        if api_data:
            data = api_data
            save_fc3d_data(data)
        else:
            print("无法获取数据，使用模拟数据进行演示")
            # 生成一些模拟数据用于演示
            data = []
            for i in range(100):
                data.append({
                    'number': f"{random.randint(0,9)}{random.randint(0,9)}{random.randint(0,9)}",
                    'opendate': f"2024-{random.randint(1,12):02d}-{random.randint(1,28):02d}"
                })
    
    print(f"已加载 {len(data)} 期历史数据")
    
    # 显示最新开奖信息
    if data:
        latest = data[0]  # 第一个元素是最新的数据
        number_spaced = latest.get('number', '')
        number_continuous = number_spaced.replace(' ', '')
        print(f"\n📋 最新开奖信息:")
        print(f"   期号: {latest.get('issueno', 'N/A')}")
        print(f"   开奖日期: {latest.get('opendate', 'N/A')}")
        print(f"   开奖号码: {number_spaced} (标准格式)")
        print(f"   开奖号码: {number_continuous} (连续格式)")
    
    # 创建预测器
    predictor = FC3DPredictor(data)
    
    # 生成预测
    print("\n=== 预测结果 ===")
    predictions = predictor.generate_multiple_predictions(5)
    
    for i, pred in enumerate(predictions, 1):
        formatted = predictor.format_prediction(pred)
        pred_type, description = predictor.analyze_prediction_types(pred)
        print(f"预测 {i}: {formatted} ({pred_type} - {description})")
    
    # 显示推荐预测
    best_prediction = predictor.predict_fc3d()
    best_formatted = predictor.format_prediction(best_prediction)
    best_type, best_desc = predictor.analyze_prediction_types(best_prediction)
    
    print(f"\n=== 推荐预测 ===")
    print(f"号码: {best_formatted}")
    print(f"类型: {best_type} - {best_desc}")
    print(f"和值: {sum(best_prediction)}")
    print(f"跨度: {max(best_prediction) - min(best_prediction)}")
    
    print("\n=== 投注建议 ===")
    print(f"直选投注: {best_formatted}")
    if best_type == "组选6":
        # 组选6的所有排列
        from itertools import permutations
        perms = list(permutations(best_prediction))
        perm_strs = [''.join(map(str, p)) for p in perms]
        print(f"组选6投注: {', '.join(perm_strs)}")
    elif best_type == "组选3":
        print(f"组选3投注: 包含数字 {set(best_prediction)} 的所有组合")
    
    print("\n预测完成！祝您好运！")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f'程序运行出错: {str(e)}')
        print("请检查网络连接和API密钥是否正确")