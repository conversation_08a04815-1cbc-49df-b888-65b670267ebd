#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级马尔科夫链双色球预测算法
结合近期号码权重提升、马尔科夫链转移概率和上期重号分析
目标：红球命中率≥1.5/6 (25%)，蓝球命中率≥10%
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np

class AdvancedMarkovPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = []  # 历史红球数据
        self.blue_balls = []  # 历史蓝球数据
        
        # 马尔科夫链转移矩阵
        self.red_transition_matrix = defaultdict(lambda: defaultdict(int))
        self.blue_transition_matrix = defaultdict(lambda: defaultdict(int))
        
        # 近期权重参数
        self.recent_periods = 30  # 近期期数
        self.weight_decay = 0.95  # 权重衰减因子
        
        # 上期重号分析
        self.repeat_probability = {}
        
    def load_data(self, filename='ssq_data.json'):
        """加载双色球历史数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            # 提取红球和蓝球数据
            for item in self.data:
                if 'number' in item and 'refernumber' in item:
                    # 解析红球号码
                    red_str = item['number'].strip()
                    red_numbers = [int(x) for x in red_str.split()]
                    self.red_balls.append(sorted(red_numbers))
                    
                    # 解析蓝球号码
                    blue_number = int(item['refernumber'])
                    self.blue_balls.append(blue_number)
            
            print(f"成功加载 {len(self.data)} 期历史数据")
            print(f"红球数据: {len(self.red_balls)} 期")
            print(f"蓝球数据: {len(self.blue_balls)} 期")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def build_markov_chains(self):
        """构建马尔科夫链转移矩阵"""
        print("构建马尔科夫链转移矩阵...")
        
        # 红球马尔科夫链
        for i in range(1, len(self.red_balls)):
            prev_red = tuple(self.red_balls[i-1])
            curr_red = tuple(self.red_balls[i])
            
            # 计算号码间的转移关系
            for prev_num in prev_red:
                for curr_num in curr_red:
                    self.red_transition_matrix[prev_num][curr_num] += 1
        
        # 蓝球马尔科夫链
        for i in range(1, len(self.blue_balls)):
            prev_blue = self.blue_balls[i-1]
            curr_blue = self.blue_balls[i]
            self.blue_transition_matrix[prev_blue][curr_blue] += 1
        
        print("马尔科夫链构建完成")
    
    def analyze_repeat_probability(self):
        """分析上期重号概率"""
        print("分析上期重号概率...")
        
        if len(self.red_balls) < 2:
            self.repeat_probability = {
                'red_repeat_rate': 0.1,
                'blue_repeat_rate': 0.05,
                'red_repeat_nums': {}
            }
            return
        
        red_repeat_count = 0
        blue_repeat_count = 0
        total_periods = len(self.red_balls) - 1
        
        # 统计红球重号情况
        red_repeat_nums = defaultdict(int)
        for i in range(1, len(self.red_balls)):
            prev_red = set(self.red_balls[i-1])
            curr_red = set(self.red_balls[i])
            repeat_nums = prev_red & curr_red
            
            if repeat_nums:
                red_repeat_count += 1
                for num in repeat_nums:
                    red_repeat_nums[num] += 1
        
        # 统计蓝球重号情况
        for i in range(1, len(self.blue_balls)):
            if self.blue_balls[i-1] == self.blue_balls[i]:
                blue_repeat_count += 1
        
        self.repeat_probability = {
            'red_repeat_rate': red_repeat_count / total_periods if total_periods > 0 else 0.1,
            'blue_repeat_rate': blue_repeat_count / total_periods if total_periods > 0 else 0.05,
            'red_repeat_nums': dict(red_repeat_nums)
        }
        
        print(f"红球重号概率: {self.repeat_probability['red_repeat_rate']:.2%}")
        print(f"蓝球重号概率: {self.repeat_probability['blue_repeat_rate']:.2%}")
    
    def calculate_recent_weights(self, numbers, is_red=True):
        """计算近期号码权重"""
        weights = defaultdict(float)
        recent_data = (self.red_balls if is_red else self.blue_balls)[-self.recent_periods:]
        
        for i, period_data in enumerate(recent_data):
            # 权重随时间衰减，越近期权重越高
            period_weight = self.weight_decay ** (len(recent_data) - 1 - i)
            
            if is_red:
                for num in period_data:
                    weights[num] += period_weight
            else:
                weights[period_data] += period_weight
        
        return weights
    
    def get_markov_probabilities(self, last_numbers, is_red=True):
        """基于马尔科夫链计算转移概率"""
        probabilities = defaultdict(float)
        transition_matrix = self.red_transition_matrix if is_red else self.blue_transition_matrix
        
        if is_red:
            # 红球：基于上期所有红球计算转移概率
            for last_num in last_numbers:
                if last_num in transition_matrix:
                    total_transitions = sum(transition_matrix[last_num].values())
                    if total_transitions > 0:
                        for next_num, count in transition_matrix[last_num].items():
                            probabilities[next_num] += count / total_transitions
        else:
            # 蓝球：基于上期蓝球计算转移概率
            if last_numbers in transition_matrix:
                total_transitions = sum(transition_matrix[last_numbers].values())
                if total_transitions > 0:
                    for next_num, count in transition_matrix[last_numbers].items():
                        probabilities[next_num] = count / total_transitions
        
        return probabilities
    
    def predict_red_balls(self):
        """预测红球号码"""
        if not self.red_balls:
            return random.sample(range(1, 34), 6)
        
        last_red = self.red_balls[-1]
        
        # 1. 近期权重分析
        recent_weights = self.calculate_recent_weights(range(1, 34), is_red=True)
        
        # 2. 马尔科夫链转移概率
        markov_probs = self.get_markov_probabilities(last_red, is_red=True)
        
        # 3. 上期重号概率调整
        repeat_bonus = {}
        if 'red_repeat_nums' in self.repeat_probability:
            for num, count in self.repeat_probability['red_repeat_nums'].items():
                repeat_bonus[num] = count / len(self.red_balls) * 2  # 重号加权
        
        # 4. 综合评分计算
        scores = defaultdict(float)
        for num in range(1, 34):
            # 基础分数：近期权重 (40%)
            scores[num] += recent_weights.get(num, 0) * 0.4
            
            # 马尔科夫转移概率 (35%)
            scores[num] += markov_probs.get(num, 0) * 0.35
            
            # 上期重号加权 (15%)
            if num in last_red:
                scores[num] += self.repeat_probability.get('red_repeat_rate', 0.1) * 0.15
            
            # 重号历史加权 (10%)
            scores[num] += repeat_bonus.get(num, 0) * 0.1
        
        # 5. 智能选择策略
        # 按分数排序，选择前12个候选
        candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:12]
        
        # 从候选中随机选择6个，保持一定随机性
        selected = []
        candidate_nums = [num for num, score in candidates]
        
        # 确保选择的号码分布合理
        while len(selected) < 6:
            if candidate_nums:
                num = random.choice(candidate_nums)
                if num not in selected:
                    selected.append(num)
                candidate_nums.remove(num)
            else:
                # 如果候选不足，从剩余号码中随机选择
                remaining = [i for i in range(1, 34) if i not in selected]
                if remaining:
                    selected.append(random.choice(remaining))
        
        return sorted(selected)
    
    def predict_blue_ball(self):
        """预测蓝球号码"""
        if not self.blue_balls:
            return random.randint(1, 16)
        
        last_blue = self.blue_balls[-1]
        
        # 1. 近期权重分析
        recent_weights = self.calculate_recent_weights(range(1, 17), is_red=False)
        
        # 2. 马尔科夫链转移概率
        markov_probs = self.get_markov_probabilities(last_blue, is_red=False)
        
        # 3. 综合评分计算
        scores = defaultdict(float)
        for num in range(1, 17):
            # 基础分数：近期权重 (50%)
            scores[num] += recent_weights.get(num, 0) * 0.5
            
            # 马尔科夫转移概率 (30%)
            scores[num] += markov_probs.get(num, 0) * 0.3
            
            # 上期重号加权 (20%)
            if num == last_blue:
                scores[num] += self.repeat_probability.get('blue_repeat_rate', 0.05) * 0.2
        
        # 4. 智能选择
        if scores:
            # 按分数排序，从前5个中随机选择
            top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:5]
            weights = [score for num, score in top_candidates]
            
            if sum(weights) > 0:
                # 加权随机选择
                selected_idx = np.random.choice(len(top_candidates), p=np.array(weights)/sum(weights))
                return top_candidates[selected_idx][0]
        
        # 备选方案：随机选择
        return random.randint(1, 16)
    
    def predict(self):
        """生成预测号码"""
        red_prediction = self.predict_red_balls()
        blue_prediction = self.predict_blue_ball()
        
        return {
            'red': red_prediction,
            'blue': blue_prediction,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def advanced_markov_backtest(periods=500):
    """高级马尔科夫算法回测"""
    print("=== 高级马尔科夫链双色球预测算法回测 ===")
    print(f"回测期数: {periods}")
    print(f"目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("="*50)
    
    predictor = AdvancedMarkovPredictor()
    
    if not predictor.load_data():
        return
    
    if len(predictor.data) < periods + 50:
        print(f"数据不足，需要至少 {periods + 50} 期数据")
        return
    
    # 构建马尔科夫链和分析重号概率
    predictor.build_markov_chains()
    predictor.analyze_repeat_probability()
    
    # 回测统计
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    # 使用前面的数据训练，后面的数据测试
    test_start = len(predictor.data) - periods
    
    for i in range(test_start, len(predictor.data)):
        # 使用到当前期之前的数据进行预测
        predictor.red_balls = []
        predictor.blue_balls = []
        
        for item in predictor.data[:i]:
            if 'number' in item and 'refernumber' in item:
                # 解析红球号码
                red_str = item['number'].strip()
                red_numbers = [int(x) for x in red_str.split()]
                predictor.red_balls.append(sorted(red_numbers))
                
                # 解析蓝球号码
                blue_number = int(item['refernumber'])
                predictor.blue_balls.append(blue_number)
        
        # 重新构建模型（使用截断数据）
        predictor.build_markov_chains()
        predictor.analyze_repeat_probability()
        
        # 生成预测
        prediction = predictor.predict()
        actual = predictor.data[i]
        
        # 解析实际开奖号码
        actual_red_str = actual['number'].strip()
        actual_red = [int(x) for x in actual_red_str.split()]
        actual_blue = int(actual['refernumber'])
        
        # 计算红球命中数
        red_hit_count = len(set(prediction['red']) & set(actual_red))
        red_hits.append(red_hit_count)
        
        # 计算蓝球命中
        blue_hit = 1 if prediction['blue'] == actual_blue else 0
        blue_hits += blue_hit
        
        # 记录优秀预测
        if red_hit_count >= 3 or blue_hit:
            best_predictions.append({
                'period': actual.get('issueno', f'第{i+1}期'),
                'red_hit': red_hit_count,
                'blue_hit': blue_hit,
                'predicted_red': prediction['red'],
                'actual_red': actual_red,
                'predicted_blue': prediction['blue'],
                'actual_blue': actual_blue
            })
    
    # 统计结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / periods
    
    print(f"\n=== 回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits}/{periods})")
    
    # 红球命中分布
    red_distribution = Counter(red_hits)
    print(f"\n红球命中分布:")
    for hits in sorted(red_distribution.keys()):
        count = red_distribution[hits]
        percentage = count / periods * 100
        print(f"  {hits}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 0.10
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6 ({red_target/6*100:.1f}%), 实际: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%), 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target:.1%}, 实际: {blue_hit_rate:.1%}, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前{min(20, len(best_predictions))}期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:20], 1):
            blue_mark = "✓" if pred['blue_hit'] else "✗"
            print(f"{i:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_mark}")
            print(f"    预测红球: {pred['predicted_red']}")
            print(f"    实际红球: {pred['actual_red']}")
            print(f"    预测蓝球: {pred['predicted_blue']}, 实际蓝球: {pred['actual_blue']}")
            print()
    
    # 算法优化建议
    print(f"\n=== 算法优化建议 ===")
    if avg_red_hits < red_target:
        print(f"- 红球还需提升 {red_target - avg_red_hits:.2f} 个命中")
        print(f"- 可以调整马尔科夫链权重和近期数据权重")
        print(f"- 优化重号概率分析策略")
    
    if blue_hit_rate < blue_target:
        print(f"- 蓝球还需提升 {(blue_target - blue_hit_rate)*100:.1f}% 命中率")
        print(f"- 可以增强蓝球马尔科夫链分析")
        print(f"- 优化蓝球权重计算方法")
    
    if red_achievement > 85 and blue_achievement > 85:
        print(f"🎉 算法性能优秀，接近目标！")
    elif red_achievement > 70 or blue_achievement > 70:
        print(f"📈 算法性能良好，继续优化中...")
    else:
        print(f"🔧 需要进一步优化算法策略")

if __name__ == "__main__":
    advanced_markov_backtest(500)