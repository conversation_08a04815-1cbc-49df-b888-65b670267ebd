import json
import random
import numpy as np
from collections import defaultdict
from main import markov_predict
from improved_algorithm import smart_predict, analyze_patterns, calculate_weights, advanced_markov_predict

def load_data():
    """加载历史数据"""
    with open('ssq_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def parse_actual_numbers(entry):
    """解析实际开奖号码"""
    red_str = entry['number'].split(' ')
    red = sorted([int(x) for x in red_str])
    blue = int(entry['refernumber'])
    return red, blue

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def comprehensive_backtest(data, test_periods=30):
    """综合回测三种算法"""
    print(f"开始综合回测，测试最近{test_periods}期数据...")
    
    algorithms = {
        '原始马尔科夫': {'func': markov_predict, 'results': []},
        '改进马尔科夫': {'func': lambda d: improved_markov_with_freq(d), 'results': []},
        '智能集成算法': {'func': smart_predict, 'results': []}
    }
    
    for i in range(test_periods, min(len(data), test_periods + test_periods)):
        training_data = data[i:]
        actual_red, actual_blue = parse_actual_numbers(data[i-1])
        
        print(f"\n测试期号: {data[i-1]['issueno']}")
        print(f"实际开奖: {actual_red} + {actual_blue}")
        
        for name, algo in algorithms.items():
            try:
                if name == '改进马尔科夫':
                    patterns = analyze_patterns(training_data)
                    weights = calculate_weights(patterns)
                    predicted_red, predicted_blue = advanced_markov_predict(training_data, patterns, weights)
                else:
                    predicted_red, predicted_blue = algo['func'](training_data)
                
                if predicted_red and predicted_blue:
                    red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
                    
                    result = {
                        'period': data[i-1]['issueno'],
                        'predicted_red': predicted_red,
                        'predicted_blue': predicted_blue,
                        'actual_red': actual_red,
                        'actual_blue': actual_blue,
                        'red_hits': red_hits,
                        'blue_hit': blue_hit
                    }
                    
                    algo['results'].append(result)
                    print(f"{name}: {predicted_red} + {predicted_blue} (红球{red_hits}/6, 蓝球{'✓' if blue_hit else '✗'})")
                else:
                    print(f"{name}: 预测失败")
                    
            except Exception as e:
                print(f"{name}: 预测出错 - {e}")
    
    return algorithms

def improved_markov_with_freq(data):
    """改进的马尔科夫算法（简化版）"""
    patterns = analyze_patterns(data)
    weights = calculate_weights(patterns)
    return advanced_markov_predict(data, patterns, weights)

def analyze_algorithm_performance(algorithms):
    """分析算法性能"""
    print("\n" + "="*60)
    print("算法性能对比分析")
    print("="*60)
    
    for name, algo in algorithms.items():
        results = algo['results']
        if not results:
            continue
            
        print(f"\n{name}:")
        print("-" * 30)
        
        # 基础统计
        total_tests = len(results)
        red_hits_dist = defaultdict(int)
        blue_hits = 0
        total_red_hits = 0
        
        for result in results:
            red_hits_dist[result['red_hits']] += 1
            blue_hits += result['blue_hit']
            total_red_hits += result['red_hits']
        
        # 红球命中分布
        print("红球命中分布:")
        for hits in range(7):
            count = red_hits_dist[hits]
            percentage = (count / total_tests) * 100 if total_tests > 0 else 0
            print(f"  命中{hits}个: {count:2d}次 ({percentage:5.1f}%)")
        
        # 平均命中率
        avg_red_hits = total_red_hits / total_tests if total_tests > 0 else 0
        blue_hit_rate = (blue_hits / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n平均红球命中: {avg_red_hits:.2f}/6")
        print(f"蓝球命中率: {blue_hits}/{total_tests} ({blue_hit_rate:.1f}%)")
        
        # 最佳预测
        best_predictions = sorted(results, key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)[:3]
        print("\n最佳预测:")
        for pred in best_predictions:
            print(f"  期号{pred['period']}: 红球{pred['red_hits']}/6, 蓝球{'✓' if pred['blue_hit'] else '✗'}")
        
        # 计算综合得分（红球命中数 + 蓝球命中*2）
        total_score = sum(r['red_hits'] + r['blue_hit'] * 2 for r in results)
        avg_score = total_score / total_tests if total_tests > 0 else 0
        print(f"\n综合得分: {avg_score:.2f} (红球命中数 + 蓝球命中*2)")

def detailed_pattern_analysis(data):
    """详细模式分析"""
    print("\n" + "="*60)
    print("详细模式分析")
    print("="*60)
    
    patterns = analyze_patterns(data)
    
    # 红球热号冷号分析
    print("\n红球频率分析:")
    sorted_red = sorted(patterns['red_freq'].items(), key=lambda x: x[1], reverse=True)
    
    print("热号 (前10):")
    for i, (num, freq) in enumerate(sorted_red[:10]):
        print(f"  {i+1:2d}. {num:2d}号: {freq:3d}次")
    
    print("\n冷号 (后10):")
    for i, (num, freq) in enumerate(sorted_red[-10:]):
        print(f"  {i+1:2d}. {num:2d}号: {freq:3d}次")
    
    # 蓝球分析
    print("\n蓝球频率分析:")
    sorted_blue = sorted(patterns['blue_freq'].items(), key=lambda x: x[1], reverse=True)
    for i, (num, freq) in enumerate(sorted_blue[:8]):
        print(f"  {i+1:2d}. {num:2d}号: {freq:3d}次")
    
    # 奇偶分析
    print("\n红球奇偶分布:")
    for odd_count, freq in sorted(patterns['odd_even_patterns'].items()):
        print(f"  {odd_count}个奇数: {freq}次")
    
    # 连号分析
    print("\n红球连号分布:")
    for consec_count, freq in sorted(patterns['consecutive_patterns'].items()):
        print(f"  {consec_count}组连号: {freq}次")
    
    # 最近趋势
    print("\n最近100期趋势 (前10):")
    recent_red = sorted(patterns['recent_trends']['red'].items(), key=lambda x: x[1], reverse=True)[:10]
    for num, freq in recent_red:
        print(f"  {num:2d}号: {freq}次")

def generate_recommendations(algorithms):
    """生成算法改进建议"""
    print("\n" + "="*60)
    print("算法改进建议")
    print("="*60)
    
    # 找出表现最好的算法
    best_algo = None
    best_score = -1
    
    for name, algo in algorithms.items():
        if algo['results']:
            total_score = sum(r['red_hits'] + r['blue_hit'] * 2 for r in algo['results'])
            avg_score = total_score / len(algo['results'])
            if avg_score > best_score:
                best_score = avg_score
                best_algo = name
    
    print(f"\n表现最佳算法: {best_algo} (平均得分: {best_score:.2f})")
    
    print("\n改进建议:")
    print("1. 蓝球预测准确率普遍较低，建议:")
    print("   - 增加更多蓝球预测特征（如周期性、季节性）")
    print("   - 考虑蓝球与红球和值的深层关系")
    print("   - 引入机器学习方法（如随机森林、神经网络）")
    
    print("\n2. 红球预测改进方向:")
    print("   - 增加位置权重，不同位置使用不同预测策略")
    print("   - 考虑号码间的相关性和排斥性")
    print("   - 引入时间序列分析方法")
    
    print("\n3. 集成方法优化:")
    print("   - 使用更多样化的基础预测器")
    print("   - 动态调整各算法权重")
    print("   - 引入在线学习机制")

if __name__ == '__main__':
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 加载数据
    data = load_data()
    print(f"加载了{len(data)}期历史数据")
    
    # 进行综合回测
    algorithms = comprehensive_backtest(data, test_periods=20)
    
    # 分析性能
    analyze_algorithm_performance(algorithms)
    
    # 详细模式分析
    detailed_pattern_analysis(data)
    
    # 生成改进建议
    generate_recommendations(algorithms)
    
    print("\n回测分析完成！")