#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级蓝球预测器
基于深度数据挖掘和模式识别的蓝球预测系统
目标：蓝球命中率≥12%

核心策略：
1. 深度间隔模式分析
2. 多维度冷热分析
3. 历史重现模式
4. 概率分布优化
5. 智能候选筛选
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class SuperBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化超级蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 核心策略权重（基于实际效果调整）
        self.strategy_weights = {
            'deep_interval_analysis': 0.40,    # 深度间隔分析
            'multi_cold_hot': 0.25,           # 多维冷热分析
            'historical_pattern': 0.20,       # 历史重现模式
            'probability_optimization': 0.15   # 概率分布优化
        }
        
        # 预测历史
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'strategy_performance': {strategy: {'total': 0, 'correct': 0} for strategy in self.strategy_weights.keys()}
        }
        
        # 模式库
        self.interval_patterns = {}
        self.recurrence_patterns = {}
        
        # 初始化分析
        self.initialize_patterns()
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def initialize_patterns(self):
        """初始化模式分析"""
        if not self.data or len(self.data) < 100:
            return
        
        blue_sequence = [entry['blue'] for entry in self.data]
        
        # 构建间隔模式库
        for ball in self.blue_range:
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 3:
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                # 分析间隔模式
                self.interval_patterns[ball] = {
                    'intervals': intervals,
                    'avg_interval': np.mean(intervals),
                    'std_interval': np.std(intervals),
                    'min_interval': min(intervals),
                    'max_interval': max(intervals),
                    'common_intervals': Counter(intervals).most_common(3),
                    'last_position': positions[-1],
                    'frequency': len(positions)
                }
        
        # 构建重现模式库
        self.build_recurrence_patterns(blue_sequence)
    
    def build_recurrence_patterns(self, blue_sequence):
        """构建重现模式库"""
        # 分析不同长度的序列模式
        for pattern_length in [2, 3, 4]:
            patterns = {}
            
            for i in range(len(blue_sequence) - pattern_length):
                pattern = tuple(blue_sequence[i:i+pattern_length])
                next_ball = blue_sequence[i+pattern_length]
                
                if pattern not in patterns:
                    patterns[pattern] = []
                patterns[pattern].append(next_ball)
            
            # 只保留出现次数>=2的模式
            valid_patterns = {k: v for k, v in patterns.items() if len(v) >= 2}
            self.recurrence_patterns[pattern_length] = valid_patterns
    
    def deep_interval_analysis(self):
        """深度间隔分析策略"""
        if not self.data or len(self.data) < 50:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        current_length = len(blue_sequence)
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            if ball not in self.interval_patterns:
                # 如果没有足够的历史数据，给予中等分数
                scores[ball] = 1.0
                continue
            
            pattern = self.interval_patterns[ball]
            current_gap = current_length - pattern['last_position']
            
            score = 0.0
            
            # 1. 基于平均间隔的预测
            avg_interval = pattern['avg_interval']
            if current_gap >= avg_interval * 0.8:
                score += min(3.0, current_gap / avg_interval)
            
            # 2. 基于常见间隔的预测
            for interval, count in pattern['common_intervals']:
                if abs(current_gap - interval) <= 1:
                    score += count / pattern['frequency'] * 2.0
            
            # 3. 基于标准差的预测
            std_interval = pattern['std_interval']
            if std_interval > 0:
                z_score = (current_gap - avg_interval) / std_interval
                if 0.5 <= z_score <= 2.0:  # 在合理范围内
                    score += z_score * 0.8
            
            # 4. 超长间隔的特殊处理
            max_interval = pattern['max_interval']
            if current_gap > max_interval:
                score += 2.5  # 超历史最大间隔，给予高分
            elif current_gap > max_interval * 0.9:
                score += 1.5
            
            # 5. 频率调整
            total_periods = len(blue_sequence)
            expected_frequency = total_periods / 16
            actual_frequency = pattern['frequency']
            
            if actual_frequency < expected_frequency:
                score *= 1.2  # 出现频率低，增加权重
            
            scores[ball] = score
        
        return scores
    
    def multi_cold_hot_analysis(self):
        """多维冷热分析策略"""
        if not self.data:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 多个时间窗口的冷热分析
        windows = {
            'short': {'size': 12, 'weight': 0.4},
            'medium': {'size': 25, 'weight': 0.35},
            'long': {'size': 50, 'weight': 0.25}
        }
        
        for window_name, config in windows.items():
            window_size = config['size']
            weight = config['weight']
            
            if len(blue_sequence) >= window_size:
                recent_blues = blue_sequence[-window_size:]
                frequency = Counter(recent_blues)
                expected_freq = window_size / 16
                
                for ball in self.blue_range:
                    actual_freq = frequency[ball]
                    
                    # 冷号分析
                    if actual_freq == 0:
                        # 完全未出现
                        scores[ball] += 3.0 * weight
                    elif actual_freq < expected_freq * 0.5:
                        # 严重偏冷
                        scores[ball] += 2.0 * weight
                    elif actual_freq < expected_freq:
                        # 轻微偏冷
                        scores[ball] += 1.0 * weight
                    
                    # 特殊情况：轻微热号的回调
                    elif actual_freq == expected_freq + 1:
                        scores[ball] += 0.3 * weight
        
        # 连续未出现的特殊奖励
        for ball in self.blue_range:
            consecutive_miss = 0
            for i in range(min(20, len(blue_sequence))):
                if blue_sequence[-(i+1)] != ball:
                    consecutive_miss += 1
                else:
                    break
            
            if consecutive_miss >= 15:
                scores[ball] += 2.0
            elif consecutive_miss >= 10:
                scores[ball] += 1.0
        
        return scores
    
    def historical_pattern_analysis(self):
        """历史重现模式分析"""
        if not self.data or len(self.data) < 10:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 分析最近的序列，寻找历史重现模式
        for pattern_length in [2, 3, 4]:
            if len(blue_sequence) >= pattern_length:
                recent_pattern = tuple(blue_sequence[-pattern_length:])
                
                if pattern_length in self.recurrence_patterns:
                    patterns = self.recurrence_patterns[pattern_length]
                    
                    if recent_pattern in patterns:
                        next_balls = patterns[recent_pattern]
                        next_counter = Counter(next_balls)
                        
                        # 根据历史重现频率给分
                        total_occurrences = len(next_balls)
                        for ball, count in next_counter.items():
                            probability = count / total_occurrences
                            weight = 1.0 / pattern_length  # 长模式权重更高
                            scores[ball] += probability * 3.0 * weight
        
        # 周期性模式分析
        self.analyze_periodic_patterns(blue_sequence, scores)
        
        return scores
    
    def analyze_periodic_patterns(self, blue_sequence, scores):
        """分析周期性模式"""
        # 检查是否存在周期性出现的模式
        for ball in self.blue_range:
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 4:
                # 分析间隔的周期性
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                # 寻找重复的间隔模式
                for period in [2, 3, 4, 5]:
                    if len(intervals) >= period * 2:
                        pattern_matches = 0
                        
                        for start in range(len(intervals) - period):
                            pattern1 = intervals[start:start+period]
                            
                            for compare_start in range(start+period, len(intervals)-period+1):
                                pattern2 = intervals[compare_start:compare_start+period]
                                
                                if pattern1 == pattern2:
                                    pattern_matches += 1
                        
                        if pattern_matches >= 2:  # 找到重复模式
                            current_gap = len(blue_sequence) - positions[-1]
                            last_interval = intervals[-1] if intervals else 0
                            
                            # 预测下一个间隔
                            if len(intervals) >= period:
                                expected_next_interval = intervals[-(period-1)] if period > 1 else last_interval
                                
                                if abs(current_gap - expected_next_interval) <= 1:
                                    scores[ball] += 1.5
    
    def probability_optimization(self):
        """概率分布优化策略"""
        if not self.data:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 整体频率分布分析
        total_frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        expected_count = total_count / 16
        
        for ball in self.blue_range:
            actual_count = total_frequency[ball]
            deviation = expected_count - actual_count
            
            # 基于偏差的评分
            if deviation > 0:
                # 出现次数少于期望
                deviation_score = min(2.5, deviation / expected_count * 2.5)
                scores[ball] += deviation_score
                
                # 严重偏少的特殊奖励
                if deviation > expected_count * 0.25:
                    scores[ball] += 1.0
            
            # 最近趋势的概率调整
            recent_30 = blue_sequence[-30:] if len(blue_sequence) >= 30 else blue_sequence
            recent_freq = recent_30.count(ball)
            recent_expected = len(recent_30) / 16
            
            if recent_freq < recent_expected * 0.5:
                scores[ball] += 0.8
        
        # 数字分布均衡性分析
        self.analyze_distribution_balance(blue_sequence, scores)
        
        return scores
    
    def analyze_distribution_balance(self, blue_sequence, scores):
        """分析数字分布均衡性"""
        # 分析奇偶分布
        recent_50 = blue_sequence[-50:] if len(blue_sequence) >= 50 else blue_sequence
        odd_count = sum(1 for ball in recent_50 if ball % 2 == 1)
        even_count = len(recent_50) - odd_count
        
        # 如果奇偶分布不均衡，调整相应数字的分数
        if odd_count < even_count * 0.8:  # 奇数偏少
            for ball in self.blue_range:
                if ball % 2 == 1:
                    scores[ball] += 0.5
        elif even_count < odd_count * 0.8:  # 偶数偏少
            for ball in self.blue_range:
                if ball % 2 == 0:
                    scores[ball] += 0.5
        
        # 分析大小数分布（1-8为小，9-16为大）
        small_count = sum(1 for ball in recent_50 if ball <= 8)
        big_count = len(recent_50) - small_count
        
        if small_count < big_count * 0.8:  # 小数偏少
            for ball in range(1, 9):
                scores[ball] += 0.5
        elif big_count < small_count * 0.8:  # 大数偏少
            for ball in range(9, 17):
                scores[ball] += 0.5
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各策略得分
        deep_interval_scores = self.deep_interval_analysis()
        multi_cold_hot_scores = self.multi_cold_hot_analysis()
        historical_pattern_scores = self.historical_pattern_analysis()
        probability_opt_scores = self.probability_optimization()
        
        # 计算加权总分
        final_scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            final_scores[ball] = (
                deep_interval_scores[ball] * self.strategy_weights['deep_interval_analysis'] +
                multi_cold_hot_scores[ball] * self.strategy_weights['multi_cold_hot'] +
                historical_pattern_scores[ball] * self.strategy_weights['historical_pattern'] +
                probability_opt_scores[ball] * self.strategy_weights['probability_optimization']
            )
        
        # 归一化
        max_score = max(final_scores.values()) if final_scores.values() else 1
        if max_score > 0:
            for ball in self.blue_range:
                final_scores[ball] /= max_score
        
        # 智能候选筛选
        sorted_candidates = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 多层候选策略
        top_3 = sorted_candidates[:3]
        top_6 = sorted_candidates[:6]
        top_10 = sorted_candidates[:10]
        
        # 根据得分差异选择候选池
        if len(top_3) >= 2:
            score_diff = top_3[0][1] - top_3[1][1]
            
            if score_diff > 0.3:  # 得分差异很大，选择前3
                candidates = [ball for ball, score in top_3]
                candidate_weights = [score for ball, score in top_3]
            elif score_diff > 0.15:  # 得分差异中等，选择前6
                candidates = [ball for ball, score in top_6]
                candidate_weights = [score for ball, score in top_6]
            else:  # 得分差异较小，选择前10
                candidates = [ball for ball, score in top_10]
                candidate_weights = [score for ball, score in top_10]
        else:
            candidates = [ball for ball, score in top_6]
            candidate_weights = [score for ball, score in top_6]
        
        # 按权重随机选择
        if sum(candidate_weights) > 0:
            normalized_weights = [w / sum(candidate_weights) for w in candidate_weights]
            predicted_ball = np.random.choice(candidates, p=normalized_weights)
        else:
            predicted_ball = candidates[0] if candidates else random.randint(1, 16)
        
        # 记录预测详情
        self.prediction_history.append({
            'prediction': predicted_ball,
            'final_scores': final_scores,
            'strategy_scores': {
                'deep_interval_analysis': deep_interval_scores[predicted_ball],
                'multi_cold_hot': multi_cold_hot_scores[predicted_ball],
                'historical_pattern': historical_pattern_scores[predicted_ball],
                'probability_optimization': probability_opt_scores[predicted_ball]
            },
            'candidates': [(ball, score) for ball, score in sorted_candidates[:10]],
            'timestamp': datetime.now().isoformat()
        })
        
        return predicted_ball
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 更新策略性能
        if self.prediction_history:
            last_prediction = self.prediction_history[-1]
            strategy_contributions = last_prediction['strategy_scores']
            
            for strategy, score in strategy_contributions.items():
                self.performance_stats['strategy_performance'][strategy]['total'] += 1
                if is_correct and score > 0:
                    self.performance_stats['strategy_performance'][strategy]['correct'] += 1
    
    def get_performance_report(self):
        """获取性能报告"""
        strategy_hit_rates = {}
        for strategy, stats in self.performance_stats['strategy_performance'].items():
            if stats['total'] > 0:
                strategy_hit_rates[strategy] = stats['correct'] / stats['total']
            else:
                strategy_hit_rates[strategy] = 0.0
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'hit_rate': self.performance_stats['hit_rate'],
            'strategy_weights': self.strategy_weights.copy(),
            'strategy_hit_rates': strategy_hit_rates
        }

def test_super_blue_predictor():
    """测试超级蓝球预测器"""
    print("=== 超级蓝球预测器测试 ===")
    
    predictor = SuperBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    print(f"间隔模式库: {len(predictor.interval_patterns)}个球号")
    print(f"重现模式库: {sum(len(patterns) for patterns in predictor.recurrence_patterns.values())}个模式")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(100, len(original_data) - 100)  # 确保有足够的训练数据
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 重新初始化模式（每20期更新一次）
        if i % 20 == 0:
            predictor.initialize_patterns()
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 5 or i % 25 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    predictor.initialize_patterns()
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    print(f"总预测次数: {report['total_predictions']}")
    print(f"正确预测次数: {report['correct_predictions']}")
    print(f"整体命中率: {report['hit_rate']:.4f}")
    
    print(f"\n策略权重配置:")
    for strategy, weight in report['strategy_weights'].items():
        print(f"  {strategy}: {weight:.3f}")
    
    print(f"\n策略命中率:")
    for strategy, hit_rate in report['strategy_hit_rates'].items():
        print(f"  {strategy}: {hit_rate:.4f}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"各策略得分:")
        for strategy, score in last_prediction['strategy_scores'].items():
            print(f"  {strategy}: {score:.4f}")
        
        print(f"\n候选球号排序:")
        for i, (ball, score) in enumerate(last_prediction['candidates']):
            print(f"  第{i+1}名: 球号{ball}, 得分{score:.4f}")
    
    return predictor

if __name__ == "__main__":
    test_super_blue_predictor()