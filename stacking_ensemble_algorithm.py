#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Stacking集成双色球预测算法
使用元学习器优化多个基础预测器的结果
目标：红球≥1.5/6，蓝球≥10%
"""

import json
import numpy as np
import random
from collections import defaultdict, Counter
from datetime import datetime
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scikit-learn，如果没有则使用简化版本
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import cross_val_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: scikit-learn未安装，将使用简化版元学习器")

class BasePredictor:
    """基础预测器接口"""
    
    def __init__(self, name: str):
        self.name = name
        self.performance_history = []
        self.feature_importance = {}
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int, Dict[str, float]]:
        """预测接口，返回预测结果和特征重要性"""
        raise NotImplementedError
    
    def get_features(self, data: List[Dict]) -> Dict[str, float]:
        """提取特征"""
        return {}

class AdvancedFrequencyPredictor(BasePredictor):
    """高级频率预测器"""
    
    def __init__(self):
        super().__init__("高级频率预测器")
        self.window_sizes = [20, 30, 50]
        self.decay_factors = [0.9, 0.95, 0.98]
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int, Dict[str, float]]:
        features = self.get_features(data)
        
        red_scores = np.zeros(33)
        blue_scores = np.zeros(16)
        
        # 多窗口频率分析
        for window_size, decay in zip(self.window_sizes, self.decay_factors):
            recent_data = data[-window_size:] if len(data) > window_size else data
            
            for i, record in enumerate(recent_data):
                weight = decay ** (len(recent_data) - i - 1)
                
                red_nums = self.parse_numbers(record['number'])
                blue_num = int(record['refernumber'])
                
                for num in red_nums:
                    if 1 <= num <= 33:
                        red_scores[num-1] += weight
                
                if 1 <= blue_num <= 16:
                    blue_scores[blue_num-1] += weight
        
        # 归一化
        red_scores = red_scores / np.sum(red_scores) if np.sum(red_scores) > 0 else np.ones(33) / 33
        blue_scores = blue_scores / np.sum(blue_scores) if np.sum(blue_scores) > 0 else np.ones(16) / 16
        
        # 选择红球
        red_indices = np.argsort(red_scores)[-6:]
        selected_red = sorted([i + 1 for i in red_indices])
        
        # 选择蓝球
        blue_index = np.argmax(blue_scores)
        selected_blue = blue_index + 1
        
        return selected_red, selected_blue, features
    
    def get_features(self, data: List[Dict]) -> Dict[str, float]:
        if len(data) < 10:
            return {}
        
        recent_data = data[-20:]
        features = {}
        
        # 频率方差
        red_freq = np.zeros(33)
        for record in recent_data:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if 1 <= num <= 33:
                    red_freq[num-1] += 1
        
        features['freq_variance'] = np.var(red_freq)
        features['freq_entropy'] = -np.sum(red_freq * np.log(red_freq + 1e-10))
        
        return features

class AdvancedMarkovPredictor(BasePredictor):
    """高级马尔科夫预测器"""
    
    def __init__(self):
        super().__init__("高级马尔科夫预测器")
        self.orders = [1, 2, 3]  # 多阶马尔科夫
        self.window_size = 60
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int, Dict[str, float]]:
        features = self.get_features(data)
        
        if len(data) < max(self.orders) + 1:
            return list(range(1, 7)), 1, features
        
        recent_data = data[-self.window_size:] if len(data) > self.window_size else data
        
        red_probs = np.zeros(33)
        blue_probs = np.zeros(16)
        
        # 多阶马尔科夫分析
        for order in self.orders:
            if len(recent_data) <= order:
                continue
            
            # 构建转移矩阵
            transitions = defaultdict(lambda: defaultdict(float))
            
            for i in range(len(recent_data) - order):
                # 构建状态序列
                state_sequence = []
                for j in range(order):
                    red_nums = set(self.parse_numbers(recent_data[i + j]['number']))
                    state_sequence.append(tuple(sorted(red_nums)))
                
                current_state = tuple(state_sequence)
                next_red = set(self.parse_numbers(recent_data[i + order]['number']))
                
                weight = (i + 1) / (len(recent_data) - order)
                
                for num in next_red:
                    transitions[current_state][num] += weight
            
            # 预测
            current_sequence = []
            for j in range(order):
                idx = len(data) - order + j
                if idx >= 0:
                    red_nums = set(self.parse_numbers(data[idx]['number']))
                    current_sequence.append(tuple(sorted(red_nums)))
            
            if len(current_sequence) == order:
                current_state = tuple(current_sequence)
                
                if current_state in transitions:
                    total = sum(transitions[current_state].values())
                    if total > 0:
                        for num, count in transitions[current_state].items():
                            if 1 <= num <= 33:
                                red_probs[num-1] += (count / total) / len(self.orders)
        
        # 归一化
        if np.sum(red_probs) > 0:
            red_probs = red_probs / np.sum(red_probs)
        else:
            red_probs = np.ones(33) / 33
        
        # 蓝球马尔科夫
        blue_transitions = defaultdict(float)
        for i in range(len(recent_data) - 1):
            current_blue = int(recent_data[i]['refernumber'])
            next_blue = int(recent_data[i + 1]['refernumber'])
            blue_transitions[next_blue] += 1
        
        total_blue = sum(blue_transitions.values())
        if total_blue > 0:
            for num in range(1, 17):
                blue_probs[num-1] = blue_transitions.get(num, 0) / total_blue
        else:
            blue_probs = np.ones(16) / 16
        
        # 选择号码
        red_indices = np.argsort(red_probs)[-6:]
        selected_red = sorted([i + 1 for i in red_indices])
        
        blue_index = np.argmax(blue_probs)
        selected_blue = blue_index + 1
        
        return selected_red, selected_blue, features
    
    def get_features(self, data: List[Dict]) -> Dict[str, float]:
        if len(data) < 10:
            return {}
        
        features = {}
        recent_data = data[-20:]
        
        # 转移熵
        transitions = defaultdict(int)
        for i in range(len(recent_data) - 1):
            curr_red = set(self.parse_numbers(recent_data[i]['number']))
            next_red = set(self.parse_numbers(recent_data[i + 1]['number']))
            
            overlap = len(curr_red & next_red)
            transitions[overlap] += 1
        
        total = sum(transitions.values())
        if total > 0:
            entropy = 0
            for count in transitions.values():
                p = count / total
                entropy -= p * np.log(p + 1e-10)
            features['transition_entropy'] = entropy
        
        return features

class AdvancedTrendPredictor(BasePredictor):
    """高级趋势预测器"""
    
    def __init__(self):
        super().__init__("高级趋势预测器")
        self.window_sizes = [15, 25, 35]
        self.trend_weights = [0.5, 0.3, 0.2]
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int, Dict[str, float]]:
        features = self.get_features(data)
        
        red_scores = np.zeros(33)
        blue_scores = np.zeros(16)
        
        # 多窗口趋势分析
        for window_size, weight in zip(self.window_sizes, self.trend_weights):
            if len(data) < window_size:
                continue
            
            recent_data = data[-window_size:]
            
            # 红球趋势
            for num in range(1, 34):
                appearances = []
                for i, record in enumerate(recent_data):
                    red_nums = self.parse_numbers(record['number'])
                    if num in red_nums:
                        appearances.append(i)
                
                if len(appearances) >= 2:
                    # 线性回归趋势
                    x = np.array(appearances[:-1])
                    y = np.array(appearances[1:])
                    
                    if len(x) > 1:
                        slope = np.polyfit(x, y, 1)[0]
                        last_pos = appearances[-1]
                        predicted_next = last_pos + slope
                        
                        distance = abs(window_size - 1 - predicted_next)
                        score = max(0, 1 - distance / window_size)
                        red_scores[num-1] += score * weight
                elif len(appearances) == 1:
                    last_pos = appearances[0]
                    red_scores[num-1] += (window_size - last_pos) / window_size * weight * 0.3
                else:
                    red_scores[num-1] += 0.2 * weight
            
            # 蓝球趋势
            blue_appearances = {}
            for i, record in enumerate(recent_data):
                blue_num = int(record['refernumber'])
                if blue_num not in blue_appearances:
                    blue_appearances[blue_num] = []
                blue_appearances[blue_num].append(i)
            
            for num in range(1, 17):
                if num in blue_appearances and len(blue_appearances[num]) >= 1:
                    last_pos = blue_appearances[num][-1]
                    blue_scores[num-1] += (window_size - last_pos) / window_size * weight
                else:
                    blue_scores[num-1] += 0.3 * weight
        
        # 归一化
        red_scores = red_scores / np.sum(red_scores) if np.sum(red_scores) > 0 else np.ones(33) / 33
        blue_scores = blue_scores / np.sum(blue_scores) if np.sum(blue_scores) > 0 else np.ones(16) / 16
        
        # 选择号码
        red_indices = np.argsort(red_scores)[-6:]
        selected_red = sorted([i + 1 for i in red_indices])
        
        blue_index = np.argmax(blue_scores)
        selected_blue = blue_index + 1
        
        return selected_red, selected_blue, features
    
    def get_features(self, data: List[Dict]) -> Dict[str, float]:
        if len(data) < 15:
            return {}
        
        features = {}
        recent_data = data[-20:]
        
        # 趋势强度
        trend_strengths = []
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_data):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 3:
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                trend_strength = np.std(intervals) if len(intervals) > 1 else 0
                trend_strengths.append(trend_strength)
        
        if trend_strengths:
            features['avg_trend_strength'] = np.mean(trend_strengths)
            features['max_trend_strength'] = np.max(trend_strengths)
        
        return features

class AdvancedPatternPredictor(BasePredictor):
    """高级模式预测器"""
    
    def __init__(self):
        super().__init__("高级模式预测器")
        self.pattern_types = ['consecutive', 'odd_even', 'zone', 'sum_range']
        self.window_size = 25
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int, Dict[str, float]]:
        features = self.get_features(data)
        
        if len(data) < self.window_size:
            return list(range(1, 7)), 1, features
        
        recent_data = data[-self.window_size:]
        red_scores = np.zeros(33)
        blue_scores = np.zeros(16)
        
        # 连号模式
        consecutive_patterns = self._analyze_consecutive_patterns(recent_data)
        for num in range(1, 34):
            red_scores[num-1] += consecutive_patterns.get(num, 0) * 0.25
        
        # 奇偶模式
        odd_even_patterns = self._analyze_odd_even_patterns(recent_data)
        for num in range(1, 34):
            if num % 2 == 1:  # 奇数
                red_scores[num-1] += odd_even_patterns.get('odd', 0) * 0.25
            else:  # 偶数
                red_scores[num-1] += odd_even_patterns.get('even', 0) * 0.25
        
        # 区间模式
        zone_patterns = self._analyze_zone_patterns(recent_data)
        for num in range(1, 34):
            if 1 <= num <= 11:
                red_scores[num-1] += zone_patterns.get('zone1', 0) * 0.25
            elif 12 <= num <= 22:
                red_scores[num-1] += zone_patterns.get('zone2', 0) * 0.25
            elif 23 <= num <= 33:
                red_scores[num-1] += zone_patterns.get('zone3', 0) * 0.25
        
        # 和值模式
        sum_patterns = self._analyze_sum_patterns(recent_data)
        for num in range(1, 34):
            red_scores[num-1] += sum_patterns.get(num, 0) * 0.25
        
        # 蓝球模式
        blue_freq = np.zeros(16)
        for record in recent_data:
            blue_num = int(record['refernumber'])
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        blue_scores = blue_freq / np.sum(blue_freq) if np.sum(blue_freq) > 0 else np.ones(16) / 16
        
        # 选择号码
        red_indices = np.argsort(red_scores)[-6:]
        selected_red = sorted([i + 1 for i in red_indices])
        
        blue_index = np.argmax(blue_scores)
        selected_blue = blue_index + 1
        
        return selected_red, selected_blue, features
    
    def _analyze_consecutive_patterns(self, data: List[Dict]) -> Dict[int, float]:
        consecutive_scores = defaultdict(float)
        
        for record in data:
            red_nums = sorted(self.parse_numbers(record['number']))
            
            # 分析连号
            for i in range(len(red_nums) - 1):
                if red_nums[i+1] == red_nums[i] + 1:
                    consecutive_scores[red_nums[i]] += 0.5
                    consecutive_scores[red_nums[i+1]] += 0.5
        
        return dict(consecutive_scores)
    
    def _analyze_odd_even_patterns(self, data: List[Dict]) -> Dict[str, float]:
        odd_count = 0
        even_count = 0
        
        for record in data:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if num % 2 == 1:
                    odd_count += 1
                else:
                    even_count += 1
        
        total = odd_count + even_count
        if total > 0:
            return {
                'odd': odd_count / total,
                'even': even_count / total
            }
        return {'odd': 0.5, 'even': 0.5}
    
    def _analyze_zone_patterns(self, data: List[Dict]) -> Dict[str, float]:
        zone_counts = [0, 0, 0]
        
        for record in data:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if 1 <= num <= 11:
                    zone_counts[0] += 1
                elif 12 <= num <= 22:
                    zone_counts[1] += 1
                elif 23 <= num <= 33:
                    zone_counts[2] += 1
        
        total = sum(zone_counts)
        if total > 0:
            return {
                'zone1': zone_counts[0] / total,
                'zone2': zone_counts[1] / total,
                'zone3': zone_counts[2] / total
            }
        return {'zone1': 1/3, 'zone2': 1/3, 'zone3': 1/3}
    
    def _analyze_sum_patterns(self, data: List[Dict]) -> Dict[int, float]:
        sum_scores = defaultdict(float)
        sum_values = []
        
        for record in data:
            red_nums = self.parse_numbers(record['number'])
            sum_val = sum(red_nums)
            sum_values.append(sum_val)
        
        if sum_values:
            avg_sum = np.mean(sum_values)
            std_sum = np.std(sum_values)
            
            for num in range(1, 34):
                # 基于历史和值分布给分
                if avg_sum - std_sum <= 100 + num * 2 <= avg_sum + std_sum:
                    sum_scores[num] = 1.0
                else:
                    distance = abs((100 + num * 2) - avg_sum)
                    sum_scores[num] = max(0, 1 - distance / (std_sum * 2))
        
        return dict(sum_scores)
    
    def get_features(self, data: List[Dict]) -> Dict[str, float]:
        if len(data) < 10:
            return {}
        
        features = {}
        recent_data = data[-15:]
        
        # 模式复杂度
        pattern_complexities = []
        for record in recent_data:
            red_nums = sorted(self.parse_numbers(record['number']))
            
            # 计算连号数量
            consecutive_count = 0
            for i in range(len(red_nums) - 1):
                if red_nums[i+1] == red_nums[i] + 1:
                    consecutive_count += 1
            
            # 计算奇偶比例
            odd_count = sum(1 for num in red_nums if num % 2 == 1)
            odd_ratio = odd_count / len(red_nums)
            
            # 复杂度评分
            complexity = consecutive_count * 0.3 + abs(odd_ratio - 0.5) * 0.7
            pattern_complexities.append(complexity)
        
        if pattern_complexities:
            features['avg_pattern_complexity'] = np.mean(pattern_complexities)
            features['pattern_stability'] = 1 / (np.std(pattern_complexities) + 1e-10)
        
        return features

class MetaLearner:
    """元学习器"""
    
    def __init__(self):
        self.red_model = None
        self.blue_model = None
        self.scaler = None
        self.is_trained = False
        
        if SKLEARN_AVAILABLE:
            self.red_model = RandomForestRegressor(n_estimators=100, random_state=42)
            self.blue_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
            self.scaler = StandardScaler()
        else:
            # 简化版元学习器
            self.weights = None
    
    def prepare_training_data(self, predictions_history: List[Dict]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """准备训练数据"""
        X_red = []
        y_red = []
        X_blue = []
        y_blue = []
        
        for entry in predictions_history:
            # 基础预测器的预测结果作为特征
            red_features = []
            blue_features = []
            
            for pred_name, (pred_red, pred_blue, features) in entry['predictions'].items():
                # 红球特征：每个预测器预测的6个号码的one-hot编码
                red_onehot = np.zeros(33)
                for num in pred_red:
                    if 1 <= num <= 33:
                        red_onehot[num-1] = 1
                red_features.extend(red_onehot)
                
                # 蓝球特征：预测的蓝球号码的one-hot编码
                blue_onehot = np.zeros(16)
                if 1 <= pred_blue <= 16:
                    blue_onehot[pred_blue-1] = 1
                blue_features.extend(blue_onehot)
                
                # 添加额外特征
                for feature_name, feature_value in features.items():
                    red_features.append(feature_value)
                    blue_features.append(feature_value)
            
            X_red.append(red_features)
            X_blue.append(blue_features)
            
            # 目标值
            actual_red = entry['actual_red']
            actual_blue = entry['actual_blue']
            
            # 红球目标：每个号码是否命中
            red_target = np.zeros(33)
            for num in actual_red:
                if 1 <= num <= 33:
                    red_target[num-1] = 1
            y_red.append(red_target)
            
            # 蓝球目标：蓝球号码的one-hot编码
            blue_target = np.zeros(16)
            if 1 <= actual_blue <= 16:
                blue_target[actual_blue-1] = 1
            y_blue.append(blue_target)
        
        return np.array(X_red), np.array(y_red), np.array(X_blue), np.array(y_blue)
    
    def train(self, predictions_history: List[Dict]):
        """训练元学习器"""
        if len(predictions_history) < 10:
            print("训练数据不足，需要至少10期数据")
            return False
        
        try:
            X_red, y_red, X_blue, y_blue = self.prepare_training_data(predictions_history)
            
            if SKLEARN_AVAILABLE and X_red.shape[0] > 0:
                # 标准化特征
                X_red_scaled = self.scaler.fit_transform(X_red)
                X_blue_scaled = self.scaler.transform(X_blue)
                
                # 训练红球模型（多输出回归）
                self.red_model.fit(X_red_scaled, y_red)
                
                # 训练蓝球模型
                y_blue_single = np.argmax(y_blue, axis=1)  # 转换为单一标签
                self.blue_model.fit(X_blue_scaled, y_blue_single)
                
                self.is_trained = True
                print(f"元学习器训练完成，使用 {len(predictions_history)} 期数据")
                return True
            else:
                # 简化版训练
                self.weights = np.random.random(len(predictions_history[0]['predictions']))
                self.weights = self.weights / np.sum(self.weights)
                self.is_trained = True
                print("使用简化版元学习器")
                return True
                
        except Exception as e:
            print(f"元学习器训练失败: {e}")
            return False
    
    def predict(self, base_predictions: Dict[str, Tuple[List[int], int, Dict[str, float]]]) -> Tuple[List[int], int]:
        """元学习器预测"""
        if not self.is_trained:
            # 如果未训练，使用简单投票
            return self._simple_voting(base_predictions)
        
        try:
            if SKLEARN_AVAILABLE and self.red_model is not None:
                # 准备特征
                features = []
                for pred_name, (pred_red, pred_blue, extra_features) in base_predictions.items():
                    # 红球one-hot
                    red_onehot = np.zeros(33)
                    for num in pred_red:
                        if 1 <= num <= 33:
                            red_onehot[num-1] = 1
                    features.extend(red_onehot)
                    
                    # 额外特征
                    for feature_value in extra_features.values():
                        features.append(feature_value)
                
                X = np.array(features).reshape(1, -1)
                X_scaled = self.scaler.transform(X)
                
                # 预测红球
                red_probs = self.red_model.predict(X_scaled)[0]
                red_indices = np.argsort(red_probs)[-6:]
                selected_red = sorted([i + 1 for i in red_indices])
                
                # 预测蓝球
                blue_features = []
                for pred_name, (pred_red, pred_blue, extra_features) in base_predictions.items():
                    blue_onehot = np.zeros(16)
                    if 1 <= pred_blue <= 16:
                        blue_onehot[pred_blue-1] = 1
                    blue_features.extend(blue_onehot)
                    
                    for feature_value in extra_features.values():
                        blue_features.append(feature_value)
                
                X_blue = np.array(blue_features).reshape(1, -1)
                X_blue_scaled = self.scaler.transform(X_blue)
                
                blue_pred = self.blue_model.predict(X_blue_scaled)[0]
                selected_blue = int(blue_pred) + 1
                selected_blue = max(1, min(16, selected_blue))
                
                return selected_red, selected_blue
            else:
                # 简化版预测
                return self._weighted_voting(base_predictions)
                
        except Exception as e:
            print(f"元学习器预测失败: {e}")
            return self._simple_voting(base_predictions)
    
    def _simple_voting(self, base_predictions: Dict[str, Tuple[List[int], int, Dict[str, float]]]) -> Tuple[List[int], int]:
        """简单投票"""
        red_votes = defaultdict(int)
        blue_votes = defaultdict(int)
        
        for pred_name, (pred_red, pred_blue, _) in base_predictions.items():
            for num in pred_red:
                red_votes[num] += 1
            blue_votes[pred_blue] += 1
        
        # 选择红球
        red_candidates = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, votes in red_candidates[:6]])
        
        # 选择蓝球
        blue_candidates = sorted(blue_votes.items(), key=lambda x: x[1], reverse=True)
        selected_blue = blue_candidates[0][0] if blue_candidates else 1
        
        return selected_red, selected_blue
    
    def _weighted_voting(self, base_predictions: Dict[str, Tuple[List[int], int, Dict[str, float]]]) -> Tuple[List[int], int]:
        """加权投票"""
        red_votes = defaultdict(float)
        blue_votes = defaultdict(float)
        
        predictor_names = list(base_predictions.keys())
        
        for i, (pred_name, (pred_red, pred_blue, _)) in enumerate(base_predictions.items()):
            weight = self.weights[i] if i < len(self.weights) else 1.0
            
            for num in pred_red:
                red_votes[num] += weight
            blue_votes[pred_blue] += weight
        
        # 选择红球
        red_candidates = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, votes in red_candidates[:6]])
        
        # 选择蓝球
        blue_candidates = sorted(blue_votes.items(), key=lambda x: x[1], reverse=True)
        selected_blue = blue_candidates[0][0] if blue_candidates else 1
        
        return selected_red, selected_blue

class StackingEnsemblePredictor:
    """Stacking集成预测器"""
    
    def __init__(self):
        self.data = []
        self.base_predictors = [
            AdvancedFrequencyPredictor(),
            AdvancedMarkovPredictor(),
            AdvancedTrendPredictor(),
            AdvancedPatternPredictor()
        ]
        self.meta_learner = MetaLearner()
        self.predictions_history = []
        self.training_window = 100  # 用于训练元学习器的窗口大小
    
    def load_data(self, file_path: str):
        """加载历史数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 期历史数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def parse_numbers(self, number_str: str) -> List[int]:
        """解析号码字符串"""
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def get_base_predictions(self, data: List[Dict]) -> Dict[str, Tuple[List[int], int, Dict[str, float]]]:
        """获取基础预测器的预测结果"""
        predictions = {}
        
        for predictor in self.base_predictors:
            try:
                pred_red, pred_blue, features = predictor.predict(data)
                predictions[predictor.name] = (pred_red, pred_blue, features)
            except Exception as e:
                print(f"{predictor.name} 预测失败: {e}")
                predictions[predictor.name] = (list(range(1, 7)), 1, {})
        
        return predictions
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        """Stacking预测"""
        # 获取基础预测器结果
        base_predictions = self.get_base_predictions(data)
        
        # 使用元学习器进行最终预测
        final_red, final_blue = self.meta_learner.predict(base_predictions)
        
        return final_red, final_blue
    
    def update_history(self, base_predictions: Dict[str, Tuple[List[int], int, Dict[str, float]]], 
                      actual_red: List[int], actual_blue: int):
        """更新预测历史"""
        entry = {
            'predictions': base_predictions,
            'actual_red': actual_red,
            'actual_blue': actual_blue
        }
        
        self.predictions_history.append(entry)
        
        # 保持历史记录在合理范围内
        if len(self.predictions_history) > self.training_window * 2:
            self.predictions_history = self.predictions_history[-self.training_window:]
        
        # 定期重新训练元学习器
        if len(self.predictions_history) >= self.training_window and len(self.predictions_history) % 20 == 0:
            print(f"重新训练元学习器，使用最近 {len(self.predictions_history)} 期数据")
            self.meta_learner.train(self.predictions_history[-self.training_window:])

def stacking_ensemble_backtest(predictor: StackingEnsemblePredictor, test_periods: int = 500):
    """Stacking集成算法回测"""
    print("\n=== Stacking集成算法回测 ===")
    print(f"回测期数: {test_periods}")
    print("目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("=" * 50)
    
    if len(predictor.data) < test_periods + predictor.training_window:
        print(f"数据不足，需要至少 {test_periods + predictor.training_window} 期数据")
        return
    
    test_data = predictor.data[-test_periods:]
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    # 基础预测器性能统计
    base_predictor_stats = {p.name: {'red_hits': [], 'blue_hits': 0} for p in predictor.base_predictors}
    
    print(f"开始回测 {test_periods} 期...")
    print(f"使用 {len(predictor.base_predictors)} 个基础预测器 + 元学习器")
    
    for i in range(test_periods):
        history_data = predictor.data[:-(test_periods-i)]
        
        try:
            # 获取基础预测器结果
            base_predictions = predictor.get_base_predictions(history_data)
            
            # Stacking预测
            pred_red, pred_blue = predictor.predict(history_data)
            
            # 实际结果
            actual_data = test_data[i]
            actual_red = predictor.parse_numbers(actual_data['number'])
            actual_blue = int(actual_data['refernumber'])
            
            # 计算命中
            red_hit_count = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            
            red_hits.append(red_hit_count)
            blue_hits += blue_hit
            
            # 统计基础预测器性能
            for pred_name, (base_red, base_blue, _) in base_predictions.items():
                base_red_hit = len(set(base_red) & set(actual_red))
                base_blue_hit = 1 if base_blue == actual_blue else 0
                
                base_predictor_stats[pred_name]['red_hits'].append(base_red_hit)
                base_predictor_stats[pred_name]['blue_hits'] += base_blue_hit
            
            # 更新历史记录
            predictor.update_history(base_predictions, actual_red, actual_blue)
            
            # 记录优秀预测
            if red_hit_count >= 3 or blue_hit == 1:
                best_predictions.append({
                    'period': actual_data['issueno'],
                    'red_hit': red_hit_count,
                    'blue_hit': blue_hit,
                    'predicted_red': pred_red,
                    'actual_red': actual_red,
                    'predicted_blue': pred_blue,
                    'actual_blue': actual_blue
                })
        except Exception as e:
            print(f"预测第 {i+1} 期时出错: {e}")
            red_hits.append(0)
        
        # 进度显示
        if (i + 1) % 100 == 0:
            current_red_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / (i + 1) * 100
            print(f"进度: {i+1}/{test_periods}, 当前红球: {current_red_avg:.2f}/6, 蓝球: {current_blue_rate:.1f}%")
    
    # 计算最终结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / test_periods * 100
    
    print(f"\n=== Stacking集成算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits}/{test_periods})")
    
    # 红球命中分布
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_hits.count(i)
        percentage = count / test_periods * 100
        print(f"  {i}个: {count}次 ({percentage:.1f}%)")
    
    # 基础预测器性能对比
    print(f"\n=== 基础预测器性能对比 ===")
    for pred_name, stats in base_predictor_stats.items():
        if stats['red_hits']:
            avg_red = sum(stats['red_hits']) / len(stats['red_hits'])
            blue_rate = stats['blue_hits'] / test_periods * 100
            print(f"  {pred_name}:")
            print(f"    红球: {avg_red:.2f}/6, 蓝球: {blue_rate:.1f}%")
    
    # Stacking效果分析
    print(f"\n=== Stacking效果分析 ===")
    best_base_red = max([sum(stats['red_hits']) / len(stats['red_hits']) 
                        for stats in base_predictor_stats.values() if stats['red_hits']])
    best_base_blue = max([stats['blue_hits'] / test_periods * 100 
                         for stats in base_predictor_stats.values()])
    
    print(f"最佳基础预测器 - 红球: {best_base_red:.2f}/6, 蓝球: {best_base_blue:.1f}%")
    print(f"Stacking集成   - 红球: {avg_red_hits:.2f}/6, 蓝球: {blue_hit_rate:.1f}%")
    
    red_improvement = (avg_red_hits - best_base_red) / best_base_red * 100 if best_base_red > 0 else 0
    blue_improvement = (blue_hit_rate - best_base_blue) / best_base_blue * 100 if best_base_blue > 0 else 0
    
    print(f"Stacking提升   - 红球: {red_improvement:+.1f}%, 蓝球: {blue_improvement:+.1f}%")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 10.0
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前15期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:15]):
            blue_symbol = "✓" if pred['blue_hit'] else "✗"
            print(f"{i+1:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_symbol}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 算法评估
    print(f"\n=== Stacking集成算法评估 ===")
    if avg_red_hits >= red_target and blue_hit_rate >= blue_target:
        print("🎉 恭喜！算法达到预期目标")
    elif avg_red_hits >= red_target * 0.9 and blue_hit_rate >= blue_target * 0.9:
        print("🔧 算法表现优秀，非常接近目标")
    elif avg_red_hits >= red_target * 0.8 and blue_hit_rate >= blue_target * 0.8:
        print("📈 算法表现良好，还有提升空间")
    else:
        print("⚠️ 算法需要进一步优化")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement,
        'stacking_improvement': {
            'red': red_improvement,
            'blue': blue_improvement
        }
    }

if __name__ == "__main__":
    print("=== Stacking集成双色球预测算法 ===")
    
    # 创建Stacking预测器
    predictor = StackingEnsemblePredictor()
    
    if not predictor.load_data('ssq_data.json'):
        print("数据加载失败，程序退出")
        exit(1)
    
    print(f"\nStacking集成预测器架构:")
    print(f"基础预测器层:")
    for i, pred in enumerate(predictor.base_predictors):
        print(f"  {i+1}. {pred.name}")
    
    sklearn_status = "可用" if SKLEARN_AVAILABLE else "不可用（使用简化版）"
    print(f"元学习器: {sklearn_status}")
    
    # 开始回测
    results = stacking_ensemble_backtest(predictor, test_periods=500)
    
    if results:
        print(f"\n=== Stacking集成算法总结 ===")
        print(f"通过两层架构（基础预测器 + 元学习器）：")
        print(f"- 红球命中率: {results['red_avg']:.2f}/6 (达成度: {results['red_achievement']:.1f}%)")
        print(f"- 蓝球命中率: {results['blue_rate']:.1f}% (达成度: {results['blue_achievement']:.1f}%)")
        print(f"- 整体目标达成度: {(results['red_achievement'] + results['blue_achievement'])/2:.1f}%")
        
        if results['red_avg'] >= 1.4 or results['blue_rate'] >= 9.0:
            print("\n🚀 Stacking效果显著！")
        elif results['red_avg'] >= 1.2 or results['blue_rate'] >= 7.5:
            print("\n📈 Stacking有一定效果，建议继续优化")
        else:
            print("\n🔄 建议调整Stacking架构或基础预测器")
        
        print(f"\n💡 进一步优化建议:")
        print(f"1. 增加更多类型的基础预测器")
        print(f"2. 尝试更复杂的元学习器（深度神经网络）")
        print(f"3. 优化特征工程和特征选择")
        print(f"4. 考虑时间序列特征和外部数据")
        print(f"5. 使用交叉验证优化超参数")