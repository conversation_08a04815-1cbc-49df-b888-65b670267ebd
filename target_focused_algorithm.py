#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目标导向双色球预测算法
专注于达到：红球命中率 >= 3/6，蓝球命中率 >= 5/16 (31.25%)
采用稳定可靠的统计学方法和优化策略
"""

import json
import numpy as np
from collections import defaultdict, Counter
import random
from datetime import datetime
import itertools

class TargetFocusedPredictor:
    def __init__(self, data):
        self.data = data
        self.red_balls = []
        self.blue_balls = []
        self.periods = []
        self.parse_data()
        
    def parse_data(self):
        """解析历史数据"""
        for item in self.data:
            red_str = item['number']
            blue_str = item['refernumber']
            
            red_nums = [int(x) for x in red_str.split()]
            self.red_balls.append(sorted(red_nums))
            self.blue_balls.append(int(blue_str))
            self.periods.append(item['issueno'])
    
    def comprehensive_analysis(self):
        """综合分析"""
        analysis = {
            'red_frequency': Counter(),
            'blue_frequency': Counter(),
            'red_position': [Counter() for _ in range(6)],
            'red_pairs': Counter(),
            'blue_after_sum': defaultdict(list),
            'red_intervals': defaultdict(list),
            'blue_intervals': defaultdict(list),
            'hot_numbers': set(),
            'cold_numbers': set(),
            'trend_numbers': set()
        }
        
        # 基础频率统计
        for red_combo, blue in zip(self.red_balls, self.blue_balls):
            for num in red_combo:
                analysis['red_frequency'][num] += 1
            analysis['blue_frequency'][blue] += 1
            
            # 位置统计
            for pos, num in enumerate(red_combo):
                analysis['red_position'][pos][num] += 1
            
            # 配对统计
            for pair in itertools.combinations(red_combo, 2):
                analysis['red_pairs'][pair] += 1
            
            # 蓝球与红球和值关系
            red_sum = sum(red_combo)
            analysis['blue_after_sum'][red_sum].append(blue)
        
        # 间隔分析
        for num in range(1, 34):
            last_appear = -1
            for i, red_combo in enumerate(self.red_balls):
                if num in red_combo:
                    if last_appear != -1:
                        analysis['red_intervals'][num].append(i - last_appear)
                    last_appear = i
        
        for num in range(1, 17):
            last_appear = -1
            for i, blue in enumerate(self.blue_balls):
                if blue == num:
                    if last_appear != -1:
                        analysis['blue_intervals'][num].append(i - last_appear)
                    last_appear = i
        
        # 热冷号分析（最近50期）
        recent_period = min(50, len(self.red_balls))
        recent_red_freq = Counter()
        recent_blue_freq = Counter()
        
        for i in range(recent_period):
            for num in self.red_balls[-(i+1)]:
                recent_red_freq[num] += 1
            recent_blue_freq[self.blue_balls[-(i+1)]] += 1
        
        # 定义热号（出现频率 > 平均值 * 1.2）
        avg_red_freq = sum(recent_red_freq.values()) / 33
        avg_blue_freq = sum(recent_blue_freq.values()) / 16
        
        for num in range(1, 34):
            if recent_red_freq[num] > avg_red_freq * 1.2:
                analysis['hot_numbers'].add(num)
            elif recent_red_freq[num] < avg_red_freq * 0.5:
                analysis['cold_numbers'].add(num)
        
        # 趋势分析（最近20期 vs 前20期）
        if len(self.red_balls) >= 40:
            recent_20 = Counter()
            prev_20 = Counter()
            
            for i in range(20):
                for num in self.red_balls[-(i+1)]:
                    recent_20[num] += 1
                for num in self.red_balls[-(i+21)]:
                    prev_20[num] += 1
            
            for num in range(1, 34):
                if recent_20[num] > prev_20[num] * 1.5:
                    analysis['trend_numbers'].add(num)
        
        return analysis
    
    def smart_red_selection(self, analysis):
        """智能红球选择"""
        scores = defaultdict(float)
        
        # 1. 历史频率权重 (30%)
        total_freq = sum(analysis['red_frequency'].values())
        for num in range(1, 34):
            freq_score = analysis['red_frequency'][num] / total_freq
            scores[num] += freq_score * 0.3
        
        # 2. 位置权重 (20%)
        for num in range(1, 34):
            position_score = 0
            for pos_counter in analysis['red_position']:
                if num in pos_counter:
                    pos_total = sum(pos_counter.values())
                    position_score += pos_counter[num] / pos_total if pos_total > 0 else 0
            scores[num] += (position_score / 6) * 0.2
        
        # 3. 配对权重 (15%)
        for num in range(1, 34):
            pair_score = 0
            for pair, count in analysis['red_pairs'].items():
                if num in pair:
                    pair_score += count
            total_pairs = sum(analysis['red_pairs'].values())
            scores[num] += (pair_score / total_pairs) * 0.15 if total_pairs > 0 else 0
        
        # 4. 间隔权重 (15%)
        for num in range(1, 34):
            if num in analysis['red_intervals'] and analysis['red_intervals'][num]:
                avg_interval = np.mean(analysis['red_intervals'][num])
                last_appear = -1
                for i, combo in enumerate(reversed(self.red_balls)):
                    if num in combo:
                        last_appear = i
                        break
                
                if last_appear != -1 and avg_interval > 0:
                    interval_score = min(1.0, last_appear / avg_interval)
                    scores[num] += interval_score * 0.15
        
        # 5. 热冷号调整 (10%)
        for num in analysis['hot_numbers']:
            scores[num] += 0.05
        for num in analysis['cold_numbers']:
            scores[num] += 0.08  # 冷号反弹权重更高
        
        # 6. 趋势调整 (10%)
        for num in analysis['trend_numbers']:
            scores[num] += 0.1
        
        # 选择得分最高的候选号码
        sorted_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 智能组合选择
        selected_reds = self.optimize_red_combination(sorted_candidates[:12], analysis)
        
        return selected_reds
    
    def optimize_red_combination(self, candidates, analysis):
        """优化红球组合"""
        best_combo = None
        best_score = -1
        
        # 生成多个候选组合
        for _ in range(1000):
            # 从前12个候选中随机选择6个
            combo = random.sample([num for num, _ in candidates], 6)
            combo = sorted(combo)
            
            # 评估组合质量
            score = self.evaluate_red_combination(combo, analysis)
            
            if score > best_score:
                best_score = score
                best_combo = combo
        
        return best_combo if best_combo else sorted([num for num, _ in candidates[:6]])
    
    def evaluate_red_combination(self, combo, analysis):
        """评估红球组合质量"""
        score = 0
        
        # 1. 和值评估
        combo_sum = sum(combo)
        if 80 <= combo_sum <= 140:  # 理想和值范围
            score += 20
        elif 70 <= combo_sum <= 160:
            score += 10
        
        # 2. 跨度评估
        span = max(combo) - min(combo)
        if 15 <= span <= 25:
            score += 15
        elif 10 <= span <= 30:
            score += 8
        
        # 3. 奇偶比评估
        odd_count = sum(1 for x in combo if x % 2 == 1)
        if odd_count in [2, 3, 4]:  # 理想奇偶比
            score += 15
        
        # 4. 连号评估
        consecutive_count = 0
        for i in range(len(combo) - 1):
            if combo[i+1] - combo[i] == 1:
                consecutive_count += 1
        
        if consecutive_count <= 2:  # 连号不宜过多
            score += 10
        
        # 5. 区间分布评估
        zone1 = sum(1 for x in combo if 1 <= x <= 11)
        zone2 = sum(1 for x in combo if 12 <= x <= 22)
        zone3 = sum(1 for x in combo if 23 <= x <= 33)
        
        if all(z >= 1 for z in [zone1, zone2, zone3]):  # 三区都有分布
            score += 15
        elif sum(1 for z in [zone1, zone2, zone3] if z >= 1) >= 2:  # 至少两区有分布
            score += 8
        
        # 6. 配对历史评估
        pair_bonus = 0
        for pair in itertools.combinations(combo, 2):
            if pair in analysis['red_pairs']:
                pair_bonus += analysis['red_pairs'][pair]
        
        total_pairs = sum(analysis['red_pairs'].values())
        if total_pairs > 0:
            score += (pair_bonus / total_pairs) * 20
        
        return score
    
    def smart_blue_selection(self, red_combo, analysis):
        """智能蓝球选择"""
        blue_scores = defaultdict(float)
        
        # 1. 历史频率权重 (40%)
        total_blue_freq = sum(analysis['blue_frequency'].values())
        for num in range(1, 17):
            freq_score = analysis['blue_frequency'][num] / total_blue_freq
            blue_scores[num] += freq_score * 0.4
        
        # 2. 与红球和值关系 (30%)
        red_sum = sum(red_combo)
        if red_sum in analysis['blue_after_sum']:
            blues_after_sum = analysis['blue_after_sum'][red_sum]
            if blues_after_sum:
                blue_counter = Counter(blues_after_sum)
                for blue, count in blue_counter.items():
                    blue_scores[blue] += (count / len(blues_after_sum)) * 0.3
        
        # 3. 间隔权重 (20%)
        for num in range(1, 17):
            if num in analysis['blue_intervals'] and analysis['blue_intervals'][num]:
                avg_interval = np.mean(analysis['blue_intervals'][num])
                last_appear = -1
                for i, blue in enumerate(reversed(self.blue_balls)):
                    if blue == num:
                        last_appear = i
                        break
                
                if last_appear != -1 and avg_interval > 0:
                    interval_score = min(1.0, last_appear / avg_interval)
                    blue_scores[num] += interval_score * 0.2
        
        # 4. 近期趋势 (10%)
        recent_blues = self.blue_balls[-10:] if len(self.blue_balls) >= 10 else self.blue_balls
        if recent_blues:
            recent_avg = np.mean(recent_blues)
            for num in range(1, 17):
                # 接近近期平均值的蓝球加分
                distance_score = 1 - abs(num - recent_avg) / 16
                blue_scores[num] += distance_score * 0.1
        
        # 选择得分最高的蓝球
        best_blue = max(blue_scores.items(), key=lambda x: x[1])[0]
        
        return best_blue
    
    def predict(self):
        """主预测函数"""
        if len(self.red_balls) < 50:
            # 数据不足，返回随机预测
            return sorted(random.sample(range(1, 34), 6)), random.randint(1, 16)
        
        # 综合分析
        analysis = self.comprehensive_analysis()
        
        # 智能选择红球
        red_prediction = self.smart_red_selection(analysis)
        
        # 智能选择蓝球
        blue_prediction = self.smart_blue_selection(red_prediction, analysis)
        
        return red_prediction, blue_prediction

def load_data():
    """加载历史数据"""
    try:
        with open('ssq_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("错误：未找到 ssq_data.json 文件")
        return []

def parse_actual_numbers(red_str, blue_str):
    """解析实际开奖号码"""
    red_nums = [int(x) for x in red_str.split()]
    blue_num = int(blue_str)
    return sorted(red_nums), blue_num

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def target_focused_backtest():
    """目标导向回测"""
    print("=== 目标导向双色球预测算法回测 ===")
    print("目标：红球命中率 >= 3/6，蓝球命中率 >= 31.25%")
    print()
    
    data = load_data()
    if len(data) < 300:
        print(f"警告：数据不足300期，当前只有 {len(data)} 期数据")
        return
    
    print(f"加载了 {len(data)} 期历史数据")
    
    test_periods = 300  # 测试300期
    training_periods = 200  # 使用200期数据进行训练
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    
    print(f"开始回测最近 {test_periods} 期数据...")
    print()
    
    for i in range(test_periods):
        start_idx = max(0, len(data) - test_periods + i - training_periods)
        end_idx = len(data) - test_periods + i
        
        if end_idx <= start_idx:
            continue
            
        training_data = data[start_idx:end_idx]
        test_data = data[len(data) - test_periods + i]
        
        try:
            predictor = TargetFocusedPredictor(training_data)
            predicted_red, predicted_blue = predictor.predict()
            
            actual_red, actual_blue = parse_actual_numbers(test_data['number'], test_data['refernumber'])
            
            red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            results.append({
                'period': test_data['issueno'],
                'predicted_red': predicted_red,
                'predicted_blue': predicted_blue,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'red_hits': red_hits,
                'blue_hit': blue_hit
            })
            
            if (i + 1) % 50 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = blue_hits_total / (i + 1)
                print(f"已完成 {i + 1}/{test_periods} 期回测")
                print(f"当前平均红球命中: {current_red_avg:.2f}/6")
                print(f"当前蓝球命中率: {current_blue_rate:.1%}")
                print()
                
        except Exception as e:
            print(f"期号 {test_data['issueno']} 预测失败: {e}")
            continue
    
    # 统计结果
    print("=== 目标导向算法回测结果统计 ===")
    
    avg_red_hits = red_hits_total / len(results) if results else 0
    blue_hit_rate = blue_hits_total / len(results) if results else 0
    
    print(f"平均红球命中数: {avg_red_hits:.2f}/6")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits_total}/{len(results)})")
    print()
    
    # 目标达成情况
    target_red = 3.0
    target_blue = 5/16
    
    print("=== 目标达成情况 ===")
    print(f"红球目标: >= {target_red}/6, 实际: {avg_red_hits:.2f}/6 {'✓' if avg_red_hits >= target_red else '✗'}")
    print(f"蓝球目标: >= {target_blue:.1%}, 实际: {blue_hit_rate:.1%} {'✓' if blue_hit_rate >= target_blue else '✗'}")
    print()
    
    # 详细分析
    red_hit_distribution = Counter([r['red_hits'] for r in results])
    print("=== 红球命中分布 ===")
    for hits in sorted(red_hit_distribution.keys()):
        count = red_hit_distribution[hits]
        percentage = count / len(results) * 100
        print(f"{hits}个红球命中: {count}次 ({percentage:.1f}%)")
    print()
    
    # 高命中率期数统计
    high_red_hits = [r for r in results if r['red_hits'] >= 3]
    blue_hits = [r for r in results if r['blue_hit'] == 1]
    
    print("=== 高性能表现统计 ===")
    print(f"红球命中>=3个的期数: {len(high_red_hits)}期 ({len(high_red_hits)/len(results)*100:.1f}%)")
    print(f"蓝球命中期数: {len(blue_hits)}期 ({len(blue_hits)/len(results)*100:.1f}%)")
    print()
    
    # 最佳预测记录
    best_results = sorted(results, key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)[:10]
    print("=== 最佳预测记录 (前10期) ===")
    for i, result in enumerate(best_results, 1):
        print(f"{i:2d}. 期号:{result['period']} 红球:{result['red_hits']}/6 蓝球:{'✓' if result['blue_hit'] else '✗'}")
        print(f"    预测红球: {result['predicted_red']}")
        print(f"    实际红球: {result['actual_red']}")
        print(f"    预测蓝球: {result['predicted_blue']}, 实际蓝球: {result['actual_blue']}")
        print()
    
    return results

if __name__ == "__main__":
    results = target_focused_backtest()