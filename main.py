import requests
import json
import time
import random  # 添加 random 模块
import math
import os
from datetime import datetime
from collections import defaultdict, Counter
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')
from improved_blue_ball_predictor import ImprovedBlueBallPredictor

class AdvancedBlueBallPredictor:
    """高级蓝球预测器 - 结合深度学习、时序分析和高级统计方法"""
    
    def __init__(self, data=None):
        """初始化高级蓝球预测器"""
        self.data = data if data else []
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 神经网络参数
        self.input_dim = 32
        self.hidden_dim = 64
        self.output_dim = 16
        
        # 初始化神经网络权重
        self.init_neural_networks()
        
        # 特征权重（动态调整）
        self.feature_weights = {
            'frequency_analysis': 0.18,
            'interval_pattern': 0.16,
            'sequence_learning': 0.15,
            'distribution_modeling': 0.14,
            'trend_analysis': 0.12,
            'neural_prediction': 0.10,
            'ensemble_voting': 0.08,
            'reverse_validation': 0.07
        }
        
        # 预测历史和性能统计
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'recent_performance': []
        }
        
        # 学习率和适应性参数
        self.learning_rate = 0.01
        self.adaptation_factor = 0.95
    
    def init_neural_networks(self):
        """初始化神经网络"""
        # 时序神经网络权重
        self.lstm_weights = {
            'input_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'forget_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'output_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'cell_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1
        }
        
        # 前馈神经网络权重
        self.ffnn_weights = {
            'hidden1': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'hidden2': np.random.randn(self.hidden_dim, self.hidden_dim) * 0.1,
            'output': np.random.randn(self.hidden_dim, self.output_dim) * 0.1
        }
        
        # 注意力机制权重
        self.attention_weights = np.random.randn(self.hidden_dim, self.hidden_dim) * 0.1
    
    def extract_advanced_features(self, sequence_length=20):
        """提取高级特征"""
        if not self.data or len(self.data) < sequence_length:
            return np.zeros(self.input_dim)
        
        blue_sequence = [entry['blue'] for entry in self.data[-sequence_length:]]
        features = []
        
        # 1. 频率特征 (16维)
        frequency = Counter(blue_sequence)
        freq_features = [frequency[i] / sequence_length for i in range(1, 17)]
        features.extend(freq_features)
        
        # 2. 间隔特征 (8维)
        intervals = []
        for i in range(1, len(blue_sequence)):
            intervals.append(blue_sequence[i] - blue_sequence[i-1])
        
        if intervals:
            interval_features = [
                np.mean(intervals),
                np.std(intervals) if len(intervals) > 1 else 0,
                np.max(intervals),
                np.min(intervals),
                len([x for x in intervals if x > 0]) / len(intervals),
                len([x for x in intervals if x < 0]) / len(intervals),
                len([x for x in intervals if x == 0]) / len(intervals),
                np.median(intervals)
            ]
        else:
            interval_features = [0] * 8
        
        features.extend(interval_features)
        
        # 3. 趋势特征 (4维)
        recent_5 = blue_sequence[-5:] if len(blue_sequence) >= 5 else blue_sequence
        trend_features = [
            np.mean(recent_5),
            np.std(recent_5) if len(recent_5) > 1 else 0,
            recent_5[-1] if recent_5 else 0,
            (recent_5[-1] - recent_5[0]) / len(recent_5) if len(recent_5) > 1 else 0
        ]
        features.extend(trend_features)
        
        # 4. 分布特征 (4维)
        distribution_features = [
            len(set(blue_sequence)) / 16,  # 唯一性
            np.var(blue_sequence),  # 方差
            (max(blue_sequence) - min(blue_sequence)) / 15,  # 范围
            sum(1 for i in range(1, len(blue_sequence)) if blue_sequence[i] == blue_sequence[i-1]) / max(1, len(blue_sequence)-1)  # 连续性
        ]
        features.extend(distribution_features)
        
        # 确保特征维度正确
        features = features[:self.input_dim]
        while len(features) < self.input_dim:
            features.append(0.0)
        
        return np.array(features)
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(x)
    
    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def frequency_analysis_advanced(self):
        """高级频率分析"""
        if not self.data:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {}
        
        # 多窗口频率分析
        windows = [10, 20, 30, 50]
        for ball in self.blue_range:
            freq_scores = []
            
            for window in windows:
                recent_data = self.data[-window:] if len(self.data) >= window else self.data
                if recent_data:
                    frequency = sum(1 for entry in recent_data if entry['blue'] == ball)
                    expected_freq = len(recent_data) / 16
                    freq_deviation = abs(frequency - expected_freq) / expected_freq if expected_freq > 0 else 0
                    freq_scores.append(1 - freq_deviation)
                else:
                    freq_scores.append(0.5)
            
            scores[ball] = np.mean(freq_scores)
        
        return scores
    
    def interval_pattern_analysis(self):
        """间隔模式分析"""
        if not self.data or len(self.data) < 2:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {}
        
        for ball in self.blue_range:
            intervals = []
            last_occurrence = -1
            
            # 计算间隔
            for i, entry in enumerate(self.data):
                if entry['blue'] == ball:
                    if last_occurrence >= 0:
                        intervals.append(i - last_occurrence)
                    last_occurrence = i
            
            if intervals:
                avg_interval = np.mean(intervals)
                current_interval = len(self.data) - last_occurrence - 1 if last_occurrence >= 0 else len(self.data)
                
                # 基于泊松分布计算概率
                lambda_param = 1 / avg_interval if avg_interval > 0 else 0.1
                probability = lambda_param * np.exp(-lambda_param * current_interval)
                scores[ball] = min(probability * 10, 1.0)  # 归一化
            else:
                scores[ball] = 0.5
        
        return scores
    
    def sequence_learning(self):
        """序列学习"""
        if not self.data or len(self.data) < 10:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 寻找历史相似序列
        recent_sequence = [entry['blue'] for entry in self.data[-5:]]
        
        for i in range(len(self.data) - 10):
            historical_sequence = [entry['blue'] for entry in self.data[i:i+5]]
            
            # 计算序列相似度
            similarity = sum(1 for a, b in zip(recent_sequence, historical_sequence) if a == b)
            
            if similarity >= 2:  # 至少2个匹配
                # 预测下一个蓝球
                if i + 5 < len(self.data):
                    next_blue = self.data[i + 5]['blue']
                    scores[next_blue] += similarity / 5.0
        
        # 归一化
        max_score = max(scores.values()) if scores.values() else 1
        if max_score > 0:
            scores = {ball: score / max_score for ball, score in scores.items()}
        
        return scores
    
    def distribution_modeling(self):
        """分布建模"""
        if not self.data:
            return {ball: 1/16 for ball in self.blue_range}
        
        # 计算历史分布
        blue_counts = Counter(entry['blue'] for entry in self.data)
        total_count = len(self.data)
        
        scores = {}
        for ball in self.blue_range:
            observed_freq = blue_counts[ball] / total_count
            expected_freq = 1 / 16
            
            # 使用卡方检验思想
            chi_square = (observed_freq - expected_freq) ** 2 / expected_freq
            scores[ball] = 1 / (1 + chi_square)
        
        return scores
    
    def trend_analysis_advanced(self):
        """高级趋势分析"""
        if not self.data or len(self.data) < 20:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {}
        
        for ball in self.blue_range:
            trend_scores = []
            
            # 多周期趋势分析
            periods = [5, 10, 15, 20]
            for period in periods:
                recent_data = self.data[-period:] if len(self.data) >= period else self.data
                
                # 计算趋势强度
                occurrences = [1 if entry['blue'] == ball else 0 for entry in recent_data]
                
                if len(occurrences) > 1:
                    # 线性回归趋势
                    x = np.arange(len(occurrences))
                    y = np.array(occurrences)
                    
                    if np.var(x) > 0:
                        slope = np.cov(x, y)[0, 1] / np.var(x)
                        trend_scores.append(abs(slope))
                    else:
                        trend_scores.append(0)
                else:
                    trend_scores.append(0)
            
            scores[ball] = np.mean(trend_scores) if trend_scores else 0.5
        
        return scores
    
    def neural_prediction(self):
        """神经网络预测"""
        features = self.extract_advanced_features()
        
        # LSTM前向传播
        lstm_output = self.lstm_forward(features)
        
        # FFNN前向传播
        ffnn_output = self.ffnn_forward(features)
        
        # 注意力机制
        attention_output = self.attention_mechanism(lstm_output, ffnn_output)
        
        # 确保输出维度正确
        if len(attention_output) != self.output_dim:
            attention_output = np.resize(attention_output, self.output_dim)
        
        # 应用softmax获得概率分布
        probabilities = self.softmax(attention_output)
        
        # 转换为球号得分
        scores = {ball: probabilities[ball-1] for ball in self.blue_range}
        
        return scores
    
    def lstm_forward(self, features):
        """LSTM前向传播"""
        h = np.zeros(self.hidden_dim)
        c = np.zeros(self.hidden_dim)
        
        # 输入门
        i_gate = self.sigmoid(np.dot(features, self.lstm_weights['input_gate']))
        
        # 遗忘门
        f_gate = self.sigmoid(np.dot(features, self.lstm_weights['forget_gate']))
        
        # 输出门
        o_gate = self.sigmoid(np.dot(features, self.lstm_weights['output_gate']))
        
        # 候选值
        c_tilde = self.tanh(np.dot(features, self.lstm_weights['cell_gate']))
        
        # 更新细胞状态
        c = f_gate * c + i_gate * c_tilde
        
        # 更新隐藏状态
        h = o_gate * self.tanh(c)
        
        return h
    
    def ffnn_forward(self, features):
        """前馈神经网络前向传播"""
        # 第一层
        hidden1 = self.relu(np.dot(features, self.ffnn_weights['hidden1']))
        
        # 第二层
        hidden2 = self.relu(np.dot(hidden1, self.ffnn_weights['hidden2']))
        
        # 输出层
        output = np.dot(hidden2, self.ffnn_weights['output'])
        
        return output
    
    def attention_mechanism(self, lstm_output, ffnn_output):
        """注意力机制"""
        # 确保查询向量是一维的
        if lstm_output.ndim > 1:
            query = lstm_output.flatten()
        else:
            query = lstm_output
        
        # 确保键向量是一维的
        if ffnn_output.ndim > 1:
            key = ffnn_output.flatten()
        else:
            key = ffnn_output
        
        # 调整维度以匹配注意力权重
        if len(query) != self.attention_weights.shape[0]:
            query = np.resize(query, self.attention_weights.shape[0])
        
        # 计算注意力分数
        attention_scores = np.dot(query, self.attention_weights)
        attention_weights = self.softmax(attention_scores)
        
        # 确保值向量维度匹配
        if len(key) != len(attention_weights):
            key = np.resize(key, len(attention_weights))
        
        # 加权求和
        attended_output = np.sum(attention_weights * key)
        
        # 确保输出是向量形式
        if np.isscalar(attended_output):
            attended_output = np.full(self.output_dim, attended_output / self.output_dim)
        elif len(attended_output) != self.output_dim:
            attended_output = np.resize(attended_output, self.output_dim)
        
        return attended_output
    
    def ensemble_voting(self, all_predictions):
        """集成投票"""
        ensemble_scores = {ball: 0.0 for ball in self.blue_range}
        
        for predictions in all_predictions:
            if predictions:
                for ball, score in predictions.items():
                    ensemble_scores[ball] += score
        
        # 归一化
        total_score = sum(ensemble_scores.values())
        if total_score > 0:
            ensemble_scores = {ball: score / total_score for ball, score in ensemble_scores.items()}
        
        return ensemble_scores
    
    def reverse_validation(self):
        """反向验证"""
        if not self.data or len(self.data) < 30:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 使用历史数据进行反向预测验证
        validation_periods = min(20, len(self.data) - 10)
        
        for i in range(validation_periods):
            # 使用前面的数据预测
            train_data = self.data[:-(validation_periods-i)]
            actual_blue = self.data[-(validation_periods-i)]['blue']
            
            # 简化预测（基于频率）
            blue_counts = Counter(entry['blue'] for entry in train_data[-20:])
            predicted_blue = blue_counts.most_common(1)[0][0] if blue_counts else random.choice(self.blue_range)
            
            # 如果预测正确，增加该球的得分
            if predicted_blue == actual_blue:
                scores[actual_blue] += 1.0
        
        # 归一化
        max_score = max(scores.values()) if scores.values() else 1
        if max_score > 0:
            scores = {ball: score / max_score for ball, score in scores.items()}
        
        return scores
    
    def dynamic_weight_adjustment(self):
        """动态权重调整"""
        if len(self.performance_stats['recent_performance']) < 10:
            return
        
        recent_performance = np.mean(self.performance_stats['recent_performance'][-10:])
        
        # 根据近期表现调整权重
        if recent_performance > 0.15:  # 表现好，增加神经网络权重
            self.feature_weights['neural_prediction'] *= 1.1
            self.feature_weights['frequency_analysis'] *= 0.95
        elif recent_performance < 0.08:  # 表现差，增加传统方法权重
            self.feature_weights['frequency_analysis'] *= 1.1
            self.feature_weights['neural_prediction'] *= 0.95
        
        # 权重归一化
        total_weight = sum(self.feature_weights.values())
        for key in self.feature_weights:
            self.feature_weights[key] /= total_weight
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各种预测得分
        freq_scores = self.frequency_analysis_advanced()
        interval_scores = self.interval_pattern_analysis()
        sequence_scores = self.sequence_learning()
        distribution_scores = self.distribution_modeling()
        trend_scores = self.trend_analysis_advanced()
        neural_scores = self.neural_prediction()
        reverse_scores = self.reverse_validation()
        
        # 集成投票
        all_predictions = [
            freq_scores, interval_scores, sequence_scores,
            distribution_scores, trend_scores, neural_scores, reverse_scores
        ]
        ensemble_scores = self.ensemble_voting(all_predictions)
        
        # 计算最终得分
        final_scores = {}
        for ball in self.blue_range:
            final_score = (
                freq_scores[ball] * self.feature_weights['frequency_analysis'] +
                interval_scores[ball] * self.feature_weights['interval_pattern'] +
                sequence_scores[ball] * self.feature_weights['sequence_learning'] +
                distribution_scores[ball] * self.feature_weights['distribution_modeling'] +
                trend_scores[ball] * self.feature_weights['trend_analysis'] +
                neural_scores[ball] * self.feature_weights['neural_prediction'] +
                ensemble_scores[ball] * self.feature_weights['ensemble_voting'] +
                reverse_scores[ball] * self.feature_weights['reverse_validation']
            )
            final_scores[ball] = final_score
        
        # 多策略选择
        predictions = []
        
        # 策略1：最高分
        best_ball = max(final_scores.items(), key=lambda x: x[1])[0]
        predictions.append(best_ball)
        
        # 策略2：概率采样
        total_score = sum(final_scores.values())
        if total_score > 0:
            probs = [final_scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 策略3：神经网络优先
        neural_best = max(neural_scores.items(), key=lambda x: x[1])[0]
        predictions.append(neural_best)
        
        # 策略4：集成优先
        ensemble_best = max(ensemble_scores.items(), key=lambda x: x[1])[0]
        predictions.append(ensemble_best)
        
        # 投票决策
        vote_count = Counter(predictions)
        final_prediction = vote_count.most_common(1)[0][0]
        
        # 记录预测
        self.prediction_history.append({
            'prediction': final_prediction,
            'final_scores': final_scores,
            'component_scores': {
                'frequency': freq_scores[final_prediction],
                'interval': interval_scores[final_prediction],
                'sequence': sequence_scores[final_prediction],
                'distribution': distribution_scores[final_prediction],
                'trend': trend_scores[final_prediction],
                'neural': neural_scores[final_prediction],
                'ensemble': ensemble_scores[final_prediction],
                'reverse': reverse_scores[final_prediction]
            },
            'timestamp': datetime.now().isoformat()
        })
        
        return final_prediction
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 记录最近性能
        self.performance_stats['recent_performance'].append(1.0 if is_correct else 0.0)
        if len(self.performance_stats['recent_performance']) > 50:
            self.performance_stats['recent_performance'].pop(0)
        
        # 动态调整权重
        self.dynamic_weight_adjustment()
    
    def get_performance_report(self):
        """获取性能报告"""
        recent_hit_rate = 0.0
        if self.performance_stats['recent_performance']:
            recent_hit_rate = np.mean(self.performance_stats['recent_performance'])
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'overall_hit_rate': self.performance_stats['hit_rate'],
            'recent_hit_rate': recent_hit_rate,
            'feature_weights': self.feature_weights.copy()
        }

# JisuAPI参数
APPKEY = 'eb6cc4f9bf4a23b4'  # 用户提供的appkey
DATA_FILE = 'ssq_data.json'  # 数据文件名

def check_existing_data():
    """检查现有数据文件，返回数据和最新期号"""
    if not os.path.exists(DATA_FILE):
        return [], None
    
    try:
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        if data:
            # 获取最新期号（第一条记录是最新的）
            latest_period = data[0]['issueno']
            print(f"本地数据最新期号: {latest_period}，共{len(data)}期数据")
            return data, latest_period
        return [], None
    except Exception as e:
        print(f"读取本地数据失败: {e}")
        return [], None

def get_latest_period():
    """获取最新一期的期号，只请求1条数据"""
    url = f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=11&num=1&start=0'
    try:
        response = requests.get(url)
        data = response.json()
        if data['status'] != 0:
            raise ValueError(data['msg'])
        if data['result']['list']:
            latest_period = data['result']['list'][0]['issueno']
            print(f"API最新期号: {latest_period}")
            return latest_period
        return None
    except Exception as e:
        print(f"获取最新期号失败: {e}")
        return None

def get_missing_data(start_period, count):
    """获取缺失的数据，从指定期号开始获取count条"""
    results = []
    start = 0
    num = min(20, count)  # 每页大小，JisuAPI最大20
    
    print(f"开始获取缺失数据，预计需要{count}条...")
    
    while len(results) < count:
        remaining = count - len(results)
        current_num = min(num, remaining)
        
        url = f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=11&num={current_num}&start={start}'
        try:
            response = requests.get(url)
            data = response.json()
            if data['status'] != 0:
                raise ValueError(data['msg'])
            
            page_results = data['result']['list']
            if not page_results:
                break
                
            results.extend(page_results)
            start += current_num
            print(f"已获取{len(results)}条数据...")
            time.sleep(0.5)  # 避免API调用频率过高
            
        except Exception as e:
            print(f"获取数据失败: {e}")
            break
    
    print(f"实际获取到{len(results)}条新数据")
    return results

def smart_get_ssq_data(target_count=1000):
    """智能获取双色球数据，优先使用本地数据，只获取缺失部分"""
    # 检查本地数据
    local_data, local_latest_period = check_existing_data()
    
    # 获取API最新期号
    api_latest_period = get_latest_period()
    if not api_latest_period:
        if local_data:
            print("无法获取最新期号，使用本地数据")
            return local_data[:target_count]
        else:
            raise ValueError("无法获取数据")
    
    # 如果本地数据为空，获取全部数据
    if not local_data:
        print("本地无数据，开始获取完整数据集...")
        new_data = get_missing_data(None, target_count)
        if new_data:
            save_data(new_data)
        return new_data
    
    # 如果本地数据已是最新，直接使用
    if local_latest_period == api_latest_period:
        print("本地数据已是最新，无需更新")
        if len(local_data) >= target_count:
            return local_data[:target_count]
        else:
            # 本地数据不够，补充历史数据
            missing_count = target_count - len(local_data)
            print(f"本地数据不足，需要补充{missing_count}条历史数据")
            additional_data = get_missing_data(None, missing_count)
            if additional_data:
                # 合并数据，去重
                combined_data = local_data + [item for item in additional_data if item['issueno'] not in [d['issueno'] for d in local_data]]
                combined_data.sort(key=lambda x: x['issueno'], reverse=True)  # 按期号降序排列
                save_data(combined_data)
                return combined_data[:target_count]
            return local_data
    
    # 本地数据过时，获取新数据
    print(f"检测到新开奖数据，本地期号: {local_latest_period}, 最新期号: {api_latest_period}")
    
    # 计算需要获取多少条新数据（估算）
    try:
        local_period_num = int(local_latest_period)
        api_period_num = int(api_latest_period)
        missing_periods = api_period_num - local_period_num
        missing_count = max(missing_periods, 1)  # 至少获取1条
    except:
        missing_count = 10  # 如果期号格式特殊，默认获取10条
    
    print(f"预计需要获取{missing_count}条新数据")
    new_data = get_missing_data(None, missing_count + 10)  # 多获取一些以确保完整性
    
    if new_data:
        # 合并新旧数据，去重
        combined_data = new_data + [item for item in local_data if item['issueno'] not in [d['issueno'] for d in new_data]]
        combined_data.sort(key=lambda x: x['issueno'], reverse=True)  # 按期号降序排列
        
        # 保持目标数量
        final_data = combined_data[:target_count]
        save_data(final_data)
        
        print(f"数据更新完成，当前共有{len(final_data)}期数据")
        return final_data
    else:
        print("获取新数据失败，使用本地数据")
        return local_data[:target_count]

def save_data(data):
    """保存数据到文件"""
    try:
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"数据已保存到 {DATA_FILE}")
    except Exception as e:
        print(f"保存数据失败: {e}")

class UltimateIntegratedPredictor:
    """终极集成预测器 - 融合所有算法优势"""
    
    def __init__(self, data_file='ssq_data.json'):
        self.data = self.load_data(data_file)
        self.red_range = list(range(1, 34))
        self.blue_range = list(range(1, 17))
        
        # 初始化高级蓝球预测器
        self.advanced_blue_predictor = AdvancedBlueBallPredictor(self.data)
        
        # 集成学习权重
        self.ensemble_weights = {
            'frequency': 0.20,
            'position': 0.15,
            'interval': 0.15,
            'trend': 0.15,
            'neural': 0.20,
            'quantum': 0.15
        }
        
        # 动态策略权重
        self.strategy_performance = {
            'conservative': {'score': 0.6, 'weight': 0.3},
            'balanced': {'score': 0.7, 'weight': 0.4},
            'aggressive': {'score': 0.65, 'weight': 0.3}
        }
        
        # 基于数据生成动态随机种子
        data_length = len(self.data)
        data_hash = hash(str(self.data[-10:]) if self.data else "empty") % 10000
        dynamic_seed = (data_length * 17 + data_hash) % 10000
        np.random.seed(dynamic_seed)
        random.seed(dynamic_seed)  # 同时设置random模块的种子
        
        # 改进马尔科夫算法相关属性
        self.repeat_stats = self.calculate_repeat_probabilities()
        self.markov_weights = {'red': {}, 'blue': {}}
        self.update_markov_weights()
        
        # 神经网络权重（简化版）
        self.neural_weights = np.random.randn(8, 16) * 0.1
        self.neural_hidden = np.random.randn(16, 8) * 0.1
        self.neural_output = np.random.randn(8, 1) * 0.1
        
        # 蓝球专用神经网络
        self.blue_neural_weights = np.random.randn(8, 12) * 0.1
        self.blue_hidden_weights = np.random.randn(12, 8) * 0.1
        self.blue_output_weights = np.random.randn(8, 1) * 0.1
        
        print(f"终极集成预测器初始化完成，历史数据：{len(self.data)}期")
        if self.repeat_stats:
            red_repeat_rate = np.mean(self.repeat_stats['red_repeats']) if self.repeat_stats['red_repeats'] else 0
            print(f"重复概率统计：红球平均重复率 {red_repeat_rate:.2%}")
    
    def load_data(self, data_file):
        """加载数据"""
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # 转换数据格式
            formatted_data = []
            for entry in data:
                # 解析红球号码字符串，如"04 06 09 14 32 33"
                red_str = entry['number'].strip().split(' ')
                red = sorted([int(x.strip()) for x in red_str if x.strip()])
                blue = int(entry['refernumber'])
                formatted_data.append({'red': red, 'blue': blue})
            return formatted_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(x)
    
    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def calculate_repeat_probabilities(self):
        """计算号码重复概率统计"""
        if len(self.data) < 2:
            return None
        
        red_repeats = []
        blue_repeats = []
        repeat_patterns = defaultdict(int)
        
        for i in range(1, len(self.data)):
            current_red = set(self.data[i]['red'])
            prev_red = set(self.data[i-1]['red'])
            red_repeat_count = len(current_red & prev_red)
            red_repeats.append(red_repeat_count)
            
            blue_repeat = 1 if self.data[i]['blue'] == self.data[i-1]['blue'] else 0
            blue_repeats.append(blue_repeat)
            
            # 统计重复模式
            pattern_key = f"red_{red_repeat_count}_blue_{blue_repeat}"
            repeat_patterns[pattern_key] += 1
        
        return {
            'red_repeats': red_repeats,
            'blue_repeats': blue_repeats,
            'repeat_patterns': dict(repeat_patterns),
            'red_repeat_rate': np.mean(red_repeats) if red_repeats else 0,
            'blue_repeat_rate': np.mean(blue_repeats) if blue_repeats else 0
        }
    
    def update_markov_weights(self):
        """更新马尔科夫链权重"""
        if not self.data:
            return
        
        # 红球权重计算
        self.markov_weights['red'] = self.build_markov_weights(self.red_range, is_blue=False)
        
        # 蓝球权重计算
        self.markov_weights['blue'] = self.build_markov_weights(self.blue_range, is_blue=True)
    
    def build_markov_weights(self, numbers, is_blue=False):
        """构建马尔科夫链权重"""
        weights = {}
        recent_periods = min(50, len(self.data))  # 最近50期
        
        for num in numbers:
            weight = 0
            for j in range(recent_periods):
                period_data = self.data[j]  # 数据已按期号降序排列
                period_weight = 1.0 / (j + 1)  # 越近权重越大
                
                if is_blue:
                    if period_data['blue'] == num:
                        weight += period_weight
                else:
                    if num in period_data['red']:
                        weight += period_weight
            
            weights[str(num)] = weight
        
        return weights
    
    def enhanced_data_analysis(self):
        """增强型数据分析"""
        if not self.data:
            return self._default_analysis()
        
        analysis = {
            'red_frequency': defaultdict(int),
            'blue_frequency': defaultdict(int),
            'red_position_freq': [defaultdict(int) for _ in range(6)],
            'red_intervals': defaultdict(list),
            'blue_intervals': defaultdict(list),
            'red_hot_trend': defaultdict(list),
            'blue_hot_trend': defaultdict(list),
            'red_distribution': {'low': 0, 'mid': 0, 'high': 0},
            'blue_distribution': {'low': 0, 'mid': 0, 'high': 0},
            'time_series_red': [],
            'time_series_blue': []
        }
        
        # 统计频率和位置
        for i, record in enumerate(self.data):
            red_balls = record['red']
            blue_ball = record['blue']
            
            # 红球统计
            for j, ball in enumerate(red_balls):
                analysis['red_frequency'][ball] += 1
                analysis['red_position_freq'][j][ball] += 1
                analysis['time_series_red'].append(ball)
            
            # 蓝球统计
            analysis['blue_frequency'][blue_ball] += 1
            analysis['time_series_blue'].append(blue_ball)
            
            # 分布统计
            low_count = sum(1 for ball in red_balls if ball <= 11)
            mid_count = sum(1 for ball in red_balls if 12 <= ball <= 22)
            high_count = sum(1 for ball in red_balls if ball >= 23)
            
            analysis['red_distribution']['low'] += low_count
            analysis['red_distribution']['mid'] += mid_count
            analysis['red_distribution']['high'] += high_count
            
            if blue_ball <= 5:
                analysis['blue_distribution']['low'] += 1
            elif blue_ball <= 10:
                analysis['blue_distribution']['mid'] += 1
            else:
                analysis['blue_distribution']['high'] += 1
        
        # 计算间隔
        self._calculate_intervals(analysis)
        
        # 计算趋势
        self._calculate_trends(analysis)
        
        return analysis
    
    def _calculate_intervals(self, analysis):
        """计算号码间隔"""
        # 红球间隔
        for ball in self.red_range:
            last_appear = -1
            for i, record in enumerate(self.data):
                if ball in record['red']:
                    if last_appear >= 0:
                        analysis['red_intervals'][ball].append(i - last_appear)
                    last_appear = i
        
        # 蓝球间隔
        for ball in self.blue_range:
            last_appear = -1
            for i, record in enumerate(self.data):
                if ball == record['blue']:
                    if last_appear >= 0:
                        analysis['blue_intervals'][ball].append(i - last_appear)
                    last_appear = i
    
    def _calculate_trends(self, analysis):
        """计算热度趋势"""
        recent_periods = 30
        if len(self.data) < recent_periods:
            return
        
        recent_data = self.data[-recent_periods:]
        
        # 红球趋势
        for ball in self.red_range:
            for record in recent_data:
                if ball in record['red']:
                    analysis['red_hot_trend'][ball].append(1)
        
        # 蓝球趋势
        for ball in self.blue_range:
            for record in recent_data:
                if ball == record['blue']:
                    analysis['blue_hot_trend'][ball].append(1)
    
    def neural_network_prediction(self, features):
        """神经网络预测"""
        # 输入层到隐藏层
        hidden = self.sigmoid(np.dot(features, self.neural_weights))
        # 隐藏层到输出层
        hidden2 = self.tanh(np.dot(hidden, self.neural_hidden))
        # 输出层
        output = self.sigmoid(np.dot(hidden2, self.neural_output))
        return output[0]
    
    def blue_neural_network_prediction(self, features):
        """蓝球专用神经网络预测"""
        # 输入层到隐藏层
        hidden = self.sigmoid(np.dot(features, self.blue_neural_weights))
        # 隐藏层到输出层
        hidden2 = self.tanh(np.dot(hidden, self.blue_hidden_weights))
        # 输出层
        output = self.sigmoid(np.dot(hidden2, self.blue_output_weights))
        return output[0]
    
    def quantum_inspired_fusion(self, scores_dict):
        """量子启发的特征融合"""
        quantum_scores = {}
        
        for ball in self.red_range:
            # 量子叠加态
            amplitudes = []
            for feature, weight in self.ensemble_weights.items():
                if feature in scores_dict and ball in scores_dict[feature]:
                    amplitude = math.sqrt(scores_dict[feature][ball] * weight)
                    amplitudes.append(amplitude)
            
            if amplitudes:
                # 量子干涉
                interference = sum(amplitudes) / len(amplitudes)
                # 概率密度
                probability = interference ** 2
                quantum_scores[ball] = probability
            else:
                quantum_scores[ball] = 0.1
        
        return quantum_scores
    
    def calculate_red_scores_ultimate(self, analysis):
        """终极红球得分计算"""
        scores = {
            'frequency': {},
            'position': {},
            'interval': {},
            'trend': {},
            'neural': {},
            'quantum': {}
        }
        
        # 1. 频率得分
        max_freq = max(analysis['red_frequency'].values()) if analysis['red_frequency'] else 1
        for ball in self.red_range:
            freq = analysis['red_frequency'].get(ball, 0)
            scores['frequency'][ball] = freq / max_freq
        
        # 2. 位置得分
        for ball in self.red_range:
            position_score = 0
            for pos in range(6):
                if ball in analysis['red_position_freq'][pos]:
                    position_score += analysis['red_position_freq'][pos][ball]
            scores['position'][ball] = position_score / 6
        
        # 3. 间隔得分
        for ball in self.red_range:
            intervals = analysis['red_intervals'].get(ball, [10])
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                scores['interval'][ball] = 1 / (1 + avg_interval * 0.1)
            else:
                scores['interval'][ball] = 0.1
        
        # 4. 趋势得分
        for ball in self.red_range:
            trend_count = len(analysis['red_hot_trend'].get(ball, []))
            scores['trend'][ball] = trend_count / 30
        
        # 5. 神经网络得分
        for ball in self.red_range:
            features = np.array([
                scores['frequency'][ball],
                scores['position'][ball],
                scores['interval'][ball],
                scores['trend'][ball],
                analysis['red_distribution']['low'] / 100,
                analysis['red_distribution']['mid'] / 100,
                analysis['red_distribution']['high'] / 100,
                random.random() * 0.1
            ])
            scores['neural'][ball] = self.neural_network_prediction(features)
        
        # 6. 量子融合得分
        scores['quantum'] = self.quantum_inspired_fusion(scores)
        
        return scores
    
    def improved_markov_prediction(self, num_combinations=5):
        """改进的马尔科夫预测算法"""
        combinations = []
        
        # 获取最优同号数量范围
        optimal_same_counts = self.get_optimal_same_count_range()
        
        for i in range(num_combinations):
             # 动态种子确保每次生成不同组合
             current_time = int(time.time() * 1000) % 10000
             data_hash = hash(str(self.data[-10:]) if self.data else "empty") % 10000
             combo_seed = (data_hash + i * 17 + current_time) % 10000
             np.random.seed(combo_seed)
             random.seed(combo_seed)
             
             # 根据同号概率控制生成红球组合
             target_same_count = random.choice(optimal_same_counts)
             red_combo = self.generate_red_combination_with_same_control(target_same_count)
             
             # 生成蓝球 - 使用改进的蓝球预测器
             improved_blue_predictor = ImprovedBlueBallPredictor(self.data)
             blue_combo = improved_blue_predictor.predict_blue_ball()
             
             # 计算增强组合得分
             score = self.calculate_combination_score_enhanced(red_combo, blue_combo, target_same_count)
             
             combinations.append({
                 'red': sorted(red_combo),
                 'blue': blue_combo,
                 'score': score,
                 'same_count': target_same_count
             })
        
        # 按得分排序
        combinations.sort(key=lambda x: x['score'], reverse=True)
        
        return combinations
    
    def generate_red_combination_improved(self):
        """生成改进的红球组合"""
        # 结合马尔科夫权重和重复概率
        red_weights = []
        for num in self.red_range:
            markov_weight = self.markov_weights['red'].get(str(num), 0.1)
            
            # 考虑重复概率调整
            if self.repeat_stats and self.data:
                last_red = set(self.data[0]['red'])  # 最新一期红球
                if num in last_red:
                    # 如果号码在上期出现，根据重复率调整权重
                    repeat_factor = self.repeat_stats['red_repeat_rate'] / 6  # 平均到每个号码
                    markov_weight *= (1 + repeat_factor)
                else:
                    # 未在上期出现的号码，稍微提升权重
                    markov_weight *= 1.1
            
            red_weights.append(markov_weight)
        
        # 归一化权重
        total_weight = sum(red_weights)
        if total_weight > 0:
            red_weights = [w / total_weight for w in red_weights]
        else:
            red_weights = [1/len(self.red_range)] * len(self.red_range)
        
        # 选择7个红球
        selected_red = np.random.choice(
            self.red_range, 
            size=7, 
            replace=False, 
            p=red_weights
        )
        
        return list(selected_red)
    
    def generate_blue_combination_improved(self):
        """生成改进的蓝球组合"""
        blue_weights = []
        for num in self.blue_range:
            markov_weight = self.markov_weights['blue'].get(str(num), 0.1)
            
            # 考虑蓝球重复概率
            if self.repeat_stats and self.data:
                last_blue = self.data[0]['blue']  # 最新一期蓝球
                if num == last_blue:
                    # 如果是上期蓝球，根据重复率调整
                    repeat_factor = self.repeat_stats['blue_repeat_rate']
                    markov_weight *= (1 + repeat_factor * 0.5)  # 降低权重加成
                else:
                    markov_weight *= 1.02  # 降低非上期蓝球的权重调整
            
            blue_weights.append(markov_weight)
        
        # 归一化权重
        total_weight = sum(blue_weights)
        if total_weight > 0:
            blue_weights = [w / total_weight for w in blue_weights]
        else:
            blue_weights = [1/len(self.blue_range)] * len(self.blue_range)
        
        # 选择1个蓝球
        selected_blue = np.random.choice(
            self.blue_range,
            p=blue_weights
        )
        
        return int(selected_blue)
    
    def calculate_combination_score(self, red_combo, blue_combo):
        """计算组合得分"""
        score = 0
        
        # 马尔科夫权重得分
        for num in red_combo:
            score += self.markov_weights['red'].get(str(num), 0.1)
        score += self.markov_weights['blue'].get(str(blue_combo), 0.1)
        
        # 重复概率得分
        if self.repeat_stats and self.data:
            last_red = set(self.data[0]['red'])
            last_blue = self.data[0]['blue']
            
            # 红球重复得分
            red_repeat_count = len(set(red_combo) & last_red)
            expected_repeat = self.repeat_stats['red_repeat_rate']
            repeat_score = 1 - abs(red_repeat_count - expected_repeat) / 6
            score += repeat_score * 0.3
            
            # 蓝球重复得分
            if blue_combo == last_blue:
                score += self.repeat_stats['blue_repeat_rate'] * 0.2
            else:
                score += (1 - self.repeat_stats['blue_repeat_rate']) * 0.2
        
        # 号码分布得分（避免过于集中）
        red_spread = max(red_combo) - min(red_combo)
        spread_score = min(red_spread / 30, 1)  # 理想分布跨度
        score += spread_score * 0.1
        
        return score
    
    def get_optimal_same_count_range(self):
        """获取最优同号数量范围"""
        # 基于历史概率分析，返回最优的同号数量
        # 根据same_number_analysis.py的分析结果
        # 1个同号: 45.35%, 0个同号: 27.13%, 2个同号: 22.42%
        return [1, 0, 2, 1, 0]  # 权重分布，1个同号出现更频繁
    
    def count_same_numbers(self, red_combo):
        """计算与上期开奖号码的同号数量"""
        if not self.data:
            return 0
        last_red = set(self.data[0]['red'])
        return len(set(red_combo) & last_red)
    
    def generate_red_combination_with_same_control(self, target_same_count):
        """根据目标同号数量生成红球组合"""
        if not self.data:
            return self.generate_red_combination_improved()
        
        last_red = set(self.data[0]['red'])
        max_attempts = 100
        
        for attempt in range(max_attempts):
            # 先选择同号
            same_numbers = []
            if target_same_count > 0 and last_red:
                available_same = list(last_red)
                same_count = min(target_same_count, len(available_same))
                same_numbers = random.sample(available_same, same_count)
            
            # 选择非同号
            remaining_count = 7 - len(same_numbers)
            available_different = [num for num in self.red_range if num not in last_red]
            
            if len(available_different) >= remaining_count:
                # 使用马尔科夫权重选择非同号
                different_weights = []
                for num in available_different:
                    weight = self.markov_weights['red'].get(str(num), 0.1)
                    different_weights.append(weight)
                
                # 归一化权重
                total_weight = sum(different_weights)
                if total_weight > 0:
                    different_weights = [w / total_weight for w in different_weights]
                else:
                    different_weights = [1/len(available_different)] * len(available_different)
                
                different_numbers = list(np.random.choice(
                    available_different,
                    size=remaining_count,
                    replace=False,
                    p=different_weights
                ))
                
                red_combo = same_numbers + different_numbers
                
                # 验证同号数量
                actual_same_count = self.count_same_numbers(red_combo)
                if actual_same_count == target_same_count:
                    return red_combo
        
        # 如果无法达到目标同号数量，返回改进的马尔科夫组合
        return self.generate_red_combination_improved()
    
    def calculate_combination_score_enhanced(self, red_combo, blue_combo, target_same_count):
        """计算增强的组合得分，考虑同号概率"""
        # 基础得分
        base_score = self.calculate_combination_score(red_combo, blue_combo)
        
        # 同号概率奖励
        actual_same_count = self.count_same_numbers(red_combo)
        same_count_bonus = 0
        
        # 根据历史概率给予奖励
        if actual_same_count == 1:
            same_count_bonus = 0.4535  # 45.35%概率
        elif actual_same_count == 0:
            same_count_bonus = 0.2713  # 27.13%概率
        elif actual_same_count == 2:
            same_count_bonus = 0.2242  # 22.42%概率
        elif actual_same_count == 3:
            same_count_bonus = 0.048   # 4.80%概率
        else:
            same_count_bonus = 0.01    # 其他情况很少
        
        # 目标匹配奖励
        target_match_bonus = 0.2 if actual_same_count == target_same_count else 0
        
        return base_score + same_count_bonus + target_match_bonus
    
    def calculate_blue_scores_ultimate(self, analysis):
        """终极蓝球得分计算"""
        blue_scores = {}
        
        max_freq = max(analysis['blue_frequency'].values()) if analysis['blue_frequency'] else 1
        
        for ball in self.blue_range:
            # 频率得分
            freq_score = analysis['blue_frequency'].get(ball, 0) / max_freq
            
            # 间隔得分
            intervals = analysis['blue_intervals'].get(ball, [8])
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                interval_score = 1 / (1 + avg_interval * 0.1)
            else:
                interval_score = 0.1
            
            # 趋势得分
            trend_count = len(analysis['blue_hot_trend'].get(ball, []))
            trend_score = trend_count / 30
            
            # 分布得分
            if ball <= 5:
                dist_score = analysis['blue_distribution']['low'] / 100
            elif ball <= 10:
                dist_score = analysis['blue_distribution']['mid'] / 100
            else:
                dist_score = analysis['blue_distribution']['high'] / 100
            
            # 神经网络特征
            features = np.array([
                freq_score, interval_score, trend_score, dist_score,
                math.sin(ball * math.pi / 16),
                math.cos(ball * math.pi / 16),
                ball / 16,
                random.random() * 0.05
            ])
            
            # 深度神经网络预测
            neural_score = self.blue_neural_network_prediction(features)
            
            # 时序注意力
            recent_blues = analysis['time_series_blue'][-20:]
            attention_score = recent_blues.count(ball) / len(recent_blues) if recent_blues else 0
            
            # 综合得分
            blue_scores[ball] = (
                freq_score * 0.25 +
                interval_score * 0.20 +
                trend_score * 0.15 +
                neural_score * 0.25 +
                attention_score * 0.15
            )
        
        return blue_scores
    
    def dynamic_strategy_selection(self, red_scores, analysis):
        """动态策略选择"""
        strategies = {
            'conservative': self.conservative_selection,
            'balanced': self.balanced_selection,
            'aggressive': self.aggressive_selection
        }
        
        # 根据历史表现选择策略
        best_strategy = max(self.strategy_performance.keys(), 
                          key=lambda x: self.strategy_performance[x]['score'])
        
        # 策略权重归一化
        total_weight = sum(s['weight'] for s in self.strategy_performance.values())
        normalized_weights = {k: v['weight']/total_weight for k, v in self.strategy_performance.items()}
        
        # 加权组合选择
        final_selection = set()
        for strategy_name, weight in normalized_weights.items():
            strategy_func = strategies[strategy_name]
            selection = strategy_func(red_scores, analysis)
            num_to_add = max(1, int(7 * weight))
            final_selection.update(list(selection)[:num_to_add])
        
        # 确保选择7个球
        if len(final_selection) < 7:
            remaining = set(self.red_range) - final_selection
            sorted_remaining = sorted(remaining, key=lambda x: sum(red_scores[feature].get(x, 0) 
                                                                 for feature in red_scores), reverse=True)
            final_selection.update(sorted_remaining[:7-len(final_selection)])
        elif len(final_selection) > 7:
            scored_selection = [(ball, sum(red_scores[feature].get(ball, 0) for feature in red_scores)) 
                              for ball in final_selection]
            scored_selection.sort(key=lambda x: x[1], reverse=True)
            final_selection = set([ball for ball, _ in scored_selection[:7]])
        
        return list(final_selection)
    
    def conservative_selection(self, red_scores, analysis):
        """保守策略"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = (
                red_scores['frequency'].get(ball, 0) * 0.4 +
                red_scores['position'].get(ball, 0) * 0.3 +
                red_scores['trend'].get(ball, 0) * 0.3
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def balanced_selection(self, red_scores, analysis):
        """平衡策略"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = sum(
                red_scores[feature].get(ball, 0) * weight 
                for feature, weight in self.ensemble_weights.items()
                if feature in red_scores
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        selected = [ball for ball, _ in sorted_balls[:7]]
        
        # 区间平衡
        low_count = sum(1 for ball in selected if ball <= 11)
        mid_count = sum(1 for ball in selected if 12 <= ball <= 22)
        high_count = sum(1 for ball in selected if ball >= 23)
        
        # 调整平衡
        if low_count > 3 or high_count > 3:
            low_balls = [ball for ball, _ in sorted_balls if ball <= 11][:2]
            mid_balls = [ball for ball, _ in sorted_balls if 12 <= ball <= 22][:3]
            high_balls = [ball for ball, _ in sorted_balls if ball >= 23][:2]
            selected = low_balls + mid_balls + high_balls
        
        return selected[:7]
    
    def aggressive_selection(self, red_scores, analysis):
        """激进策略"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = (
                red_scores['neural'].get(ball, 0) * 0.4 +
                red_scores['quantum'].get(ball, 0) * 0.3 +
                red_scores['interval'].get(ball, 0) * 0.3
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def select_blue_ball_ultimate(self, blue_scores, analysis):
        """终极蓝球选择 - 使用高级蓝球预测器"""
        try:
            # 优先使用高级蓝球预测器
            advanced_blue = self.advanced_blue_predictor.predict_blue_ball()
            
            # 如果高级预测器返回有效结果，优先使用
            if advanced_blue and 1 <= advanced_blue <= 16:
                print(f"使用高级蓝球预测器预测: {advanced_blue}")
                return advanced_blue
            
            print("高级预测器无效，使用原有多模型融合策略")
            
        except Exception as e:
            print(f"高级蓝球预测器出错: {e}，使用原有策略")
        
        # 降级到原有的多模型融合预测
        predictions = []
        
        # 模型1：直接得分排序
        sorted_blues = sorted(blue_scores.items(), key=lambda x: x[1], reverse=True)
        predictions.append(sorted_blues[0][0])
        
        # 模型2：随机森林思想
        top_candidates = [ball for ball, _ in sorted_blues[:5]]
        weights = [score for _, score in sorted_blues[:5]]
        if sum(weights) > 0:
            normalized_weights = [w/sum(weights) for w in weights]
            predictions.append(np.random.choice(top_candidates, p=normalized_weights))
        
        # 模型3：时序模式匹配
        recent_blues = analysis['time_series_blue'][-10:]
        pattern_scores = defaultdict(float)
        for i in range(len(analysis['time_series_blue']) - 10):
            historical_pattern = analysis['time_series_blue'][i:i+10]
            similarity = sum(1 for a, b in zip(recent_blues, historical_pattern) if a == b)
            if similarity >= 3:
                next_blue = analysis['time_series_blue'][i+10] if i+10 < len(analysis['time_series_blue']) else None
                if next_blue:
                    pattern_scores[next_blue] += similarity
        
        if pattern_scores:
            best_pattern_blue = max(pattern_scores.items(), key=lambda x: x[1])[0]
            predictions.append(best_pattern_blue)
        
        # 模型4：反向选择（避免过热号码）
        recent_freq = Counter(analysis['time_series_blue'][-15:])
        cold_balls = [ball for ball in self.blue_range if recent_freq[ball] <= 1]
        if cold_balls:
            cold_scores = {ball: blue_scores[ball] for ball in cold_balls}
            if cold_scores:
                best_cold = max(cold_scores.items(), key=lambda x: x[1])[0]
                predictions.append(best_cold)
        
        # 投票选择
        if predictions:
            vote_count = Counter(predictions)
            final_blue = vote_count.most_common(1)[0][0]
        else:
            final_blue = sorted_blues[0][0]
        
        return final_blue
    
    def _default_analysis(self):
        """默认分析（当没有数据时）"""
        return {
            'red_frequency': defaultdict(int),
            'blue_frequency': defaultdict(int),
            'red_position_freq': [defaultdict(int) for _ in range(6)],
            'red_intervals': defaultdict(list),
            'blue_intervals': defaultdict(list),
            'red_hot_trend': defaultdict(list),
            'blue_hot_trend': defaultdict(list),
            'red_distribution': {'low': 33, 'mid': 33, 'high': 34},
            'blue_distribution': {'low': 33, 'mid': 33, 'high': 34},
            'time_series_red': [],
            'time_series_blue': []
        }
    
    def predict(self):
        """终极预测方法"""
        print("\n=== 终极集成预测开始 ===")
        
        # 数据分析
        analysis = self.enhanced_data_analysis()
        print("✓ 增强型数据分析完成")
        
        # 红球得分计算
        red_scores = self.calculate_red_scores_ultimate(analysis)
        print("✓ 终极红球得分计算完成")
        
        # 蓝球得分计算
        blue_scores = self.calculate_blue_scores_ultimate(analysis)
        print("✓ 终极蓝球得分计算完成")
        
        # 动态策略选择
        selected_reds = self.dynamic_strategy_selection(red_scores, analysis)
        print(f"✓ 动态策略选择完成，选中红球: {sorted(selected_reds)}")
        
        # 蓝球选择
        selected_blue = self.select_blue_ball_ultimate(blue_scores, analysis)
        print(f"✓ 终极蓝球选择完成，选中蓝球: {selected_blue}")
        
        # 预测结果
        prediction = {
            'red': sorted(selected_reds),
            'blue': selected_blue,
            'confidence': {
                'red_avg_score': np.mean([sum(red_scores[f].get(ball, 0) for f in red_scores) for ball in selected_reds]),
                'blue_score': blue_scores[selected_blue],
                'strategy_confidence': max(s['score'] for s in self.strategy_performance.values())
            },
            'analysis_summary': {
                'total_periods': len(self.data),
                'red_distribution': dict(analysis['red_distribution']),
                'blue_distribution': dict(analysis['blue_distribution']),
                'ensemble_weights': self.ensemble_weights,
                'strategy_weights': {k: v['weight'] for k, v in self.strategy_performance.items()}
            }
        }
        
        print(f"\n预测结果: 红球 {prediction['red']}, 蓝球 {prediction['blue']}")
        print(f"置信度: 红球 {prediction['confidence']['red_avg_score']:.3f}, 蓝球 {prediction['confidence']['blue_score']:.3f}")
        
        return prediction

# 加载数据并进行综合分析
def markov_predict(data):
    """使用马尔科夫链结合频率分析预测下期双色球号码（7红1蓝复式）"""
    historical = []
    red_freq = [0] * 34  # 1-33
    blue_freq = [0] * 17  # 1-16
    for entry in data:
        red_str = entry['number'].split(' ')
        red = sorted([int(x) for x in red_str])
        blue = int(entry['refernumber'])
        historical.append((red, blue))
        for r in red:
            red_freq[r] += 1
        blue_freq[blue] += 1
    historical = historical[::-1]  # 从旧到新

    # 蓝球马尔科夫链
    num_blue = 16
    transition_blue = [[0] * (num_blue + 1) for _ in range(num_blue + 1)]
    for i in range(1, len(historical)):
        prev = historical[i-1][1]
        curr = historical[i][1]
        transition_blue[prev][curr] += 1
    prob_blue = [[0.0] * (num_blue + 1) for _ in range(num_blue + 1)]
    for i in range(1, num_blue + 1):
        total = sum(transition_blue[i])
        if total > 0:
            for j in range(1, num_blue + 1):
                prob_blue[i][j] = (transition_blue[i][j] / total) * (blue_freq[j] / sum(blue_freq[1:]))  # 结合频率
        else:
            for j in range(1, num_blue + 1):
                prob_blue[i][j] = (1.0 / num_blue) * (blue_freq[j] / sum(blue_freq[1:]))
    last_blue = historical[-1][1]
    blue_candidates = list(range(1, num_blue + 1))
    blue_weights = [prob_blue[last_blue][j] for j in blue_candidates]
    if sum(blue_weights) == 0:
        blue_weights = [1.0 / num_blue] * num_blue
    next_blue = random.choices(blue_candidates, weights=blue_weights, k=1)[0]

    # 红球：为每个位置构建马尔科夫链（扩展到7位，对于第7位使用平均概率），结合频率
    num_red = 33
    positions = 7
    transition_red = [[[0] * (num_red + 1) for _ in range(num_red + 1)] for _ in range(positions)]
    for pos in range(6):
        for i in range(1, len(historical)):
            prev = historical[i-1][0][pos]
            curr = historical[i][0][pos]
            transition_red[pos][prev][curr] += 1
    for i in range(1, num_red + 1):
        for j in range(1, num_red + 1):
            avg_count = sum(transition_red[pos][i][j] for pos in range(6)) / 6
            transition_red[6][i][j] = avg_count
    prob_red = [[[0.0] * (num_red + 1) for _ in range(num_red + 1)] for _ in range(positions)]
    for pos in range(positions):
        for i in range(1, num_red + 1):
            total = sum(transition_red[pos][i])
            if total > 0:
                for j in range(1, num_red + 1):
                    prob_red[pos][i][j] = (transition_red[pos][i][j] / total) * (red_freq[j] / sum(red_freq[1:]))
            else:
                for j in range(1, num_red + 1):
                    prob_red[pos][i][j] = (1.0 / num_red) * (red_freq[j] / sum(red_freq[1:]))
    last_red = historical[-1][0] + [historical[-1][0][-1]]
    next_red = []
    used = set()
    for pos in range(positions):
        prev = last_red[pos]
        red_candidates = [j for j in range(1, num_red + 1) if j not in used]
        red_weights = [prob_red[pos][prev][j] for j in red_candidates]
        if sum(red_weights) == 0:
            red_weights = [1.0 / len(red_candidates)] * len(red_candidates)
        selected = random.choices(red_candidates, weights=red_weights, k=1)[0]
        next_red.append(selected)
        used.add(selected)
    next_red.sort()
    return next_red, next_blue

# 双色球预测函数（7红1蓝复式）
def predict_ssq_7red_1blue():
    """使用终极集成算法预测双色球号码（7红1蓝复式）"""
    # 确保数据是最新的
    data = smart_get_ssq_data(1000)
    
    # 创建终极预测器
    predictor = UltimateIntegratedPredictor()
    
    # 进行预测
    prediction = predictor.predict()
    
    return prediction

def test_integrated_advanced_blue_predictor():
    """测试整合后的高级蓝球预测器"""
    print("\n=== 测试整合后的高级蓝球预测器 ===")
    
    try:
        # 确保数据是最新的
        data = smart_get_ssq_data(1000)
        print(f"✓ 数据加载完成，共 {len(data)} 期")
        
        # 创建终极预测器（包含高级蓝球预测器）
        predictor = UltimateIntegratedPredictor()
        print("✓ 终极预测器初始化完成")
        
        # 测试高级蓝球预测器的性能
        print("\n--- 高级蓝球预测器性能测试 ---")
        performance_report = predictor.advanced_blue_predictor.get_performance_report()
        print(performance_report)
        
        # 进行完整预测
        print("\n--- 完整预测测试 ---")
        prediction = predictor.predict()
        
        print("\n=== 预测结果 ===")
        print(f"红球: {prediction['red']}")
        print(f"蓝球: {prediction['blue']}")
        print(f"红球平均得分: {prediction['confidence']['red_avg_score']:.3f}")
        print(f"蓝球得分: {prediction['confidence']['blue_score']:.3f}")
        
        return prediction
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 运行测试
    test_integrated_advanced_blue_predictor()

# 检查数据完整性
def check_data_integrity(data):
    """检查数据完整性，确保数据是最新的"""
    if not data:
        print("⚠️ 数据为空，无法进行完整性检查")
        return False, "数据为空"
    
    # 检查数据格式
    required_fields = ['issueno', 'number', 'refernumber']
    for i, record in enumerate(data[:5]):  # 检查前5条记录
        for field in required_fields:
            if field not in record:
                print(f"❌ 数据格式错误：第{i+1}条记录缺少字段 '{field}'")
                return False, f"数据格式错误：缺少字段 '{field}'"
    
    # 检查期号连续性
    periods = []
    for record in data[:20]:  # 检查前20期
        try:
            period_num = int(record['issueno'])
            periods.append(period_num)
        except ValueError:
            print(f"⚠️ 期号格式异常: {record['issueno']}")
    
    if len(periods) >= 2:
        gaps = []
        for i in range(1, len(periods)):
            gap = periods[i-1] - periods[i]
            if gap > 1:
                gaps.append(gap)
        
        if gaps:
            max_gap = max(gaps)
            if max_gap > 3:
                print(f"⚠️ 检测到数据缺失，最大间隔: {max_gap} 期")
                return False, f"数据缺失，最大间隔: {max_gap} 期"
    
    # 检查数据时效性
    latest_period = data[0]['issueno']
    try:
        api_latest = get_latest_period()
        if api_latest and latest_period != api_latest:
            print(f"⚠️ 数据不是最新的，本地: {latest_period}, 最新: {api_latest}")
            return False, f"数据过时，本地: {latest_period}, 最新: {api_latest}"
    except:
        print("⚠️ 无法验证数据时效性")
    
    print(f"✅ 数据完整性检查通过，最新期号: {latest_period}")
    return True, "数据完整"

def generate_five_predictions_with_same_control():
    """生成5注符合同号概率控制的预测"""
    print("\n🎯 正在生成5注同号概率优化预测...")
    
    # 获取数据
    data = smart_get_ssq_data(1000)
    if not data:
        print("❌ 无法获取数据")
        return []
    
    # 创建预测器（使用文件路径）
    predictor = UltimateIntegratedPredictor('ssq_data.json')
    
    # 生成多个候选组合
    markov_combinations = predictor.improved_markov_prediction(15)  # 生成更多候选
    
    # 分析同号概率
    # 解析最新一期的红球和蓝球
    latest_entry = data[0]
    red_str = latest_entry['number'].strip().split(' ')
    last_red = set([int(x.strip()) for x in red_str if x.strip()])
    last_blue = int(latest_entry['refernumber'])
    
    print(f"\n📊 上期开奖: 红球{sorted(list(last_red))} 蓝球{last_blue}")
    
    # 按同号数量分组
    grouped_by_same = {}
    for combo in markov_combinations:
        same_count = len(set(combo['red']) & last_red)
        if same_count not in grouped_by_same:
            grouped_by_same[same_count] = []
        grouped_by_same[same_count].append(combo)
    
    # 选择5注最优预测
    final_predictions = []
    
    # 优先选择1个同号（概率45.35%）
    if 1 in grouped_by_same:
        final_predictions.extend(grouped_by_same[1][:2])
    
    # 选择0个同号（概率27.13%）
    if 0 in grouped_by_same:
        final_predictions.extend(grouped_by_same[0][:2])
    
    # 选择2个同号（概率22.42%）
    if 2 in grouped_by_same:
        final_predictions.extend(grouped_by_same[2][:1])
    
    # 如果不足5注，从其他组合中补充
    if len(final_predictions) < 5:
        remaining = [combo for combo in markov_combinations if combo not in final_predictions]
        final_predictions.extend(remaining[:5-len(final_predictions)])
    
    # 确保只有5注
    final_predictions = final_predictions[:5]
    
    return final_predictions, last_red, last_blue

# 格式化输出预测结果
def format_prediction_output(prediction):
    """格式化输出预测结果"""
    print("\n" + "="*60)
    print("🎯 终极集成双色球预测系统 🎯")
    print("="*60)
    
    # 预测号码
    red_balls = ' '.join([f"{num:02d}" for num in prediction['red']])
    blue_ball = f"{prediction['blue']:02d}"
    
    print(f"\n🔴 红球预测: {red_balls}")
    print(f"🔵 蓝球预测: {blue_ball}")
    print(f"\n📊 预测组合: {red_balls} + {blue_ball}")
    
    # 生成指定格式输出
    red_str = ''.join([f"{num:02d}" for num in prediction['red']])
    blue_str = f"{prediction['blue']:02d}"
    formatted_output = f"00CP#01#{red_str}*{blue_str}#1"
    print(f"\n🎯 格式化输出: {formatted_output}")
    
    # 置信度信息
    if 'confidence' in prediction:
        conf = prediction['confidence']
        print(f"\n📈 置信度分析:")
        print(f"   红球平均得分: {conf.get('red_avg_score', 0):.3f}")
        print(f"   蓝球得分: {conf.get('blue_score', 0):.3f}")
        print(f"   策略置信度: {conf.get('strategy_confidence', 0):.3f}")
    
    # 分析摘要
    if 'analysis' in prediction:
        analysis = prediction['analysis']
        print(f"\n📋 分析摘要:")
        print(f"   历史数据期数: {analysis.get('total_periods', 0)}")
        
        if 'red_distribution' in analysis:
            red_dist = analysis['red_distribution']
            print(f"   红球分布 - 低区: {red_dist.get('low', 0)}, 中区: {red_dist.get('mid', 0)}, 高区: {red_dist.get('high', 0)}")
        
        if 'ensemble_weights' in analysis:
            print(f"   集成权重: {analysis['ensemble_weights']}")
    
    print("\n" + "="*60)
    print("💡 本预测基于终极集成算法，融合了多种先进技术")
    print("🔬 包含: 频率分析、神经网络、量子启发、动态策略等")
    print("⚠️  仅供参考，请理性购彩")
    print("="*60)
    
    return formatted_output

def format_five_predictions_output(predictions, last_red, last_blue):
    """格式化输出5注预测结果"""
    print("\n" + "="*80)
    print("🎯 同号概率优化 - 5注精选预测 🎯")
    print("="*80)
    
    print(f"\n📊 上期开奖: 红球{sorted(list(last_red))} 蓝球{last_blue}")
    print(f"\n🎲 基于同号概率分析生成的5注预测:")
    print("\n" + "-"*80)
    
    formatted_outputs = []
    
    for i, pred in enumerate(predictions, 1):
        red_balls = ' '.join([f"{num:02d}" for num in pred['red']])
        blue_ball = f"{pred['blue']:02d}"
        
        # 计算同号情况
        same_count = len(set(pred['red']) & last_red)
        same_numbers = sorted(list(set(pred['red']) & last_red))
        
        # 同号概率
        same_prob = {
            0: "27.13%", 1: "45.35%", 2: "22.42%", 
            3: "4.80%", 4: "0.30%", 5: "0.00%", 6: "0.00%"
        }.get(same_count, "<0.01%")
        
        print(f"\n第{i}注: {red_balls} + {blue_ball}")
        print(f"      同号: {same_count}个 {same_numbers if same_numbers else '无'} (概率:{same_prob})")
        print(f"      得分: {pred['score']:.3f}")
        
        # 生成格式化输出
        red_str = ''.join([f"{num:02d}" for num in pred['red']])
        blue_str = f"{pred['blue']:02d}"
        formatted_output = f"00CP#01#{red_str}*{blue_str}#1"
        formatted_outputs.append(formatted_output)
        print(f"      格式: {formatted_output}")
    
    print("\n" + "-"*80)
    print("\n📈 概率分析说明:")
    print("   • 1个同号: 45.35% (最常见)")
    print("   • 0个同号: 27.13% (次常见)")
    print("   • 2个同号: 22.42% (较常见)")
    print("   • 3个同号: 4.80%  (较少见)")
    print("   • 4个以上: <1%    (极少见)")
    
    print("\n🔬 算法特色:")
    print("   • 马尔可夫链权重分析")
    print("   • 同号概率智能控制")
    print("   • 历史数据深度挖掘")
    print("   • 多维度综合评分")
    
    print("\n⚠️  仅供参考，请理性购彩")
    print("="*80)
    
    return formatted_outputs

# 主程序入口
def main():
    """主程序入口"""
    print("🎲 双色球终极预测系统启动中...")
    
    try:
        # 获取最新数据
        print("\n📡 正在获取最新双色球数据...")
        data = smart_get_ssq_data(1000)
        
        if data:
            print(f"✅ 数据获取成功，共 {len(data)} 期历史数据")
            
            # 数据完整性检查
            print("\n🔍 正在进行数据完整性检查...")
            is_valid, message = check_data_integrity(data)
            
            # 生成5注同号概率优化预测
            print("\n🎯 开始生成同号概率优化预测...")
            predictions, last_red, last_blue = generate_five_predictions_with_same_control()
            
            if predictions:
                # 格式化输出5注预测
                formatted_outputs = format_five_predictions_output(predictions, last_red, last_blue)
                
                # 保存预测记录
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                prediction_record = {
                    'timestamp': timestamp,
                    'predictions': [{
                        'red': [int(x) for x in pred['red']],
                        'blue': int(pred['blue']),
                        'score': float(pred['score']),
                        'same_count': int(pred.get('same_count', 0))
                    } for pred in predictions],
                    'last_red': [int(x) for x in sorted(list(last_red))],
                    'last_blue': int(last_blue),
                    'formatted_outputs': formatted_outputs,
                    'algorithm': 'SameNumberOptimizedPredictor'
                }
                
                # 保存到文件
                try:
                    with open('prediction_history.json', 'a', encoding='utf-8') as f:
                        f.write(json.dumps(prediction_record, ensure_ascii=False) + '\n')
                    print(f"\n💾 预测记录已保存 ({timestamp})")
                except Exception as e:
                    print(f"\n❌ 保存预测记录失败: {e}")
                
                print("\n✅ 5注同号概率优化预测生成完成！")
                return formatted_outputs
            else:
                print("❌ 预测生成失败")
                return []
            
            if not is_valid:
                print(f"⚠️ 数据完整性检查失败: {message}")
                print("🔄 尝试重新获取数据...")
                data = smart_get_ssq_data(1000)
                if data:
                    is_valid, message = check_data_integrity(data)
                    if not is_valid:
                        print(f"⚠️ 重新获取后仍有问题: {message}")
                        print("⚠️ 将继续使用现有数据进行预测")
        else:
            print("⚠️ 数据获取失败，将使用随机预测")
        
        # 进行预测
        print("\n🤖 启动终极集成预测算法...")
        prediction = predict_ssq_7red_1blue()
        
        # 输出结果
        formatted_result = format_prediction_output(prediction)
        
        # 保存预测记录
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prediction_record = {
            'timestamp': timestamp,
            'prediction': prediction,
            'formatted_output': formatted_result,
            'algorithm': 'UltimateIntegratedPredictor',
            'data_integrity': is_valid if 'is_valid' in locals() else True
        }
        
        # 保存到文件
        try:
            with open('prediction_history.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(prediction_record, ensure_ascii=False) + '\n')
            print(f"\n💾 预测记录已保存 ({timestamp})")
        except Exception as e:
            print(f"\n❌ 保存预测记录失败: {e}")
        
        return prediction, formatted_result
        
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("\n🔄 使用备用随机预测...")
        
        backup_prediction = {
            'red': sorted(random.sample(range(1, 34), 7)),
            'blue': random.randint(1, 16),
            'confidence': {'red_avg_score': 0.5, 'blue_score': 0.5, 'strategy_confidence': 0.5},
            'analysis': {}
        }
        
        formatted_result = format_prediction_output(backup_prediction)
        return backup_prediction, formatted_result

if __name__ == '__main__':
    try:
        print("=== 双色球终极集成预测系统 ===")
        print(f"数据文件: {DATA_FILE}")
        print("="*50)
        
        # 直接运行主程序
        main()
        
    except Exception as e:
        print(f'错误: {str(e)}')
        print("请检查网络连接和API密钥是否正确")