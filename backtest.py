import json
import random
from collections import defaultdict
import numpy as np
from main import markov_predict

def load_data():
    """加载历史数据"""
    with open('ssq_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def parse_actual_numbers(entry):
    """解析实际开奖号码"""
    red_str = entry['number'].split(' ')
    red = sorted([int(x) for x in red_str])
    blue = int(entry['refernumber'])
    return red, blue

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def backtest_algorithm(data, test_periods=100):
    """回测算法性能"""
    print(f"开始回测，测试最近{test_periods}期数据...")
    
    results = {
        'total_tests': 0,
        'red_hits': defaultdict(int),  # 红球命中数量统计
        'blue_hits': 0,  # 蓝球命中次数
        'predictions': [],  # 详细预测记录
        'hit_patterns': []  # 命中模式
    }
    
    # 从第test_periods期开始，用前面的数据预测后面的结果
    for i in range(test_periods, min(len(data), test_periods + 50)):
        # 使用前i期数据进行预测
        training_data = data[i:]
        
        # 获取实际结果（第i期）
        actual_red, actual_blue = parse_actual_numbers(data[i-1])
        
        try:
            # 进行预测
            predicted_red, predicted_blue = markov_predict(training_data)
            
            # 计算命中率
            red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
            
            # 记录结果
            results['total_tests'] += 1
            results['red_hits'][red_hits] += 1
            results['blue_hits'] += blue_hit
            
            prediction_record = {
                'period': data[i-1]['issueno'],
                'predicted_red': predicted_red,
                'predicted_blue': predicted_blue,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'red_hits': red_hits,
                'blue_hit': blue_hit
            }
            results['predictions'].append(prediction_record)
            results['hit_patterns'].append(red_hits)
            
            print(f"期号{data[i-1]['issueno']}: 红球命中{red_hits}/7, 蓝球{'命中' if blue_hit else '未中'}")
            
        except Exception as e:
            print(f"预测期号{data[i-1]['issueno']}时出错: {e}")
            continue
    
    return results

def analyze_results(results):
    """分析回测结果"""
    print("\n=== 回测结果分析 ===")
    print(f"总测试期数: {results['total_tests']}")
    
    # 红球命中率分析
    print("\n红球命中分布:")
    for hits in range(8):
        count = results['red_hits'][hits]
        percentage = (count / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
        print(f"  命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    # 蓝球命中率
    blue_rate = (results['blue_hits'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"\n蓝球命中率: {results['blue_hits']}/{results['total_tests']} ({blue_rate:.1f}%)")
    
    # 平均红球命中数
    total_red_hits = sum(hits * count for hits, count in results['red_hits'].items())
    avg_red_hits = total_red_hits / results['total_tests'] if results['total_tests'] > 0 else 0
    print(f"平均红球命中数: {avg_red_hits:.2f}/7")
    
    # 最佳预测记录
    if results['predictions']:
        best_predictions = sorted(results['predictions'], key=lambda x: x['red_hits'], reverse=True)[:5]
        print("\n最佳预测记录:")
        for pred in best_predictions:
            print(f"  期号{pred['period']}: 红球{pred['red_hits']}/7, 蓝球{'✓' if pred['blue_hit'] else '✗'}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    return results

def frequency_analysis(data):
    """频率分析，寻找改进点"""
    print("\n=== 频率分析 ===")
    
    # 红球频率统计
    red_freq = defaultdict(int)
    blue_freq = defaultdict(int)
    
    for entry in data:
        red_str = entry['number'].split(' ')
        red = [int(x) for x in red_str]
        blue = int(entry['refernumber'])
        
        for r in red:
            red_freq[r] += 1
        blue_freq[blue] += 1
    
    # 找出热号和冷号
    sorted_red = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)
    sorted_blue = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)
    
    print("红球热号 (前10):")
    for num, freq in sorted_red[:10]:
        print(f"  {num:2d}: {freq}次")
    
    print("\n红球冷号 (后10):")
    for num, freq in sorted_red[-10:]:
        print(f"  {num:2d}: {freq}次")
    
    print("\n蓝球频率 (前8):")
    for num, freq in sorted_blue[:8]:
        print(f"  {num:2d}: {freq}次")
    
    return red_freq, blue_freq

def improved_markov_predict(data, red_freq, blue_freq, alpha=0.7):
    """改进的马尔科夫预测算法"""
    historical = []
    for entry in data:
        red_str = entry['number'].split(' ')
        red = sorted([int(x) for x in red_str])
        blue = int(entry['refernumber'])
        historical.append((red, blue))
    historical = historical[::-1]  # 从旧到新

    # 改进的蓝球预测：增加频率权重
    num_blue = 16
    transition_blue = [[0] * (num_blue + 1) for _ in range(num_blue + 1)]
    for i in range(1, len(historical)):
        prev = historical[i-1][1]
        curr = historical[i][1]
        transition_blue[prev][curr] += 1
    
    prob_blue = [[0.0] * (num_blue + 1) for _ in range(num_blue + 1)]
    for i in range(1, num_blue + 1):
        total = sum(transition_blue[i])
        if total > 0:
            for j in range(1, num_blue + 1):
                # 增加频率权重的影响
                freq_weight = blue_freq[j] / sum(blue_freq.values())
                markov_weight = transition_blue[i][j] / total
                prob_blue[i][j] = alpha * markov_weight + (1 - alpha) * freq_weight
        else:
            for j in range(1, num_blue + 1):
                prob_blue[i][j] = blue_freq[j] / sum(blue_freq.values())
    
    last_blue = historical[-1][1]
    blue_candidates = list(range(1, num_blue + 1))
    blue_weights = [prob_blue[last_blue][j] for j in blue_candidates]
    if sum(blue_weights) == 0:
        blue_weights = [1.0 / num_blue] * num_blue
    next_blue = random.choices(blue_candidates, weights=blue_weights, k=1)[0]

    # 改进的红球预测：考虑热号冷号平衡
    num_red = 33
    positions = 7
    transition_red = [[[0] * (num_red + 1) for _ in range(num_red + 1)] for _ in range(positions)]
    
    for pos in range(6):
        for i in range(1, len(historical)):
            prev = historical[i-1][0][pos]
            curr = historical[i][0][pos]
            transition_red[pos][prev][curr] += 1
    
    # 第7位使用平均概率
    for i in range(1, num_red + 1):
        for j in range(1, num_red + 1):
            avg_count = sum(transition_red[pos][i][j] for pos in range(6)) / 6
            transition_red[6][i][j] = avg_count
    
    prob_red = [[[0.0] * (num_red + 1) for _ in range(num_red + 1)] for _ in range(positions)]
    for pos in range(positions):
        for i in range(1, num_red + 1):
            total = sum(transition_red[pos][i])
            if total > 0:
                for j in range(1, num_red + 1):
                    freq_weight = red_freq[j] / sum(red_freq.values())
                    markov_weight = transition_red[pos][i][j] / total
                    prob_red[pos][i][j] = alpha * markov_weight + (1 - alpha) * freq_weight
            else:
                for j in range(1, num_red + 1):
                    prob_red[pos][i][j] = red_freq[j] / sum(red_freq.values())
    
    last_red = historical[-1][0] + [historical[-1][0][-1]]
    next_red = []
    used = set()
    
    for pos in range(positions):
        prev = last_red[pos]
        red_candidates = [j for j in range(1, num_red + 1) if j not in used]
        red_weights = [prob_red[pos][prev][j] for j in red_candidates]
        if sum(red_weights) == 0:
            red_weights = [1.0 / len(red_candidates)] * len(red_candidates)
        selected = random.choices(red_candidates, weights=red_weights, k=1)[0]
        next_red.append(selected)
        used.add(selected)
    
    next_red.sort()
    return next_red, next_blue

def compare_algorithms(data, red_freq, blue_freq):
    """比较原算法和改进算法"""
    print("\n=== 算法对比测试 ===")
    
    # 测试最近10期
    test_periods = min(10, len(data) - 100)
    
    original_results = {'red_hits': [], 'blue_hits': 0}
    improved_results = {'red_hits': [], 'blue_hits': 0}
    
    for i in range(test_periods):
        training_data = data[i+10:]
        actual_red, actual_blue = parse_actual_numbers(data[i])
        
        try:
            # 原算法预测
            pred_red_orig, pred_blue_orig = markov_predict(training_data)
            red_hits_orig, blue_hit_orig = calculate_hit_rate(pred_red_orig, pred_blue_orig, actual_red, actual_blue)
            original_results['red_hits'].append(red_hits_orig)
            original_results['blue_hits'] += blue_hit_orig
            
            # 改进算法预测
            pred_red_impr, pred_blue_impr = improved_markov_predict(training_data, red_freq, blue_freq)
            red_hits_impr, blue_hit_impr = calculate_hit_rate(pred_red_impr, pred_blue_impr, actual_red, actual_blue)
            improved_results['red_hits'].append(red_hits_impr)
            improved_results['blue_hits'] += blue_hit_impr
            
            print(f"期号{data[i]['issueno']}:")
            print(f"  原算法: 红球{red_hits_orig}/7, 蓝球{'✓' if blue_hit_orig else '✗'}")
            print(f"  改进版: 红球{red_hits_impr}/7, 蓝球{'✓' if blue_hit_impr else '✗'}")
            
        except Exception as e:
            print(f"测试期号{data[i]['issueno']}时出错: {e}")
    
    # 统计对比结果
    orig_avg = np.mean(original_results['red_hits']) if original_results['red_hits'] else 0
    impr_avg = np.mean(improved_results['red_hits']) if improved_results['red_hits'] else 0
    
    print(f"\n对比结果 (测试{test_periods}期):")
    print(f"原算法平均红球命中: {orig_avg:.2f}/7")
    print(f"改进算法平均红球命中: {impr_avg:.2f}/7")
    print(f"原算法蓝球命中率: {original_results['blue_hits']}/{test_periods}")
    print(f"改进算法蓝球命中率: {improved_results['blue_hits']}/{test_periods}")

if __name__ == '__main__':
    # 设置随机种子以便复现结果
    random.seed(42)
    
    # 加载数据
    data = load_data()
    print(f"加载了{len(data)}期历史数据")
    
    # 进行回测
    results = backtest_algorithm(data)
    analyze_results(results)
    
    # 频率分析
    red_freq, blue_freq = frequency_analysis(data)
    
    # 算法对比
    compare_algorithms(data, red_freq, blue_freq)
    
    print("\n回测完成！基于结果可以进一步优化算法参数。")