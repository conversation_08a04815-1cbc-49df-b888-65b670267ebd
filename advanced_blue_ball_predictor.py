#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级蓝球预测器
结合深度学习、时序分析和高级统计方法的蓝球预测系统
目标：蓝球命中率≥12%

核心技术：
1. 深度时序神经网络
2. 多维特征工程
3. 集成学习策略
4. 动态权重调整
5. 概率分布建模
6. 反向验证机制
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class AdvancedBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化高级蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 神经网络参数
        self.input_dim = 32
        self.hidden_dim = 64
        self.output_dim = 16
        
        # 初始化神经网络权重
        self.init_neural_networks()
        
        # 特征权重（动态调整）
        self.feature_weights = {
            'frequency_analysis': 0.18,
            'interval_pattern': 0.16,
            'sequence_learning': 0.15,
            'distribution_modeling': 0.14,
            'trend_analysis': 0.12,
            'neural_prediction': 0.10,
            'ensemble_voting': 0.08,
            'reverse_validation': 0.07
        }
        
        # 预测历史和性能统计
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'recent_performance': []
        }
        
        # 学习率和适应性参数
        self.learning_rate = 0.01
        self.adaptation_factor = 0.95
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def init_neural_networks(self):
        """初始化神经网络"""
        # 时序神经网络权重
        self.lstm_weights = {
            'input_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'forget_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'output_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'cell_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1
        }
        
        # 前馈神经网络权重
        self.ffnn_weights = {
            'hidden1': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'hidden2': np.random.randn(self.hidden_dim, self.hidden_dim) * 0.1,
            'output': np.random.randn(self.hidden_dim, self.output_dim) * 0.1
        }
        
        # 注意力机制权重
        self.attention_weights = np.random.randn(self.hidden_dim, self.hidden_dim) * 0.1
    
    def extract_advanced_features(self, sequence_length=20):
        """提取高级特征"""
        if not self.data or len(self.data) < sequence_length:
            return np.zeros(self.input_dim)
        
        blue_sequence = [entry['blue'] for entry in self.data[-sequence_length:]]
        features = []
        
        # 1. 频率特征 (16维)
        frequency = Counter(blue_sequence)
        freq_features = [frequency[i] / sequence_length for i in range(1, 17)]
        features.extend(freq_features)
        
        # 2. 间隔特征 (8维)
        intervals = []
        for i in range(1, len(blue_sequence)):
            intervals.append(blue_sequence[i] - blue_sequence[i-1])
        
        if intervals:
            interval_features = [
                np.mean(intervals),
                np.std(intervals) if len(intervals) > 1 else 0,
                np.max(intervals),
                np.min(intervals),
                len([x for x in intervals if x > 0]) / len(intervals),
                len([x for x in intervals if x < 0]) / len(intervals),
                len([x for x in intervals if x == 0]) / len(intervals),
                np.median(intervals)
            ]
        else:
            interval_features = [0] * 8
        
        features.extend(interval_features)
        
        # 3. 趋势特征 (4维)
        recent_5 = blue_sequence[-5:] if len(blue_sequence) >= 5 else blue_sequence
        trend_features = [
            np.mean(recent_5),
            np.std(recent_5) if len(recent_5) > 1 else 0,
            recent_5[-1] if recent_5 else 0,
            (recent_5[-1] - recent_5[0]) / len(recent_5) if len(recent_5) > 1 else 0
        ]
        features.extend(trend_features)
        
        # 4. 分布特征 (4维)
        distribution_features = [
            len(set(blue_sequence)) / 16,  # 唯一性
            np.var(blue_sequence),  # 方差
            (max(blue_sequence) - min(blue_sequence)) / 15,  # 范围
            sum(1 for i in range(1, len(blue_sequence)) if blue_sequence[i] == blue_sequence[i-1]) / max(1, len(blue_sequence)-1)  # 连续性
        ]
        features.extend(distribution_features)
        
        # 确保特征维度正确
        features = features[:self.input_dim]
        while len(features) < self.input_dim:
            features.append(0.0)
        
        return np.array(features)
    
    def lstm_forward(self, features):
        """LSTM前向传播"""
        # 简化的LSTM实现
        h = np.zeros(self.hidden_dim)
        c = np.zeros(self.hidden_dim)
        
        # 输入门
        i_gate = self.sigmoid(np.dot(features, self.lstm_weights['input_gate']))
        
        # 遗忘门
        f_gate = self.sigmoid(np.dot(features, self.lstm_weights['forget_gate']))
        
        # 输出门
        o_gate = self.sigmoid(np.dot(features, self.lstm_weights['output_gate']))
        
        # 候选值
        c_candidate = np.tanh(np.dot(features, self.lstm_weights['cell_gate']))
        
        # 更新细胞状态
        c = f_gate * c + i_gate * c_candidate
        
        # 更新隐藏状态
        h = o_gate * np.tanh(c)
        
        return h
    
    def ffnn_forward(self, features):
        """前馈神经网络前向传播"""
        # 第一隐藏层
        h1 = self.relu(np.dot(features, self.ffnn_weights['hidden1']))
        
        # 第二隐藏层
        h2 = self.relu(np.dot(h1, self.ffnn_weights['hidden2']))
        
        # 输出层
        output = self.softmax(np.dot(h2, self.ffnn_weights['output']))
        
        return output
    
    def attention_mechanism(self, query, keys, values):
        """注意力机制"""
        # 确保query是一维向量
        if query.ndim > 1:
            query = query.flatten()
        
        # 计算注意力分数 - 简化版本
        scores = []
        for i in range(keys.shape[0]):
            key = keys[i]
            if key.ndim > 1:
                key = key.flatten()
            
            # 计算相似度得分
            if len(query) == len(key):
                score = np.dot(query, key) / (np.linalg.norm(query) * np.linalg.norm(key) + 1e-8)
            else:
                # 如果维度不匹配，使用较小维度
                min_dim = min(len(query), len(key))
                score = np.dot(query[:min_dim], key[:min_dim]) / (np.linalg.norm(query[:min_dim]) * np.linalg.norm(key[:min_dim]) + 1e-8)
            
            scores.append(score)
        
        scores = np.array(scores)
        
        # 应用softmax
        attention_weights = self.softmax(scores)
        
        # 计算加权值
        attended_values = np.zeros_like(query)
        for i in range(values.shape[0]):
            value = values[i]
            if value.ndim > 1:
                value = value.flatten()
            
            # 确保维度匹配
            min_dim = min(len(attended_values), len(value))
            attended_values[:min_dim] += attention_weights[i] * value[:min_dim]
        
        return attended_values
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(x)
    
    def softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def frequency_analysis_advanced(self, window_sizes=[10, 20, 50]):
        """高级频率分析"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency_scores = {ball: 0.0 for ball in self.blue_range}
        
        for window_size in window_sizes:
            if len(blue_sequence) >= window_size:
                recent_blues = blue_sequence[-window_size:]
                frequency = Counter(recent_blues)
                
                # 计算期望频率
                expected_freq = window_size / 16
                
                for ball in self.blue_range:
                    actual_freq = frequency[ball]
                    
                    # 频率偏差得分（偏差越大，未来出现概率越高）
                    deviation = expected_freq - actual_freq
                    score = self.sigmoid(deviation)
                    
                    frequency_scores[ball] += score / len(window_sizes)
        
        return frequency_scores
    
    def interval_pattern_analysis(self):
        """间隔模式分析"""
        if not self.data or len(self.data) < 10:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        interval_scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            # 找到该球的所有出现位置
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 2:
                # 计算间隔
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                if intervals:
                    avg_interval = np.mean(intervals)
                    std_interval = np.std(intervals) if len(intervals) > 1 else 1
                    
                    # 当前间隔
                    current_interval = len(blue_sequence) - positions[-1]
                    
                    # 基于正态分布的概率
                    if std_interval > 0:
                        prob = np.exp(-0.5 * ((current_interval - avg_interval) / std_interval) ** 2)
                        interval_scores[ball] = prob
                    else:
                        interval_scores[ball] = 0.5
                else:
                    interval_scores[ball] = 0.5
            else:
                interval_scores[ball] = 0.8  # 很少出现的球给高分
        
        return interval_scores
    
    def sequence_learning(self, sequence_length=5):
        """序列学习"""
        if not self.data or len(self.data) < sequence_length + 1:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        sequence_scores = {ball: 0.0 for ball in self.blue_range}
        
        # 获取当前序列
        current_sequence = tuple(blue_sequence[-sequence_length:])
        
        # 在历史中寻找相似序列
        matches = []
        for i in range(len(blue_sequence) - sequence_length):
            historical_sequence = tuple(blue_sequence[i:i+sequence_length])
            
            # 计算序列相似度
            similarity = sum(1 for a, b in zip(current_sequence, historical_sequence) if a == b)
            
            if similarity >= sequence_length - 1:  # 允许一个不同
                next_ball = blue_sequence[i + sequence_length]
                matches.append(next_ball)
        
        # 计算得分
        if matches:
            match_count = Counter(matches)
            total_matches = len(matches)
            
            for ball in self.blue_range:
                sequence_scores[ball] = match_count[ball] / total_matches
        else:
            # 如果没有匹配，使用均匀分布
            for ball in self.blue_range:
                sequence_scores[ball] = 1.0 / 16
        
        return sequence_scores
    
    def distribution_modeling(self):
        """分布建模"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        
        # 计算各种分布特征
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        
        distribution_scores = {}
        
        for ball in self.blue_range:
            actual_freq = frequency[ball]
            expected_freq = total_count / 16
            
            # 基于泊松分布的概率
            poisson_prob = self.poisson_pmf(actual_freq, expected_freq)
            
            # 基于均匀分布的偏差
            uniform_deviation = abs(actual_freq - expected_freq) / expected_freq
            uniform_score = 1.0 / (1.0 + uniform_deviation)
            
            # 综合得分
            distribution_scores[ball] = 0.6 * poisson_prob + 0.4 * uniform_score
        
        return distribution_scores
    
    def poisson_pmf(self, k, lam):
        """泊松分布概率质量函数"""
        if lam <= 0:
            return 0
        return (lam ** k) * np.exp(-lam) / math.factorial(int(k))
    
    def trend_analysis_advanced(self, trend_periods=[5, 10, 15]):
        """高级趋势分析"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        trend_scores = {ball: 0.0 for ball in self.blue_range}
        
        for period in trend_periods:
            if len(blue_sequence) >= period:
                recent_blues = blue_sequence[-period:]
                
                for ball in self.blue_range:
                    # 计算趋势强度
                    positions = [i for i, blue in enumerate(recent_blues) if blue == ball]
                    
                    if positions:
                        # 最近出现的位置权重
                        position_weights = [pos / (period - 1) for pos in positions]
                        trend_strength = np.mean(position_weights)
                        
                        # 出现频率
                        frequency = len(positions) / period
                        
                        # 综合趋势得分
                        trend_score = 0.6 * trend_strength + 0.4 * (1 - frequency)
                        trend_scores[ball] += trend_score / len(trend_periods)
                    else:
                        # 未出现的球给予较高分数
                        trend_scores[ball] += 0.8 / len(trend_periods)
        
        return trend_scores
    
    def neural_prediction(self):
        """神经网络预测"""
        features = self.extract_advanced_features()
        
        # LSTM预测
        lstm_output = self.lstm_forward(features)
        
        # 前馈网络预测
        ffnn_output = self.ffnn_forward(features)
        
        # 确保维度一致性
        if len(lstm_output) != self.hidden_dim:
            lstm_output = np.resize(lstm_output, self.hidden_dim)
        
        if len(features) < self.hidden_dim:
            padded_features = np.pad(features, (0, self.hidden_dim - len(features)), 'constant')
        else:
            padded_features = features[:self.hidden_dim]
        
        # 注意力机制融合
        keys = np.vstack([lstm_output.reshape(1, -1), padded_features.reshape(1, -1)])
        values = np.vstack([lstm_output.reshape(1, -1), padded_features.reshape(1, -1)])
        attended_output = self.attention_mechanism(lstm_output, keys, values)
        
        # 将注意力输出转换为球号概率
        if len(attended_output) >= 16:
            neural_probs = self.softmax(attended_output[:16])
        else:
            # 如果维度不够，使用前馈网络输出
            neural_probs = ffnn_output
        
        # 转换为字典格式
        neural_scores = {}
        for i, ball in enumerate(self.blue_range):
            neural_scores[ball] = neural_probs[i]
        
        return neural_scores
    
    def ensemble_voting(self, predictions_list):
        """集成投票"""
        ensemble_scores = {ball: 0.0 for ball in self.blue_range}
        
        for predictions in predictions_list:
            for ball in self.blue_range:
                ensemble_scores[ball] += predictions.get(ball, 0)
        
        # 归一化
        total_score = sum(ensemble_scores.values())
        if total_score > 0:
            for ball in self.blue_range:
                ensemble_scores[ball] /= total_score
        
        return ensemble_scores
    
    def reverse_validation(self):
        """反向验证"""
        if not self.data or len(self.data) < 20:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        # 使用前80%的数据进行预测，验证后20%的准确性
        split_point = int(len(self.data) * 0.8)
        train_data = self.data[:split_point]
        test_data = self.data[split_point:]
        
        # 临时切换数据
        original_data = self.data
        self.data = train_data
        
        # 进行预测并验证
        validation_scores = {ball: 0.0 for ball in self.blue_range}
        
        for i, test_entry in enumerate(test_data[:10]):  # 只验证前10期
            # 预测
            freq_scores = self.frequency_analysis_advanced()
            interval_scores = self.interval_pattern_analysis()
            
            # 计算综合得分
            combined_scores = {}
            for ball in self.blue_range:
                combined_scores[ball] = 0.5 * freq_scores[ball] + 0.5 * interval_scores[ball]
            
            # 检查预测准确性
            actual_ball = test_entry['blue']
            predicted_ball = max(combined_scores.items(), key=lambda x: x[1])[0]
            
            if predicted_ball == actual_ball:
                validation_scores[actual_ball] += 1.0
            
            # 更新训练数据
            self.data.append(test_entry)
        
        # 恢复原始数据
        self.data = original_data
        
        # 归一化验证得分
        total_validation = sum(validation_scores.values())
        if total_validation > 0:
            for ball in self.blue_range:
                validation_scores[ball] /= total_validation
        else:
            for ball in self.blue_range:
                validation_scores[ball] = 1.0 / 16
        
        return validation_scores
    
    def dynamic_weight_adjustment(self):
        """动态权重调整"""
        if len(self.performance_stats['recent_performance']) < 10:
            return
        
        recent_performance = self.performance_stats['recent_performance'][-10:]
        avg_performance = np.mean(recent_performance)
        
        # 如果性能下降，调整权重
        if avg_performance < 0.08:  # 低于8%
            # 增加神经网络和集成学习的权重
            self.feature_weights['neural_prediction'] *= 1.1
            self.feature_weights['ensemble_voting'] *= 1.1
            
            # 减少传统方法的权重
            self.feature_weights['frequency_analysis'] *= 0.9
            self.feature_weights['interval_pattern'] *= 0.9
        
        # 归一化权重
        total_weight = sum(self.feature_weights.values())
        for key in self.feature_weights:
            self.feature_weights[key] /= total_weight
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各种预测得分
        freq_scores = self.frequency_analysis_advanced()
        interval_scores = self.interval_pattern_analysis()
        sequence_scores = self.sequence_learning()
        distribution_scores = self.distribution_modeling()
        trend_scores = self.trend_analysis_advanced()
        neural_scores = self.neural_prediction()
        reverse_scores = self.reverse_validation()
        
        # 集成投票
        all_predictions = [
            freq_scores, interval_scores, sequence_scores,
            distribution_scores, trend_scores, neural_scores, reverse_scores
        ]
        ensemble_scores = self.ensemble_voting(all_predictions)
        
        # 计算最终得分
        final_scores = {}
        for ball in self.blue_range:
            final_score = (
                freq_scores[ball] * self.feature_weights['frequency_analysis'] +
                interval_scores[ball] * self.feature_weights['interval_pattern'] +
                sequence_scores[ball] * self.feature_weights['sequence_learning'] +
                distribution_scores[ball] * self.feature_weights['distribution_modeling'] +
                trend_scores[ball] * self.feature_weights['trend_analysis'] +
                neural_scores[ball] * self.feature_weights['neural_prediction'] +
                ensemble_scores[ball] * self.feature_weights['ensemble_voting'] +
                reverse_scores[ball] * self.feature_weights['reverse_validation']
            )
            final_scores[ball] = final_score
        
        # 多策略选择
        predictions = []
        
        # 策略1：最高分
        best_ball = max(final_scores.items(), key=lambda x: x[1])[0]
        predictions.append(best_ball)
        
        # 策略2：概率采样
        total_score = sum(final_scores.values())
        if total_score > 0:
            probs = [final_scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 策略3：神经网络优先
        neural_best = max(neural_scores.items(), key=lambda x: x[1])[0]
        predictions.append(neural_best)
        
        # 策略4：集成优先
        ensemble_best = max(ensemble_scores.items(), key=lambda x: x[1])[0]
        predictions.append(ensemble_best)
        
        # 投票决策
        vote_count = Counter(predictions)
        final_prediction = vote_count.most_common(1)[0][0]
        
        # 记录预测
        self.prediction_history.append({
            'prediction': final_prediction,
            'final_scores': final_scores,
            'component_scores': {
                'frequency': freq_scores[final_prediction],
                'interval': interval_scores[final_prediction],
                'sequence': sequence_scores[final_prediction],
                'distribution': distribution_scores[final_prediction],
                'trend': trend_scores[final_prediction],
                'neural': neural_scores[final_prediction],
                'ensemble': ensemble_scores[final_prediction],
                'reverse': reverse_scores[final_prediction]
            },
            'timestamp': datetime.now().isoformat()
        })
        
        return final_prediction
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 记录最近性能
        self.performance_stats['recent_performance'].append(1.0 if is_correct else 0.0)
        if len(self.performance_stats['recent_performance']) > 50:
            self.performance_stats['recent_performance'].pop(0)
        
        # 动态调整权重
        self.dynamic_weight_adjustment()
    
    def get_performance_report(self):
        """获取性能报告"""
        recent_hit_rate = 0.0
        if self.performance_stats['recent_performance']:
            recent_hit_rate = np.mean(self.performance_stats['recent_performance'])
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'overall_hit_rate': self.performance_stats['hit_rate'],
            'recent_hit_rate': recent_hit_rate,
            'feature_weights': self.feature_weights.copy()
        }

def test_advanced_blue_predictor():
    """测试高级蓝球预测器"""
    print("=== 高级蓝球预测器测试 ===")
    
    predictor = AdvancedBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(120, len(original_data) - 20)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 10 or i % 30 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    for key, value in report.items():
        if key == 'feature_weights':
            print(f"特征权重配置:")
            for weight_key, weight_value in value.items():
                print(f"  {weight_key}: {weight_value:.3f}")
        else:
            if isinstance(value, float):
                print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"各组件得分:")
        for component, score in last_prediction['component_scores'].items():
            print(f"  {component}: {score:.4f}")
    
    return predictor

if __name__ == "__main__":
    test_advanced_blue_predictor()