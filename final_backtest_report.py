import json
import random
import numpy as np
from collections import defaultdict
from main import markov_predict
from final_optimized_algorithm import ultimate_predict

def load_data():
    """加载历史数据"""
    with open('ssq_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def parse_actual_numbers(entry):
    """解析实际开奖号码"""
    red_str = entry['number'].split(' ')
    red = sorted([int(x) for x in red_str])
    blue = int(entry['refernumber'])
    return red, blue

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def final_algorithm_test(data, test_periods=50):
    """最终算法测试"""
    print("=" * 80)
    print("双色球预测算法最终回测报告")
    print("=" * 80)
    
    algorithms = {
        '原始马尔科夫算法': {'func': markov_predict, 'results': []},
        '终极优化算法': {'func': ultimate_predict, 'results': []}
    }
    
    print(f"\n开始回测，测试最近{test_periods}期数据...")
    
    for i in range(test_periods, min(len(data), test_periods + test_periods)):
        training_data = data[i:]
        actual_red, actual_blue = parse_actual_numbers(data[i-1])
        
        for name, algo in algorithms.items():
            try:
                predicted_red, predicted_blue = algo['func'](training_data)
                
                if predicted_red and predicted_blue:
                    red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
                    
                    result = {
                        'period': data[i-1]['issueno'],
                        'predicted_red': predicted_red,
                        'predicted_blue': predicted_blue,
                        'actual_red': actual_red,
                        'actual_blue': actual_blue,
                        'red_hits': red_hits,
                        'blue_hit': blue_hit,
                        'score': red_hits + blue_hit * 2  # 综合得分
                    }
                    
                    algo['results'].append(result)
                    
            except Exception as e:
                print(f"{name} 预测期号{data[i-1]['issueno']}时出错: {e}")
    
    return algorithms

def generate_comprehensive_report(algorithms):
    """生成综合报告"""
    print("\n" + "=" * 80)
    print("详细性能分析报告")
    print("=" * 80)
    
    for name, algo in algorithms.items():
        results = algo['results']
        if not results:
            continue
            
        print(f"\n{name}:")
        print("-" * 50)
        
        # 基础统计
        total_tests = len(results)
        red_hits_dist = defaultdict(int)
        blue_hits = 0
        total_red_hits = 0
        total_score = 0
        
        for result in results:
            red_hits_dist[result['red_hits']] += 1
            blue_hits += result['blue_hit']
            total_red_hits += result['red_hits']
            total_score += result['score']
        
        # 红球命中分布
        print("红球命中分布:")
        for hits in range(7):
            count = red_hits_dist[hits]
            percentage = (count / total_tests) * 100 if total_tests > 0 else 0
            print(f"  命中{hits}个: {count:2d}次 ({percentage:5.1f}%)")
        
        # 关键指标
        avg_red_hits = total_red_hits / total_tests if total_tests > 0 else 0
        blue_hit_rate = (blue_hits / total_tests) * 100 if total_tests > 0 else 0
        avg_score = total_score / total_tests if total_tests > 0 else 0
        
        print(f"\n关键指标:")
        print(f"  平均红球命中: {avg_red_hits:.2f}/6")
        print(f"  蓝球命中率: {blue_hits}/{total_tests} ({blue_hit_rate:.1f}%)")
        print(f"  平均综合得分: {avg_score:.2f}")
        
        # 命中率分析
        red_3_plus = sum(red_hits_dist[i] for i in range(3, 7))
        red_3_plus_rate = (red_3_plus / total_tests) * 100 if total_tests > 0 else 0
        
        red_4_plus = sum(red_hits_dist[i] for i in range(4, 7))
        red_4_plus_rate = (red_4_plus / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n高命中率分析:")
        print(f"  红球命中3+个: {red_3_plus}次 ({red_3_plus_rate:.1f}%)")
        print(f"  红球命中4+个: {red_4_plus}次 ({red_4_plus_rate:.1f}%)")
        
        # 最佳预测记录
        best_predictions = sorted(results, key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)[:5]
        print(f"\n最佳预测记录:")
        for i, pred in enumerate(best_predictions, 1):
            print(f"  {i}. 期号{pred['period']}: 红球{pred['red_hits']}/6, 蓝球{'✓' if pred['blue_hit'] else '✗'} (得分:{pred['score']})")
            print(f"     预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"     实际: {pred['actual_red']} + {pred['actual_blue']}")

def algorithm_comparison(algorithms):
    """算法对比"""
    print("\n" + "=" * 80)
    print("算法对比总结")
    print("=" * 80)
    
    comparison_data = []
    
    for name, algo in algorithms.items():
        results = algo['results']
        if results:
            total_tests = len(results)
            total_red_hits = sum(r['red_hits'] for r in results)
            blue_hits = sum(r['blue_hit'] for r in results)
            total_score = sum(r['score'] for r in results)
            
            avg_red_hits = total_red_hits / total_tests
            blue_hit_rate = blue_hits / total_tests
            avg_score = total_score / total_tests
            
            comparison_data.append({
                'name': name,
                'avg_red_hits': avg_red_hits,
                'blue_hit_rate': blue_hit_rate,
                'avg_score': avg_score,
                'total_tests': total_tests
            })
    
    # 排序并显示
    comparison_data.sort(key=lambda x: x['avg_score'], reverse=True)
    
    print(f"{'算法名称':<20} {'平均红球命中':<12} {'蓝球命中率':<10} {'平均得分':<8} {'测试期数':<8}")
    print("-" * 70)
    
    for data in comparison_data:
        print(f"{data['name']:<20} {data['avg_red_hits']:<12.2f} {data['blue_hit_rate']:<10.1%} {data['avg_score']:<8.2f} {data['total_tests']:<8d}")
    
    # 改进效果
    if len(comparison_data) >= 2:
        original = comparison_data[1]  # 原始算法
        optimized = comparison_data[0]  # 优化算法
        
        red_improvement = ((optimized['avg_red_hits'] - original['avg_red_hits']) / original['avg_red_hits']) * 100
        blue_improvement = ((optimized['blue_hit_rate'] - original['blue_hit_rate']) / original['blue_hit_rate']) * 100 if original['blue_hit_rate'] > 0 else 0
        score_improvement = ((optimized['avg_score'] - original['avg_score']) / original['avg_score']) * 100
        
        print(f"\n改进效果:")
        print(f"  红球命中率提升: {red_improvement:+.1f}%")
        print(f"  蓝球命中率提升: {blue_improvement:+.1f}%")
        print(f"  综合得分提升: {score_improvement:+.1f}%")

def generate_final_recommendations():
    """生成最终建议"""
    print("\n" + "=" * 80)
    print("最终建议与总结")
    print("=" * 80)
    
    print("\n算法改进总结:")
    print("1. 实现了智能数据检查和增量更新，解决了API调用限制问题")
    print("2. 从简单马尔科夫链发展到多维度综合预测模型")
    print("3. 引入了位置分析、间隔分析、组合模式等多种特征")
    print("4. 实现了组合优化和质量评估机制")
    
    print("\n技术亮点:")
    print("• 多维度模式分析：频率、位置、间隔、组合、数值特征")
    print("• 智能权重分配：历史频率 + 最近趋势 + 马尔科夫链 + 间隔分析")
    print("• 组合优化：基于数学特征的组合质量评估")
    print("• 自适应预测：根据历史数据动态调整预测策略")
    
    print("\n局限性与改进方向:")
    print("• 双色球本质上是随机事件，任何算法都无法保证高准确率")
    print("• 可考虑引入深度学习模型（LSTM、Transformer等）")
    print("• 可增加外部因素分析（时间、季节、特殊事件等）")
    print("• 可实现在线学习，根据最新开奖结果持续优化模型")
    
    print("\n使用建议:")
    print("• 将预测结果作为参考，不应作为投注依据")
    print("• 理性对待彩票，量力而行")
    print("• 算法主要用于学习数据分析和机器学习技术")

if __name__ == '__main__':
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 加载数据
    data = load_data()
    print(f"加载了{len(data)}期历史数据")
    
    # 进行最终测试
    algorithms = final_algorithm_test(data, test_periods=30)
    
    # 生成综合报告
    generate_comprehensive_report(algorithms)
    
    # 算法对比
    algorithm_comparison(algorithms)
    
    # 最终建议
    generate_final_recommendations()
    
    print("\n" + "=" * 80)
    print("回测分析完成！")
    print("=" * 80)