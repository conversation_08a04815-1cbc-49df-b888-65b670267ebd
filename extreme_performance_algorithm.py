#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极限性能双色球预测算法
目标：红球命中率 >= 3/6，蓝球命中率 >= 31.25%
采用多模型集成、深度特征工程和自适应学习策略
"""

import json
import numpy as np
from collections import defaultdict, Counter
import random
from datetime import datetime
import itertools
from scipy import stats
import math

class ExtremePerformancePredictor:
    def __init__(self, data):
        self.data = data
        self.red_balls = []
        self.blue_balls = []
        self.periods = []
        self.parse_data()
        
    def parse_data(self):
        """解析历史数据"""
        for item in self.data:
            red_str = item['number']
            blue_str = item['refernumber']
            
            red_nums = [int(x) for x in red_str.split()]
            self.red_balls.append(sorted(red_nums))
            self.blue_balls.append(int(blue_str))
            self.periods.append(item['issueno'])
    
    def deep_pattern_analysis(self):
        """深度模式分析"""
        patterns = {
            'frequency': self.frequency_analysis(),
            'position': self.position_analysis(),
            'interval': self.interval_analysis(),
            'correlation': self.correlation_analysis(),
            'sequence': self.sequence_analysis(),
            'distribution': self.distribution_analysis(),
            'trend': self.trend_analysis(),
            'cycle': self.cycle_analysis()
        }
        return patterns
    
    def frequency_analysis(self):
        """频率分析"""
        red_freq = Counter()
        blue_freq = Counter()
        
        # 全期频率
        for red_combo, blue in zip(self.red_balls, self.blue_balls):
            for num in red_combo:
                red_freq[num] += 1
            blue_freq[blue] += 1
        
        # 近期频率（不同时间窗口）
        windows = [10, 20, 30, 50]
        recent_freqs = {}
        
        for window in windows:
            recent_red = Counter()
            recent_blue = Counter()
            
            for i in range(min(window, len(self.red_balls))):
                for num in self.red_balls[-(i+1)]:
                    recent_red[num] += 1
                recent_blue[self.blue_balls[-(i+1)]] += 1
            
            recent_freqs[f'red_{window}'] = recent_red
            recent_freqs[f'blue_{window}'] = recent_blue
        
        return {
            'red_total': red_freq,
            'blue_total': blue_freq,
            'recent': recent_freqs
        }
    
    def position_analysis(self):
        """位置分析"""
        position_freq = [Counter() for _ in range(6)]
        position_patterns = defaultdict(list)
        
        for red_combo in self.red_balls:
            for pos, num in enumerate(red_combo):
                position_freq[pos][num] += 1
                position_patterns[pos].append(num)
        
        # 位置间关系
        position_correlations = {}
        for i in range(6):
            for j in range(i+1, 6):
                pos_i_nums = [combo[i] for combo in self.red_balls]
                pos_j_nums = [combo[j] for combo in self.red_balls]
                if len(pos_i_nums) > 1:
                    corr, _ = stats.pearsonr(pos_i_nums, pos_j_nums)
                    position_correlations[f'{i}_{j}'] = corr if not math.isnan(corr) else 0
        
        return {
            'frequency': position_freq,
            'patterns': position_patterns,
            'correlations': position_correlations
        }
    
    def interval_analysis(self):
        """间隔分析"""
        red_intervals = defaultdict(list)
        blue_intervals = defaultdict(list)
        
        # 红球间隔
        for num in range(1, 34):
            last_appear = -1
            for i, red_combo in enumerate(self.red_balls):
                if num in red_combo:
                    if last_appear != -1:
                        red_intervals[num].append(i - last_appear)
                    last_appear = i
        
        # 蓝球间隔
        for num in range(1, 17):
            last_appear = -1
            for i, blue in enumerate(self.blue_balls):
                if blue == num:
                    if last_appear != -1:
                        blue_intervals[num].append(i - last_appear)
                    last_appear = i
        
        # 计算期望间隔和当前间隔
        red_expected = {}
        blue_expected = {}
        red_current = {}
        blue_current = {}
        
        for num in range(1, 34):
            if red_intervals[num]:
                red_expected[num] = np.mean(red_intervals[num])
            
            # 计算当前间隔
            for i, combo in enumerate(reversed(self.red_balls)):
                if num in combo:
                    red_current[num] = i
                    break
            else:
                red_current[num] = len(self.red_balls)
        
        for num in range(1, 17):
            if blue_intervals[num]:
                blue_expected[num] = np.mean(blue_intervals[num])
            
            # 计算当前间隔
            for i, blue in enumerate(reversed(self.blue_balls)):
                if blue == num:
                    blue_current[num] = i
                    break
            else:
                blue_current[num] = len(self.blue_balls)
        
        return {
            'red_intervals': red_intervals,
            'blue_intervals': blue_intervals,
            'red_expected': red_expected,
            'blue_expected': blue_expected,
            'red_current': red_current,
            'blue_current': blue_current
        }
    
    def correlation_analysis(self):
        """相关性分析"""
        # 红球与蓝球关系
        red_blue_relations = defaultdict(list)
        
        for red_combo, blue in zip(self.red_balls, self.blue_balls):
            red_sum = sum(red_combo)
            red_blue_relations['sum_blue'].append((red_sum, blue))
            
            for red_num in red_combo:
                red_blue_relations[f'red_{red_num}'].append(blue)
        
        # 红球间相关性
        red_correlations = {}
        for i in range(1, 34):
            for j in range(i+1, 34):
                appear_together = 0
                total_i = 0
                total_j = 0
                
                for combo in self.red_balls:
                    if i in combo:
                        total_i += 1
                        if j in combo:
                            appear_together += 1
                    if j in combo:
                        total_j += 1
                
                if total_i > 0 and total_j > 0:
                    correlation = appear_together / min(total_i, total_j)
                    red_correlations[f'{i}_{j}'] = correlation
        
        return {
            'red_blue_relations': red_blue_relations,
            'red_correlations': red_correlations
        }
    
    def sequence_analysis(self):
        """序列分析"""
        # 连号分析
        consecutive_patterns = Counter()
        
        for combo in self.red_balls:
            consecutive_count = 0
            for i in range(len(combo) - 1):
                if combo[i+1] - combo[i] == 1:
                    consecutive_count += 1
            consecutive_patterns[consecutive_count] += 1
        
        # 等差数列分析
        arithmetic_patterns = Counter()
        
        for combo in self.red_balls:
            for length in range(3, 7):  # 3-6个数的等差数列
                for start in range(len(combo) - length + 1):
                    subseq = combo[start:start+length]
                    if len(set(np.diff(subseq))) == 1:  # 等差数列
                        diff = subseq[1] - subseq[0]
                        arithmetic_patterns[f'{length}_{diff}'] += 1
        
        return {
            'consecutive': consecutive_patterns,
            'arithmetic': arithmetic_patterns
        }
    
    def distribution_analysis(self):
        """分布分析"""
        # 和值分布
        sum_distribution = Counter([sum(combo) for combo in self.red_balls])
        
        # 跨度分布
        span_distribution = Counter([max(combo) - min(combo) for combo in self.red_balls])
        
        # 奇偶分布
        odd_even_distribution = Counter()
        for combo in self.red_balls:
            odd_count = sum(1 for x in combo if x % 2 == 1)
            odd_even_distribution[odd_count] += 1
        
        # 区间分布
        zone_distribution = Counter()
        for combo in self.red_balls:
            zone1 = sum(1 for x in combo if 1 <= x <= 11)
            zone2 = sum(1 for x in combo if 12 <= x <= 22)
            zone3 = sum(1 for x in combo if 23 <= x <= 33)
            zone_distribution[f'{zone1}_{zone2}_{zone3}'] += 1
        
        return {
            'sum': sum_distribution,
            'span': span_distribution,
            'odd_even': odd_even_distribution,
            'zone': zone_distribution
        }
    
    def trend_analysis(self):
        """趋势分析"""
        if len(self.red_balls) < 20:
            return {}
        
        # 计算各号码的趋势
        red_trends = {}
        blue_trends = {}
        
        window_size = 10
        
        for num in range(1, 34):
            recent_freq = sum(1 for i in range(window_size) 
                            if num in self.red_balls[-(i+1)])
            prev_freq = sum(1 for i in range(window_size, window_size*2) 
                          if len(self.red_balls) > window_size + i and 
                          num in self.red_balls[-(window_size+i+1)])
            
            if prev_freq > 0:
                red_trends[num] = recent_freq / prev_freq
            else:
                red_trends[num] = recent_freq + 1
        
        for num in range(1, 17):
            recent_freq = sum(1 for i in range(window_size) 
                            if self.blue_balls[-(i+1)] == num)
            prev_freq = sum(1 for i in range(window_size, window_size*2) 
                          if len(self.blue_balls) > window_size + i and 
                          self.blue_balls[-(window_size+i+1)] == num)
            
            if prev_freq > 0:
                blue_trends[num] = recent_freq / prev_freq
            else:
                blue_trends[num] = recent_freq + 1
        
        return {
            'red_trends': red_trends,
            'blue_trends': blue_trends
        }
    
    def cycle_analysis(self):
        """周期分析"""
        # 简单周期检测
        red_cycles = {}
        blue_cycles = {}
        
        for num in range(1, 34):
            appearances = [i for i, combo in enumerate(self.red_balls) if num in combo]
            if len(appearances) >= 3:
                intervals = np.diff(appearances)
                if len(intervals) > 1:
                    # 检测是否有周期性
                    mean_interval = np.mean(intervals)
                    std_interval = np.std(intervals)
                    if std_interval < mean_interval * 0.3:  # 相对稳定的间隔
                        red_cycles[num] = mean_interval
        
        for num in range(1, 17):
            appearances = [i for i, blue in enumerate(self.blue_balls) if blue == num]
            if len(appearances) >= 3:
                intervals = np.diff(appearances)
                if len(intervals) > 1:
                    mean_interval = np.mean(intervals)
                    std_interval = np.std(intervals)
                    if std_interval < mean_interval * 0.3:
                        blue_cycles[num] = mean_interval
        
        return {
            'red_cycles': red_cycles,
            'blue_cycles': blue_cycles
        }
    
    def ensemble_red_prediction(self, patterns):
        """集成红球预测"""
        models = {
            'frequency': self.frequency_model,
            'position': self.position_model,
            'interval': self.interval_model,
            'correlation': self.correlation_model,
            'trend': self.trend_model,
            'distribution': self.distribution_model
        }
        
        model_predictions = {}
        model_weights = {
            'frequency': 0.25,
            'position': 0.15,
            'interval': 0.20,
            'correlation': 0.15,
            'trend': 0.15,
            'distribution': 0.10
        }
        
        # 获取各模型预测
        for model_name, model_func in models.items():
            try:
                prediction = model_func(patterns)
                model_predictions[model_name] = prediction
            except Exception as e:
                print(f"模型 {model_name} 预测失败: {e}")
                model_predictions[model_name] = list(range(1, 7))
        
        # 集成预测
        final_scores = defaultdict(float)
        
        for model_name, prediction in model_predictions.items():
            weight = model_weights[model_name]
            for i, num in enumerate(prediction):
                # 位置权重：前面的号码权重更高
                position_weight = (len(prediction) - i) / len(prediction)
                final_scores[num] += weight * position_weight
        
        # 选择得分最高的6个号码
        sorted_candidates = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 优化组合
        best_combo = self.optimize_combination([num for num, _ in sorted_candidates[:12]], patterns)
        
        return best_combo
    
    def frequency_model(self, patterns):
        """频率模型"""
        freq_data = patterns['frequency']
        
        # 综合多个时间窗口的频率
        scores = defaultdict(float)
        
        # 总频率权重
        total_freq = sum(freq_data['red_total'].values())
        for num in range(1, 34):
            scores[num] += (freq_data['red_total'][num] / total_freq) * 0.4
        
        # 近期频率权重
        windows = [10, 20, 30, 50]
        window_weights = [0.3, 0.2, 0.1, 0.0]
        
        for window, weight in zip(windows, window_weights):
            if f'red_{window}' in freq_data['recent']:
                recent_freq = freq_data['recent'][f'red_{window}']
                total_recent = sum(recent_freq.values())
                if total_recent > 0:
                    for num in range(1, 34):
                        scores[num] += (recent_freq[num] / total_recent) * weight
        
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)[:6]
    
    def position_model(self, patterns):
        """位置模型"""
        position_data = patterns['position']
        
        # 为每个位置选择最可能的号码
        position_candidates = []
        
        for pos in range(6):
            pos_freq = position_data['frequency'][pos]
            if pos_freq:
                # 选择该位置频率最高的前3个号码
                top_nums = sorted(pos_freq.items(), key=lambda x: x[1], reverse=True)[:3]
                position_candidates.extend([num for num, _ in top_nums])
        
        # 去重并选择6个
        unique_candidates = list(set(position_candidates))
        
        if len(unique_candidates) >= 6:
            return sorted(unique_candidates[:6])
        else:
            # 补充高频号码
            all_freq = Counter()
            for pos_freq in position_data['frequency']:
                all_freq.update(pos_freq)
            
            additional = [num for num, _ in all_freq.most_common() 
                         if num not in unique_candidates]
            
            result = unique_candidates + additional[:6-len(unique_candidates)]
            return sorted(result[:6])
    
    def interval_model(self, patterns):
        """间隔模型"""
        interval_data = patterns['interval']
        
        scores = defaultdict(float)
        
        for num in range(1, 34):
            if num in interval_data['red_expected'] and num in interval_data['red_current']:
                expected = interval_data['red_expected'][num]
                current = interval_data['red_current'][num]
                
                if expected > 0:
                    # 当前间隔接近期望间隔的号码得分更高
                    ratio = current / expected
                    if 0.8 <= ratio <= 1.5:  # 接近期望的范围
                        scores[num] += 1.0
                    elif ratio > 1.5:  # 超期号码
                        scores[num] += min(2.0, ratio / 1.5)
        
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)[:6]
    
    def correlation_model(self, patterns):
        """相关性模型"""
        corr_data = patterns['correlation']
        
        # 基于历史相关性选择号码组合
        scores = defaultdict(float)
        
        # 基础频率
        for num in range(1, 34):
            scores[num] += 0.5
        
        # 相关性加分
        for pair_key, correlation in corr_data['red_correlations'].items():
            if correlation > 0.3:  # 高相关性
                nums = [int(x) for x in pair_key.split('_')]
                for num in nums:
                    scores[num] += correlation * 0.5
        
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)[:6]
    
    def trend_model(self, patterns):
        """趋势模型"""
        if 'red_trends' not in patterns['trend']:
            return list(range(1, 7))
        
        trend_data = patterns['trend']['red_trends']
        
        # 选择上升趋势的号码
        trending_up = [(num, trend) for num, trend in trend_data.items() if trend > 1.2]
        trending_up.sort(key=lambda x: x[1], reverse=True)
        
        if len(trending_up) >= 6:
            return [num for num, _ in trending_up[:6]]
        else:
            # 补充稳定的号码
            stable = [(num, trend) for num, trend in trend_data.items() 
                     if 0.8 <= trend <= 1.2]
            stable.sort(key=lambda x: x[1], reverse=True)
            
            result = [num for num, _ in trending_up]
            result.extend([num for num, _ in stable[:6-len(result)]])
            
            return result[:6]
    
    def distribution_model(self, patterns):
        """分布模型"""
        dist_data = patterns['distribution']
        
        # 基于历史分布特征生成号码
        # 选择最常见的和值范围
        sum_dist = dist_data['sum']
        common_sums = [s for s, count in sum_dist.most_common(10)]
        target_sum = random.choice(common_sums) if common_sums else 105
        
        # 选择最常见的跨度
        span_dist = dist_data['span']
        common_spans = [s for s, count in span_dist.most_common(5)]
        target_span = random.choice(common_spans) if common_spans else 20
        
        # 生成符合条件的组合
        attempts = 0
        while attempts < 1000:
            combo = sorted(random.sample(range(1, 34), 6))
            if abs(sum(combo) - target_sum) <= 10 and abs(max(combo) - min(combo) - target_span) <= 5:
                return combo
            attempts += 1
        
        # 如果无法生成，返回随机组合
        return sorted(random.sample(range(1, 34), 6))
    
    def optimize_combination(self, candidates, patterns):
        """优化号码组合"""
        best_combo = None
        best_score = -1
        
        # 生成多个候选组合
        for _ in range(2000):
            combo = sorted(random.sample(candidates, min(6, len(candidates))))
            if len(combo) < 6:
                # 补充随机号码
                remaining = [x for x in range(1, 34) if x not in combo]
                combo.extend(random.sample(remaining, 6 - len(combo)))
                combo = sorted(combo)
            
            score = self.evaluate_combination(combo, patterns)
            
            if score > best_score:
                best_score = score
                best_combo = combo
        
        return best_combo if best_combo else sorted(candidates[:6])
    
    def evaluate_combination(self, combo, patterns):
        """评估组合质量"""
        score = 0
        
        # 和值评估
        combo_sum = sum(combo)
        sum_dist = patterns['distribution']['sum']
        if combo_sum in sum_dist:
            score += sum_dist[combo_sum] * 0.1
        
        # 跨度评估
        span = max(combo) - min(combo)
        span_dist = patterns['distribution']['span']
        if span in span_dist:
            score += span_dist[span] * 0.1
        
        # 奇偶比评估
        odd_count = sum(1 for x in combo if x % 2 == 1)
        odd_dist = patterns['distribution']['odd_even']
        if odd_count in odd_dist:
            score += odd_dist[odd_count] * 0.1
        
        # 相关性评估
        corr_data = patterns['correlation']['red_correlations']
        for i in range(len(combo)):
            for j in range(i+1, len(combo)):
                pair_key = f'{combo[i]}_{combo[j]}'
                if pair_key in corr_data:
                    score += corr_data[pair_key] * 10
        
        # 频率评估
        freq_data = patterns['frequency']['red_total']
        total_freq = sum(freq_data.values())
        for num in combo:
            score += (freq_data[num] / total_freq) * 100
        
        return score
    
    def ensemble_blue_prediction(self, red_combo, patterns):
        """集成蓝球预测"""
        blue_scores = defaultdict(float)
        
        # 1. 频率权重 (30%)
        blue_freq = patterns['frequency']['blue_total']
        total_blue_freq = sum(blue_freq.values())
        for num in range(1, 17):
            blue_scores[num] += (blue_freq[num] / total_blue_freq) * 0.3
        
        # 2. 与红球关系权重 (25%)
        red_sum = sum(red_combo)
        corr_data = patterns['correlation']['red_blue_relations']
        
        if 'sum_blue' in corr_data:
            sum_blue_pairs = corr_data['sum_blue']
            similar_sums = [(s, b) for s, b in sum_blue_pairs if abs(s - red_sum) <= 10]
            if similar_sums:
                blue_counter = Counter([b for _, b in similar_sums])
                for blue, count in blue_counter.items():
                    blue_scores[blue] += (count / len(similar_sums)) * 0.25
        
        # 3. 间隔权重 (20%)
        interval_data = patterns['interval']
        for num in range(1, 17):
            if num in interval_data['blue_expected'] and num in interval_data['blue_current']:
                expected = interval_data['blue_expected'][num]
                current = interval_data['blue_current'][num]
                
                if expected > 0:
                    ratio = current / expected
                    if ratio >= 1.0:  # 超期或接近期望
                        blue_scores[num] += min(0.2, ratio * 0.1)
        
        # 4. 趋势权重 (15%)
        if 'blue_trends' in patterns['trend']:
            blue_trends = patterns['trend']['blue_trends']
            for num, trend in blue_trends.items():
                if trend > 1.0:
                    blue_scores[num] += trend * 0.1
        
        # 5. 近期频率权重 (10%)
        recent_blues = self.blue_balls[-10:] if len(self.blue_balls) >= 10 else self.blue_balls
        if recent_blues:
            recent_counter = Counter(recent_blues)
            total_recent = len(recent_blues)
            for num, count in recent_counter.items():
                blue_scores[num] += (count / total_recent) * 0.1
        
        # 选择得分最高的蓝球
        best_blue = max(blue_scores.items(), key=lambda x: x[1])[0]
        
        return best_blue
    
    def predict(self):
        """主预测函数"""
        if len(self.red_balls) < 100:
            # 数据不足，返回随机预测
            return sorted(random.sample(range(1, 34), 6)), random.randint(1, 16)
        
        # 深度模式分析
        patterns = self.deep_pattern_analysis()
        
        # 集成红球预测
        red_prediction = self.ensemble_red_prediction(patterns)
        
        # 集成蓝球预测
        blue_prediction = self.ensemble_blue_prediction(red_prediction, patterns)
        
        return red_prediction, blue_prediction

def load_data():
    """加载历史数据"""
    try:
        with open('ssq_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("错误：未找到 ssq_data.json 文件")
        return []

def parse_actual_numbers(red_str, blue_str):
    """解析实际开奖号码"""
    red_nums = [int(x) for x in red_str.split()]
    blue_num = int(blue_str)
    return sorted(red_nums), blue_num

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def extreme_performance_backtest():
    """极限性能回测"""
    print("=== 极限性能双色球预测算法回测 ===")
    print("目标：红球命中率 >= 3/6，蓝球命中率 >= 31.25%")
    print()
    
    data = load_data()
    if len(data) < 500:
        print(f"警告：数据不足500期，当前只有 {len(data)} 期数据")
        return
    
    print(f"加载了 {len(data)} 期历史数据")
    
    test_periods = 500  # 测试500期
    training_periods = 300  # 使用300期数据进行训练
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    
    print(f"开始回测最近 {test_periods} 期数据...")
    print()
    
    for i in range(test_periods):
        start_idx = max(0, len(data) - test_periods + i - training_periods)
        end_idx = len(data) - test_periods + i
        
        if end_idx <= start_idx:
            continue
            
        training_data = data[start_idx:end_idx]
        test_data = data[len(data) - test_periods + i]
        
        try:
            predictor = ExtremePerformancePredictor(training_data)
            predicted_red, predicted_blue = predictor.predict()
            
            actual_red, actual_blue = parse_actual_numbers(test_data['number'], test_data['refernumber'])
            
            red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            results.append({
                'period': test_data['issueno'],
                'predicted_red': predicted_red,
                'predicted_blue': predicted_blue,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'red_hits': red_hits,
                'blue_hit': blue_hit
            })
            
            if (i + 1) % 100 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = blue_hits_total / (i + 1)
                print(f"已完成 {i + 1}/{test_periods} 期回测")
                print(f"当前平均红球命中: {current_red_avg:.2f}/6")
                print(f"当前蓝球命中率: {current_blue_rate:.1%}")
                print()
                
        except Exception as e:
            print(f"期号 {test_data['issueno']} 预测失败: {e}")
            continue
    
    # 统计结果
    print("=== 极限性能算法回测结果统计 ===")
    
    avg_red_hits = red_hits_total / len(results) if results else 0
    blue_hit_rate = blue_hits_total / len(results) if results else 0
    
    print(f"平均红球命中数: {avg_red_hits:.2f}/6")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits_total}/{len(results)})")
    print()
    
    # 目标达成情况
    target_red = 3.0
    target_blue = 5/16
    
    print("=== 目标达成情况 ===")
    print(f"红球目标: >= {target_red}/6, 实际: {avg_red_hits:.2f}/6 {'✓' if avg_red_hits >= target_red else '✗'}")
    print(f"蓝球目标: >= {target_blue:.1%}, 实际: {blue_hit_rate:.1%} {'✓' if blue_hit_rate >= target_blue else '✗'}")
    print()
    
    # 详细分析
    red_hit_distribution = Counter([r['red_hits'] for r in results])
    print("=== 红球命中分布 ===")
    for hits in sorted(red_hit_distribution.keys()):
        count = red_hit_distribution[hits]
        percentage = count / len(results) * 100
        print(f"{hits}个红球命中: {count}次 ({percentage:.1f}%)")
    print()
    
    # 高命中率期数统计
    high_red_hits = [r for r in results if r['red_hits'] >= 3]
    blue_hits = [r for r in results if r['blue_hit'] == 1]
    
    print("=== 高性能表现统计 ===")
    print(f"红球命中>=3个的期数: {len(high_red_hits)}期 ({len(high_red_hits)/len(results)*100:.1f}%)")
    print(f"蓝球命中期数: {len(blue_hits)}期 ({len(blue_hits)/len(results)*100:.1f}%)")
    print()
    
    # 最佳预测记录
    best_results = sorted(results, key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)[:15]
    print("=== 最佳预测记录 (前15期) ===")
    for i, result in enumerate(best_results, 1):
        print(f"{i:2d}. 期号:{result['period']} 红球:{result['red_hits']}/6 蓝球:{'✓' if result['blue_hit'] else '✗'}")
        print(f"    预测红球: {result['predicted_red']}")
        print(f"    实际红球: {result['actual_red']}")
        print(f"    预测蓝球: {result['predicted_blue']}, 实际蓝球: {result['actual_blue']}")
        print()
    
    return results

if __name__ == "__main__":
    results = extreme_performance_backtest()