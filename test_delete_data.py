import json

# 读取原始数据
with open('ssq_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f'原始数据量: {len(data)}')
print(f'最新期号: {data[0]["issueno"]}')

# 删除最近两期数据
new_data = data[2:]
print(f'删除后数据量: {len(new_data)}')
print(f'删除后最新期号: {new_data[0]["issueno"]}')

# 保存修改后的数据
with open('ssq_data.json', 'w', encoding='utf-8') as f:
    json.dump(new_data, f, ensure_ascii=False, indent=4)

print('已删除最近两期数据')