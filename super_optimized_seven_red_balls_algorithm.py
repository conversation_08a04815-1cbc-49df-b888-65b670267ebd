#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级优化7红球增概率双色球预测算法
策略：基于深度学习思想的终极优化版本
目标：红球命中率≥1.5/7 (21.4%)，蓝球命中率≥10%
超级优化点：
1. 自适应神经网络权重系统
2. 量子启发的特征融合
3. 强化学习策略选择
4. 深度蓝球预测模型
5. 多模态数据融合
"""

import json
import random
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations
import math

class SuperOptimizedSevenRedBallsPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = list(range(1, 34))
        self.blue_balls = list(range(1, 17))
        
        # 统计数据缓存
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_position_freq = defaultdict(lambda: defaultdict(int))
        self.red_intervals = defaultdict(list)
        self.blue_intervals = []
        self.red_pairs = defaultdict(int)
        self.red_triplets = defaultdict(int)
        self.red_sequences = defaultdict(int)
        self.red_sum_patterns = defaultdict(int)
        self.blue_sum_patterns = defaultdict(int)
        
        # 神经网络启发的权重系统
        self.neural_weights = {
            'frequency': 0.20,
            'position': 0.16,
            'intervals': 0.18,
            'hot_trend': 0.14,
            'distribution': 0.12,
            'combination': 0.10,
            'sequence': 0.06,
            'sum_pattern': 0.04
        }
        
        # 蓝球专用权重
        self.blue_neural_weights = {
            'frequency': 0.25,
            'intervals': 0.30,
            'hot_trend': 0.20,
            'sum_correlation': 0.15,
            'position_correlation': 0.10
        }
        
        # 强化学习参数
        self.strategy_rewards = defaultdict(float)
        self.strategy_counts = defaultdict(int)
        self.learning_rate = 0.1
        self.exploration_rate = 0.2
        
        # 性能追踪和自适应
        self.performance_history = []
        self.weight_momentum = defaultdict(float)
        self.momentum_factor = 0.9
        
    def load_data(self, filename):
        """加载数据"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        print(f"加载了 {len(self.data)} 期数据")
        
    def neural_weight_adaptation(self):
        """神经网络启发的权重自适应"""
        if len(self.performance_history) < 20:
            return self.neural_weights.copy(), self.blue_neural_weights.copy()
        
        # 分析最近20期的表现梯度
        recent_performance = self.performance_history[-20:]
        red_trend = np.polyfit(range(20), [p['red_hits'] for p in recent_performance], 1)[0]
        blue_trend = np.polyfit(range(20), [p['blue_hit'] for p in recent_performance], 1)[0]
        
        # 计算权重梯度
        red_weights = self.neural_weights.copy()
        blue_weights = self.blue_neural_weights.copy()
        
        # 红球权重调整（梯度下降思想）
        if red_trend < -0.01:  # 下降趋势
            # 增强预测性权重
            red_weights['intervals'] += 0.03
            red_weights['hot_trend'] += 0.02
            red_weights['frequency'] -= 0.02
            red_weights['distribution'] -= 0.03
        elif red_trend > 0.01:  # 上升趋势
            # 保持当前策略，微调
            red_weights['combination'] += 0.01
            red_weights['sequence'] += 0.01
            red_weights['sum_pattern'] -= 0.02
        
        # 蓝球权重调整
        if blue_trend < -0.005:  # 蓝球下降
            blue_weights['intervals'] += 0.05
            blue_weights['sum_correlation'] += 0.03
            blue_weights['frequency'] -= 0.04
            blue_weights['hot_trend'] -= 0.04
        
        # 使用动量优化
        for key in red_weights:
            gradient = red_weights[key] - self.neural_weights[key]
            self.weight_momentum[f'red_{key}'] = (self.momentum_factor * self.weight_momentum[f'red_{key}'] + 
                                                 (1 - self.momentum_factor) * gradient)
            red_weights[key] = self.neural_weights[key] + self.learning_rate * self.weight_momentum[f'red_{key}']
        
        for key in blue_weights:
            gradient = blue_weights[key] - self.blue_neural_weights[key]
            self.weight_momentum[f'blue_{key}'] = (self.momentum_factor * self.weight_momentum[f'blue_{key}'] + 
                                                  (1 - self.momentum_factor) * gradient)
            blue_weights[key] = self.blue_neural_weights[key] + self.learning_rate * self.weight_momentum[f'blue_{key}']
        
        # 权重归一化
        red_total = sum(red_weights.values())
        blue_total = sum(blue_weights.values())
        
        for key in red_weights:
            red_weights[key] /= red_total
        for key in blue_weights:
            blue_weights[key] /= blue_total
        
        return red_weights, blue_weights
        
    def quantum_inspired_analysis(self, recent_count=400):
        """量子启发的数据分析 - 叠加态和纠缠特征"""
        recent_data = self.data[-recent_count:] if len(self.data) > recent_count else self.data
        
        # 重置统计
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_position_freq.clear()
        self.red_intervals.clear()
        self.blue_intervals.clear()
        self.red_pairs.clear()
        self.red_triplets.clear()
        self.red_sequences.clear()
        self.red_sum_patterns.clear()
        self.blue_sum_patterns.clear()
        
        for i, draw in enumerate(recent_data):
            red_nums = list(map(int, draw['number'].split()))
            blue_num = int(draw['refernumber'])
            
            # 量子权重 - 基于时间衰减的叠加态
            quantum_weight = math.exp(-0.01 * (len(recent_data) - i - 1))
            
            # 基础频率统计（量子加权）
            for num in red_nums:
                self.red_frequency[num] += quantum_weight
            self.blue_frequency[blue_num] += quantum_weight
            
            # 位置频率统计（量子纠缠）
            for pos, num in enumerate(red_nums):
                entanglement_factor = 1.0 + 0.1 * math.sin(pos * math.pi / 6)
                self.red_position_freq[pos][num] += quantum_weight * entanglement_factor
            
            # 间隔统计
            for num in red_nums:
                self.red_intervals[num].append(i)
            self.blue_intervals.append((i, blue_num))
            
            # 高阶组合统计（量子叠加）
            for pair in combinations(red_nums, 2):
                self.red_pairs[tuple(sorted(pair))] += quantum_weight
            
            for triplet in combinations(red_nums, 3):
                self.red_triplets[tuple(sorted(triplet))] += quantum_weight
            
            # 序列和模式分析
            sorted_reds = sorted(red_nums)
            red_sum = sum(red_nums)
            
            # 和值模式
            sum_range = red_sum // 10
            self.red_sum_patterns[sum_range] += quantum_weight
            
            blue_sum_range = (red_sum + blue_num) % 16
            self.blue_sum_patterns[blue_sum_range] += quantum_weight
            
            # 序列模式
            for j in range(len(sorted_reds) - 1):
                diff = sorted_reds[j+1] - sorted_reds[j]
                if diff <= 5:
                    self.red_sequences[(sorted_reds[j], diff)] += quantum_weight
    
    def deep_red_scoring(self):
        """深度红球评分系统"""
        scores = defaultdict(float)
        red_weights, _ = self.neural_weight_adaptation()
        
        # 第一层：量子频率分析
        if self.red_frequency:
            freq_values = list(self.red_frequency.values())
            freq_mean = np.mean(freq_values)
            freq_std = np.std(freq_values)
            
            for num in self.red_balls:
                freq = self.red_frequency[num]
                
                # 使用量子概率分布
                if freq_std > 0:
                    z_score = (freq - freq_mean) / freq_std
                    # 量子激活函数
                    quantum_activation = 1 / (1 + math.exp(-z_score))
                    freq_score = 70 + quantum_activation * 30
                else:
                    freq_score = 85
                
                scores[num] += freq_score * red_weights['frequency']
        
        # 第二层：深度位置分析
        position_quantum_weights = [0.12, 0.16, 0.20, 0.22, 0.18, 0.12]
        for pos in range(6):
            pos_freq = self.red_position_freq[pos]
            if pos_freq:
                pos_values = list(pos_freq.values())
                pos_mean = np.mean(pos_values)
                pos_std = np.std(pos_values)
                
                for num in self.red_balls:
                    pos_count = pos_freq[num]
                    
                    if pos_std > 0:
                        pos_z = (pos_count - pos_mean) / pos_std
                        pos_activation = math.tanh(pos_z) * 0.5 + 0.5
                        pos_score = 75 + pos_activation * 25
                    else:
                        pos_score = 85
                    
                    scores[num] += pos_score * red_weights['position'] * position_quantum_weights[pos]
        
        # 第三层：预测性间隔分析
        for num in self.red_balls:
            intervals = self.red_intervals[num]
            if len(intervals) >= 3:
                # 使用指数移动平均预测
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                
                # 指数权重
                exp_weights = [math.exp(-0.1 * i) for i in range(len(interval_diffs))]
                exp_weights.reverse()
                exp_weights = np.array(exp_weights) / sum(exp_weights)
                
                predicted_interval = np.average(interval_diffs, weights=exp_weights)
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 深度评分函数
                interval_ratio = last_interval / (predicted_interval + 1e-6)
                interval_score = 70 + 30 / (1 + math.exp(-3 * (interval_ratio - 1)))
                
                scores[num] += interval_score * red_weights['intervals']
            else:
                scores[num] += 80 * red_weights['intervals']
        
        # 第四层：多尺度热度分析
        time_scales = [3, 7, 12, 20, 35]
        scale_weights = [0.35, 0.25, 0.2, 0.15, 0.05]
        
        for scale, s_weight in zip(time_scales, scale_weights):
            recent_data = self.data[-scale:] if len(self.data) >= scale else self.data
            hot_counter = Counter()
            
            for draw in recent_data:
                for num in map(int, draw['number'].split()):
                    hot_counter[num] += 1
            
            if hot_counter:
                expected_freq = len(recent_data) * 6 / 33
                
                for num in self.red_balls:
                    hot_count = hot_counter[num]
                    
                    # 深度热度函数
                    if hot_count == 0:
                        hot_score = 95
                    else:
                        heat_ratio = hot_count / expected_freq
                        hot_score = 85 - 15 * math.tanh(heat_ratio - 1)
                    
                    scores[num] += hot_score * red_weights['hot_trend'] * s_weight
        
        # 第五层：智能分布优化
        for num in self.red_balls:
            # 多维分布分析
            region_score = 80
            if 1 <= num <= 11:
                region_score = 82
            elif 12 <= num <= 22:
                region_score = 88
            else:
                region_score = 80
            
            # 数字特征分析
            digit_features = {
                'is_prime': num in [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31],
                'is_fibonacci': num in [1, 2, 3, 5, 8, 13, 21],
                'tail_digit': num % 10,
                'digit_sum': sum(int(d) for d in str(num))
            }
            
            feature_score = 0
            if digit_features['is_prime']:
                feature_score += 3
            if digit_features['is_fibonacci']:
                feature_score += 2
            if digit_features['tail_digit'] in [1, 3, 7, 9]:
                feature_score += 2
            if digit_features['digit_sum'] % 3 == 0:
                feature_score += 1
            
            total_dist_score = region_score + feature_score
            scores[num] += total_dist_score * red_weights['distribution']
        
        # 第六层：深度组合分析
        combo_influence = defaultdict(float)
        
        if self.red_pairs:
            top_pairs = sorted(self.red_pairs.items(), key=lambda x: x[1], reverse=True)[:20]
            for pair, weight in top_pairs:
                for num in pair:
                    combo_influence[num] += weight * 0.6
        
        if self.red_triplets:
            top_triplets = sorted(self.red_triplets.items(), key=lambda x: x[1], reverse=True)[:15]
            for triplet, weight in top_triplets:
                for num in triplet:
                    combo_influence[num] += weight * 0.4
        
        if combo_influence:
            max_combo = max(combo_influence.values())
            for num in self.red_balls:
                combo_score = 75 + (combo_influence[num] / max_combo) * 25
                scores[num] += combo_score * red_weights['combination']
        
        # 第七层：序列模式和和值分析
        sequence_influence = defaultdict(float)
        if self.red_sequences:
            for (start_num, diff), weight in self.red_sequences.items():
                sequence_influence[start_num] += weight * 0.5
                if start_num + diff <= 33:
                    sequence_influence[start_num + diff] += weight * 0.5
        
        # 和值模式影响
        if self.red_sum_patterns:
            recent_sums = list(self.red_sum_patterns.keys())
            target_sum_range = max(recent_sums, key=lambda x: self.red_sum_patterns[x])
            target_sum = target_sum_range * 10 + 50  # 目标和值中心
            
            for num in self.red_balls:
                # 基于目标和值的评分
                sum_contribution = abs(num - target_sum / 6)
                sum_score = 85 - sum_contribution * 0.5
                sum_score = max(70, min(95, sum_score))
                
                seq_score = 75 + (sequence_influence[num] / max(sequence_influence.values(), default=1)) * 25
                
                combined_score = (seq_score + sum_score) / 2
                scores[num] += combined_score * (red_weights['sequence'] + red_weights['sum_pattern'])
        
        return scores
    
    def deep_blue_scoring(self):
        """深度蓝球评分系统"""
        scores = defaultdict(float)
        _, blue_weights = self.neural_weight_adaptation()
        
        # 第一层：贝叶斯频率分析
        if self.blue_frequency:
            total_weight = sum(self.blue_frequency.values())
            alpha = 1.0  # 先验参数
            
            for num in self.blue_balls:
                freq = self.blue_frequency[num]
                # 贝叶斯后验概率
                posterior = (freq + alpha) / (total_weight + 16 * alpha)
                prior = 1.0 / 16
                
                # 信息增益评分
                if posterior > prior * 1.3:
                    freq_score = 70
                elif posterior > prior * 0.9:
                    freq_score = 85
                else:
                    freq_score = 95
                
                scores[num] += freq_score * blue_weights['frequency']
        
        # 第二层：高级间隔预测
        blue_intervals_dict = defaultdict(list)
        for i, (pos, blue_num) in enumerate(self.blue_intervals):
            blue_intervals_dict[blue_num].append(pos)
        
        for num in self.blue_balls:
            intervals = blue_intervals_dict[num]
            if len(intervals) >= 3:
                interval_diffs = [intervals[i] - intervals[i-1] for i in range(1, len(intervals))]
                
                # 使用ARIMA思想的预测
                if len(interval_diffs) >= 3:
                    # 一阶差分
                    first_diff = [interval_diffs[i] - interval_diffs[i-1] for i in range(1, len(interval_diffs))]
                    
                    # 预测下一个间隔
                    if first_diff:
                        trend = np.mean(first_diff[-3:]) if len(first_diff) >= 3 else np.mean(first_diff)
                        predicted_interval = interval_diffs[-1] + trend
                    else:
                        predicted_interval = np.mean(interval_diffs)
                else:
                    predicted_interval = np.mean(interval_diffs)
                
                last_interval = len(self.data) - intervals[-1] if intervals else 999
                
                # 深度间隔评分
                if predicted_interval > 0:
                    interval_ratio = last_interval / predicted_interval
                    interval_score = 70 + 30 * (1 / (1 + math.exp(-4 * (interval_ratio - 1))))
                else:
                    interval_score = 85
                
                scores[num] += interval_score * blue_weights['intervals']
            else:
                scores[num] += 85 * blue_weights['intervals']
        
        # 第三层：多时间窗口热度
        for window, weight in [(5, 0.4), (12, 0.35), (25, 0.25)]:
            recent_data = self.data[-window:] if len(self.data) >= window else self.data
            hot_blues = [int(draw['refernumber']) for draw in recent_data]
            hot_count = Counter(hot_blues)
            
            expected = len(recent_data) / 16
            
            for num in self.blue_balls:
                hot_val = hot_count[num]
                
                # 深度热度函数
                if hot_val == 0:
                    hot_score = 95
                elif hot_val >= expected * 2.5:
                    hot_score = 65
                elif hot_val >= expected * 1.5:
                    hot_score = 75
                elif hot_val >= expected * 0.5:
                    hot_score = 85
                else:
                    hot_score = 90
                
                scores[num] += hot_score * blue_weights['hot_trend'] * weight
        
        # 第四层：红蓝球关联分析
        if len(self.data) >= 50:
            recent_data = self.data[-50:]
            red_blue_correlation = defaultdict(float)
            
            for draw in recent_data:
                red_nums = list(map(int, draw['number'].split()))
                blue_num = int(draw['refernumber'])
                red_sum = sum(red_nums)
                
                # 和值关联
                sum_mod = red_sum % 16
                if sum_mod == blue_num:
                    red_blue_correlation[blue_num] += 2.0
                elif abs(sum_mod - blue_num) <= 2:
                    red_blue_correlation[blue_num] += 1.0
                
                # 尾数关联
                red_tails = [num % 10 for num in red_nums]
                blue_tail = blue_num % 10
                if blue_tail in red_tails:
                    red_blue_correlation[blue_num] += 1.5
            
            if red_blue_correlation:
                max_corr = max(red_blue_correlation.values())
                for num in self.blue_balls:
                    corr_score = 75 + (red_blue_correlation[num] / max_corr) * 25
                    scores[num] += corr_score * blue_weights['sum_correlation']
        
        # 第五层：位置关联分析
        if len(self.data) >= 30:
            recent_data = self.data[-30:]
            position_correlation = defaultdict(float)
            
            for draw in recent_data:
                red_nums = list(map(int, draw['number'].split()))
                blue_num = int(draw['refernumber'])
                
                # 检查蓝球是否与红球位置相关
                for pos, red_num in enumerate(red_nums):
                    if abs(red_num - blue_num) <= 3:
                        position_correlation[blue_num] += 1.0 / (pos + 1)
            
            if position_correlation:
                max_pos_corr = max(position_correlation.values())
                for num in self.blue_balls:
                    pos_corr_score = 75 + (position_correlation[num] / max_pos_corr) * 25
                    scores[num] += pos_corr_score * blue_weights['position_correlation']
        
        return scores
    
    def reinforcement_red_selection(self, scores):
        """强化学习红球选择"""
        strategies = {
            'top_score': lambda: self._strategy_top_score(scores),
            'balanced_region': lambda: self._strategy_balanced_region(scores),
            'odd_even_balance': lambda: self._strategy_odd_even_balance(scores),
            'quantum_random': lambda: self._strategy_quantum_random(scores)
        }
        
        # 计算策略期望奖励
        strategy_values = {}
        for strategy_name in strategies:
            if self.strategy_counts[strategy_name] > 0:
                strategy_values[strategy_name] = self.strategy_rewards[strategy_name] / self.strategy_counts[strategy_name]
            else:
                strategy_values[strategy_name] = 0.5  # 初始值
        
        # ε-贪婪策略选择
        if random.random() < self.exploration_rate:
            selected_strategy = random.choice(list(strategies.keys()))
        else:
            selected_strategy = max(strategy_values, key=strategy_values.get)
        
        # 执行选择的策略
        selected_reds = strategies[selected_strategy]()
        
        return selected_reds, selected_strategy
    
    def _strategy_top_score(self, scores):
        """策略1：智能得分排序"""
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        top_candidates = [num for num, _ in sorted_scores[:15]]
        
        # 使用softmax选择
        top_scores = [score for _, score in sorted_scores[:15]]
        exp_scores = np.exp(np.array(top_scores) / 20)
        probabilities = exp_scores / np.sum(exp_scores)
        
        selected = np.random.choice(top_candidates, size=7, replace=False, p=probabilities)
        return sorted(selected)
    
    def _strategy_balanced_region(self, scores):
        """策略2：区间平衡"""
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        top_candidates = [num for num, _ in sorted_scores[:20]]
        
        small_nums = [num for num in top_candidates if 1 <= num <= 11]
        mid_nums = [num for num in top_candidates if 12 <= num <= 22]
        big_nums = [num for num in top_candidates if 23 <= num <= 33]
        
        selected = []
        # 动态区间分配
        if len(small_nums) >= 3:
            selected.extend(random.sample(small_nums[:8], 3))
        else:
            selected.extend(small_nums[:2] if len(small_nums) >= 2 else small_nums)
        
        if len(mid_nums) >= 2:
            selected.extend(random.sample(mid_nums[:8], 2))
        else:
            selected.extend(mid_nums)
        
        if len(big_nums) >= 2:
            selected.extend(random.sample(big_nums[:8], 2))
        else:
            selected.extend(big_nums)
        
        # 补充到7个
        while len(selected) < 7:
            remaining = [num for num, _ in sorted_scores[:25] if num not in selected]
            if remaining:
                selected.append(random.choice(remaining[:5]))
            else:
                break
        
        return sorted(selected[:7])
    
    def _strategy_odd_even_balance(self, scores):
        """策略3：奇偶平衡"""
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        odd_candidates = [num for num, _ in sorted_scores if num % 2 == 1][:12]
        even_candidates = [num for num, _ in sorted_scores if num % 2 == 0][:12]
        
        selected = []
        selected.extend(random.sample(odd_candidates, min(4, len(odd_candidates))))
        selected.extend(random.sample(even_candidates, min(3, len(even_candidates))))
        
        return sorted(selected)
    
    def _strategy_quantum_random(self, scores):
        """策略4：量子随机"""
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 量子权重分布
        candidates = [num for num, _ in sorted_scores[:18]]
        weights = []
        
        for i, (num, score) in enumerate(sorted_scores[:18]):
            # 量子概率分布
            quantum_weight = math.exp(-i * 0.1) * (1 + 0.1 * math.sin(num * math.pi / 16))
            weights.append(quantum_weight)
        
        weights = np.array(weights) / sum(weights)
        selected = np.random.choice(candidates, size=7, replace=False, p=weights)
        
        return sorted(selected)
    
    def advanced_blue_selection(self, scores):
        """高级蓝球选择"""
        # 获取前10个候选
        top_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # 多层选择机制
        candidate_nums = [num for num, _ in top_candidates]
        raw_scores = [score for _, score in top_candidates]
        
        # 使用温度调节的softmax
        temperature = 15
        exp_scores = np.exp(np.array(raw_scores) / temperature)
        probabilities = exp_scores / np.sum(exp_scores)
        
        # 加权随机选择
        selected_blue = np.random.choice(candidate_nums, p=probabilities)
        return int(selected_blue)
    
    def predict(self, train_size=400):
        """超级优化预测方法"""
        if len(self.data) < train_size:
            train_size = len(self.data) - 1
        
        # 量子启发数据分析
        self.quantum_inspired_analysis(train_size)
        
        # 深度评分计算
        red_scores = self.deep_red_scoring()
        blue_scores = self.deep_blue_scoring()
        
        # 强化学习选择
        red_prediction, strategy_used = self.reinforcement_red_selection(red_scores)
        blue_prediction = self.advanced_blue_selection(blue_scores)
        
        return red_prediction, blue_prediction, strategy_used
    
    def update_strategy_reward(self, strategy_name, red_hits, blue_hit):
        """更新策略奖励"""
        # 计算奖励
        red_reward = red_hits / 7.0
        blue_reward = blue_hit * 1.0
        total_reward = red_reward * 0.7 + blue_reward * 0.3
        
        # 更新策略统计
        self.strategy_rewards[strategy_name] += total_reward
        self.strategy_counts[strategy_name] += 1
    
    def update_performance(self, red_hits, blue_hit):
        """更新性能历史"""
        self.performance_history.append({
            'red_hits': red_hits,
            'blue_hit': blue_hit,
            'timestamp': len(self.performance_history)
        })
        
        # 保持最近100期的记录
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-100:]

def super_optimized_seven_red_balls_backtest(filename, test_periods=500):
    """超级优化7红球策略回测"""
    predictor = SuperOptimizedSevenRedBallsPredictor()
    predictor.load_data(filename)
    
    if len(predictor.data) < test_periods + 50:
        test_periods = len(predictor.data) - 50
        print(f"调整测试期数为: {test_periods}")
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    best_predictions = []
    strategy_performance = defaultdict(lambda: {'hits': 0, 'count': 0})
    
    # 7红球命中统计
    red_6_hits = 0
    red_5_hits = 0
    red_4_hits = 0
    
    print(f"开始超级优化7红球策略回测，测试 {test_periods} 期...")
    print(f"策略：神经网络权重+量子分析+强化学习+深度蓝球模型")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_data = predictor.data[:-(test_periods-i)]
        test_data = predictor.data[-(test_periods-i)]
        
        # 临时设置训练数据
        original_data = predictor.data
        predictor.data = train_data
        
        try:
            # 进行预测
            red_pred, blue_pred, strategy_used = predictor.predict()
            
            # 获取实际结果
            actual_red = list(map(int, test_data['number'].split()))
            actual_blue = int(test_data['refernumber'])
            
            # 计算命中
            red_hits = len(set(red_pred) & set(actual_red))
            blue_hit = 1 if blue_pred == actual_blue else 0
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            # 更新性能和策略奖励
            predictor.update_performance(red_hits, blue_hit)
            predictor.update_strategy_reward(strategy_used, red_hits, blue_hit)
            
            # 策略性能统计
            strategy_performance[strategy_used]['hits'] += red_hits
            strategy_performance[strategy_used]['count'] += 1
            
            # 统计特殊命中情况
            if red_hits >= 6:
                red_6_hits += 1
            elif red_hits >= 5:
                red_5_hits += 1
            elif red_hits >= 4:
                red_4_hits += 1
            
            result = {
                'period': test_data['issueno'],
                'red_hits': red_hits,
                'blue_hit': blue_hit,
                'red_pred': red_pred,
                'red_actual': actual_red,
                'blue_pred': blue_pred,
                'blue_actual': actual_blue,
                'strategy': strategy_used
            }
            results.append(result)
            
            # 记录优秀预测
            if red_hits >= 3 or blue_hit == 1:
                best_predictions.append(result)
            
            if (i + 1) % 50 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = (blue_hits_total / (i + 1)) * 100
                print(f"已完成 {i + 1}/{test_periods} 期 - 当前红球平均:{current_red_avg:.2f}/7, 蓝球命中率:{current_blue_rate:.1f}%")
                
        except Exception as e:
            print(f"第 {i+1} 期预测失败: {e}")
            continue
        finally:
            # 恢复原始数据
            predictor.data = original_data
    
    # 统计结果
    avg_red_hits = red_hits_total / test_periods
    blue_hit_rate = (blue_hits_total / test_periods) * 100
    
    # 红球命中分布
    red_hit_dist = Counter([r['red_hits'] for r in results])
    red_3plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 3) / test_periods * 100
    red_4plus_rate = sum(count for hits, count in red_hit_dist.items() if hits >= 4) / test_periods * 100
    
    # 目标达成度计算
    red_target = 1.5
    blue_target = 10.0
    
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    overall_achievement = (red_achievement + blue_achievement) / 2
    
    print("\n=== 超级优化7红球策略回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hits:.2f}/7 ({avg_red_hits/7*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits_total}/{test_periods})")
    print(f"红球命中≥3个的概率: {red_3plus_rate:.1f}%")
    print(f"红球命中≥4个的概率: {red_4plus_rate:.1f}%")
    
    print("\n=== 目标达成度分析 ===")
    print(f"红球目标: {red_target}/7, 实际: {avg_red_hits:.2f}/7, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    print(f"综合达成度: {overall_achievement:.1f}%")
    
    print("\n=== 强化学习策略性能 ===")
    for strategy, perf in strategy_performance.items():
        if perf['count'] > 0:
            avg_hits = perf['hits'] / perf['count']
            usage_rate = perf['count'] / test_periods * 100
            print(f"{strategy}: 平均命中{avg_hits:.2f}/7, 使用率{usage_rate:.1f}%")
    
    print("\n=== 红球命中分布 ===")
    for hits in sorted(red_hit_dist.keys()):
        count = red_hit_dist[hits]
        percentage = count / test_periods * 100
        print(f"命中{hits}个: {count}次 ({percentage:.1f}%)")
    
    print("\n=== 超级优化优势分析 ===")
    print(f"命中6个红球: {red_6_hits}次 ({red_6_hits/test_periods*100:.2f}%)")
    print(f"命中5个红球: {red_5_hits}次 ({red_5_hits/test_periods*100:.2f}%)")
    print(f"命中4个红球: {red_4_hits}次 ({red_4_hits/test_periods*100:.2f}%)")
    
    # 计算理论提升
    theoretical_6_balls = 6 * (6/33)
    theoretical_7_balls = 7 * (6/33)
    improvement = (theoretical_7_balls - theoretical_6_balls) / theoretical_6_balls * 100
    
    print(f"\n=== 理论vs实际分析 ===")
    print(f"选择6个红球理论期望: {theoretical_6_balls:.2f}个")
    print(f"选择7个红球理论期望: {theoretical_7_balls:.2f}个")
    print(f"理论提升幅度: {improvement:.1f}%")
    print(f"实际平均命中: {avg_red_hits:.2f}个")
    print(f"实际vs理论7球: {(avg_red_hits/theoretical_7_balls-1)*100:+.1f}%")
    
    print(f"\n=== 前15期最佳预测记录 ===")
    best_predictions.sort(key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
    for i, pred in enumerate(best_predictions[:15], 1):
        blue_symbol = "✓" if pred['blue_hit'] else "✗"
        print(f"{i:2d}. 期号:{pred['period']} 红球:{pred['red_hits']}/7 蓝球:{blue_symbol} 策略:{pred['strategy']}")
        print(f"    预测红球(7个): {pred['red_pred']}")
        print(f"    实际红球(6个): {pred['red_actual']}")
        print(f"    预测蓝球: {pred['blue_pred']}, 实际蓝球: {pred['blue_actual']}")
        print()
    
    print("=== 超级优化7红球策略总结 ===")
    print("🚀 超级优化亮点:")
    print("  - 神经网络启发的自适应权重系统")
    print("  - 量子启发的特征融合和叠加态分析")
    print("  - 强化学习的策略选择和奖励机制")
    print("  - 深度蓝球预测模型(贝叶斯+ARIMA+关联分析)")
    print("  - 多模态数据融合和预测性分析")
    
    print("\n💰 成本效益:")
    print("  - 需要购买7注(7选6的组合)")
    print("  - 成本增加7倍")
    print(f"  - 平均命中提升: {(avg_red_hits/theoretical_6_balls-1)*100:+.1f}%")
    
    if overall_achievement >= 85:
        print("\n🎯 目标达成: 卓越! 超级优化策略表现优异")
    elif overall_achievement >= 75:
        print("\n📈 目标达成: 优秀，策略效果显著")
    elif overall_achievement >= 65:
        print("\n🔧 目标达成: 良好，接近预期目标")
    else:
        print("\n⚠️ 需要进一步调优参数")
    
    return {
        'avg_red_hits': avg_red_hits,
        'blue_hit_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement,
        'overall_achievement': overall_achievement,
        'strategy_performance': dict(strategy_performance),
        'best_predictions': best_predictions[:10]
    }

if __name__ == "__main__":
    # 运行超级优化7红球策略回测
    result = super_optimized_seven_red_balls_backtest('ssq_data.json', 500)
    print(f"\n🏆 最终结果: 红球{result['avg_red_hits']:.2f}/7, 蓝球{result['blue_hit_rate']:.1f}%, 综合达成度{result['overall_achievement']:.1f}%")