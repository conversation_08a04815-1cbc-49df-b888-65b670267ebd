# Qwen Code 全局环境变量配置脚本
# 此脚本将永久设置系统级环境变量，使qwen命令在任何目录都能正常工作

Write-Host "=== Qwen Code 全局环境变量配置脚本 ===" -ForegroundColor Cyan
Write-Host "此脚本将永久设置系统级环境变量" -ForegroundColor Yellow
Write-Host ""

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  警告：需要管理员权限来设置系统级环境变量" -ForegroundColor Red
    Write-Host "正在尝试以管理员身份重新运行..." -ForegroundColor Yellow
    
    try {
        Start-Process PowerShell -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
        exit
    }
    catch {
        Write-Host "❌ 无法获取管理员权限，将设置用户级环境变量" -ForegroundColor Red
        Write-Host "用户级环境变量只在当前用户下生效" -ForegroundColor Yellow
        $scope = "User"
    }
} else {
    Write-Host "✓ 检测到管理员权限，将设置系统级环境变量" -ForegroundColor Green
    $scope = "Machine"
}

Write-Host ""

# 环境变量配置
$envVars = @{
    "OPENAI_API_KEY" = "sk-bb0127d0f09c412096ce6af3144e5e39"
    "OPENAI_BASE_URL" = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    "OPENAI_MODEL" = "qwen-coder-plus"
}

Write-Host "正在设置环境变量..." -ForegroundColor Green

foreach ($var in $envVars.GetEnumerator()) {
    try {
        [Environment]::SetEnvironmentVariable($var.Key, $var.Value, $scope)
        Write-Host "✓ $($var.Key) = $($var.Value)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ 设置 $($var.Key) 失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "✓ 环境变量设置完成！" -ForegroundColor Green

# 验证设置
Write-Host "验证环境变量设置..." -ForegroundColor Yellow
foreach ($var in $envVars.Keys) {
    $value = [Environment]::GetEnvironmentVariable($var, $scope)
    if ($value) {
        Write-Host "✓ $var = $value" -ForegroundColor Gray
    } else {
        Write-Host "❌ $var 未设置" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== 重要提示 ===" -ForegroundColor Cyan
if ($scope -eq "Machine") {
    Write-Host "✓ 系统级环境变量已设置，所有用户都可以使用" -ForegroundColor Green
    Write-Host "✓ 新打开的终端将自动加载这些环境变量" -ForegroundColor Green
} else {
    Write-Host "✓ 用户级环境变量已设置，当前用户可以使用" -ForegroundColor Green
    Write-Host "✓ 新打开的终端将自动加载这些环境变量" -ForegroundColor Green
}

Write-Host "⚠️  请重新打开终端或重启系统以确保环境变量生效" -ForegroundColor Yellow
Write-Host ""

# 测试当前会话
Write-Host "测试当前会话中的qwen命令..." -ForegroundColor Green

# 设置当前会话的环境变量
foreach ($var in $envVars.GetEnumerator()) {
    Set-Item -Path "Env:$($var.Key)" -Value $var.Value
}

try {
    $version = qwen --version 2>$null
    if ($version) {
        Write-Host "✓ qwen 命令测试成功！版本：$version" -ForegroundColor Green
    } else {
        Write-Host "❌ qwen 命令测试失败" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ qwen 命令不可用：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "配置完成！现在qwen命令应该在任何目录都能正常工作了！" -ForegroundColor Green
Write-Host "使用方法：" -ForegroundColor Cyan
Write-Host "  qwen --version          # 查看版本" -ForegroundColor Gray
Write-Host "  echo \"问题\" | qwen       # 管道输入" -ForegroundColor Gray
Write-Host "  qwen                    # 交互模式" -ForegroundColor Gray

Read-Host "按任意键退出"