# Qwen Code 配置文件

## 环境变量配置

### 必需的环境变量
```powershell
# 设置用户级环境变量
[System.Environment]::SetEnvironmentVariable('OPENAI_API_KEY', 'sk-bb0127d0f09c412096ce6af3144e5e39', 'User')
[System.Environment]::SetEnvironmentVariable('OPENAI_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1', 'User')
[System.Environment]::SetEnvironmentVariable('OPENAI_MODEL', 'qwen-coder-plus', 'User')

# 同时设置当前会话的环境变量（立即生效）
$env:OPENAI_API_KEY = 'sk-bb0127d0f09c412096ce6af3144e5e39'
$env:OPENAI_BASE_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
$env:OPENAI_MODEL = 'qwen-coder-plus'
```

### 配置说明
- **OPENAI_API_KEY**: 阿里云DashScope API密钥
- **OPENAI_BASE_URL**: 阿里云兼容模式API端点
- **OPENAI_MODEL**: 使用的模型名称（qwen-coder-plus）

### 快速配置
```powershell
# 运行配置脚本（推荐）
.\setup_qwen_env.ps1
```

### 验证配置
```powershell
# 验证环境变量
Write-Host "OPENAI_API_KEY: $env:OPENAI_API_KEY"
Write-Host "OPENAI_BASE_URL: $env:OPENAI_BASE_URL"
Write-Host "OPENAI_MODEL: $env:OPENAI_MODEL"

# 测试qwen命令
qwen --version
echo "测试" | qwen
```

## 系统配置

### 连接配置
```json
{
  "timeout": 180000,
  "retries": 3,
  "streaming": false,
  "model": "qwen-coder-plus",
  "maxTokens": 4096
}
```

### 超时设置
- 增加流式设置超时时间到180秒
- 禁用流式模式以提高稳定性
- 减少输入长度以避免超时
- 使用分批处理模式

### 网络配置
- 检查网络连接和防火墙设置
- 确保API端点可访问
- 优化网络连接稳定性
- 使用本地缓存减少网络依赖

### 使用建议
1. 保持输入简洁明了
2. 避免过长的代码片段
3. 分批处理复杂任务
4. 检查网络连接状态

### 故障排除

#### 常见问题及解决方案

**1. 流式设置超时错误 (Streaming setup timeout)**
```
✕ [API Error: Streaming setup timeout after 46s. Try reducing input length or increasing timeout in config.
```
**根本原因：** 缺失关键环境变量 OPENAI_BASE_URL 和 OPENAI_MODEL

**解决方案：**
- 运行配置脚本：`./setup_qwen_env.ps1`
- 或手动设置所有必需的环境变量（特别是 OPENAI_BASE_URL 和 OPENAI_MODEL）
- 验证环境变量：`$env:OPENAI_API_KEY`, `$env:OPENAI_BASE_URL`, `$env:OPENAI_MODEL`
- 重启终端或重新登录使环境变量生效
- 检查API密钥是否有效

**2. 环境变量未生效**
- 使用 `[System.Environment]::SetEnvironmentVariable()` 设置用户级环境变量
- 同时设置当前会话环境变量以立即生效
- 验证设置：`$env:OPENAI_API_KEY`, `$env:OPENAI_BASE_URL`, `$env:OPENAI_MODEL`

**3. 其他故障排除**
- 如果遇到超时错误，请重试或减少输入长度
- 检查防火墙和网络设置
- 考虑使用本地模式或其他API端点
- 确认qwen版本：`qwen --version`

## 项目特定配置

### 双色球预测项目
- 专注于算法优化和数据分析
- 使用本地数据进行预测，减少API依赖
- 优先处理核心功能实现

### 交互模式
- 使用中文进行交流
- 提供详细的技术说明
- 关注代码质量和性能优化

## 版本信息
- Qwen Code 版本：0.0.1-alpha.8
- 最后更新：$(Get-Date -Format 'yyyy-MM-dd')
- 配置状态：✅ 正常工作