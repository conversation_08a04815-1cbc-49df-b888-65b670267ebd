# 🎯 改进马尔科夫双色球预测算法使用指南

## 📋 快速开始

### 1. 运行预测
```bash
python improved_markov_algorithm.py
```

### 2. 查看结果
算法会输出5个预测组合，按得分排序：
```
🏆 预测组合排行榜:
第1名 (得分: 0.808)
🔴 红球: 04 06 07 09 14 32 33
🔵 蓝球: 10
📋 组合: 04 06 07 09 14 32 33 + 10
🎯 格式化: 00CP#01#04060709143233*10#1
```

## 🔍 结果解读

### 得分说明
- **0.8-1.0**: 高置信度，算法认为中奖概率较高
- **0.6-0.8**: 中等置信度，具有一定参考价值
- **0.4-0.6**: 低置信度，可作为备选方案
- **0.0-0.4**: 极低置信度，不建议选择

### 格式化输出
`00CP#01#04060709143233*10#1` 格式说明：
- `00CP#01#`: 固定前缀
- `04060709143233`: 7个红球号码（两位数格式）
- `*10#1`: 蓝球号码和固定后缀

## 📊 算法特点

### 1. 重复概率分析
- 统计每期与上期号码的重复情况
- 红球平均重复率：17.63%
- 蓝球平均重复率：7.01%
- 根据重复概率调整预测权重

### 2. 马尔科夫链权重
- 最近一期权重最大（权重=1.0）
- 权重按时间距离递减（第2期权重=0.5，第3期权重=0.33...）
- 基于状态转移概率计算号码出现可能性

### 3. 多样化组合
- 每次运行生成5个不同组合
- 使用时间戳种子确保结果多样性
- 智能评分排序，提供最优选择

## 🎮 使用策略

### 推荐策略1：高置信度选择
```python
# 选择得分最高的组合
选择第1名组合进行投注
```

### 推荐策略2：多组合投注
```python
# 选择前3名组合
分别投注前3个高分组合，增加中奖概率
```

### 推荐策略3：混合策略
```python
# 结合个人喜好
从5个组合中选择包含幸运号码的组合
```

## 📁 文件说明

### 输入文件
- `ssq_data.json`: 历史开奖数据（1000期）
- 算法会自动加载和分析历史数据

### 输出文件
- `improved_markov_predictions.json`: 预测记录
- 每次预测都会追加保存到此文件

### 预测记录格式
```json
{
  "timestamp": "2025-07-22 08:40:58",
  "algorithm": "ImprovedMarkovPredictor",
  "prediction_result": {
    "best_combination": {
      "red": [4, 6, 7, 9, 14, 32, 33],
      "blue": 10,
      "score": 0.808,
      "rank": 1
    },
    "all_combinations": [...],
    "analysis": {
      "total_periods": 1000,
      "latest_period": "2025082",
      "repeat_stats": {...}
    }
  }
}
```

## ⚙️ 高级设置

### 修改组合数量
```python
# 在 main() 函数中修改
result = predictor.predict(num_combinations=10)  # 生成10个组合
```

### 调整权重衰减
```python
# 在 build_markov_weights() 函数中修改
period_weight = 1.0 / (j + 1)  # 线性衰减
# 或
period_weight = 0.9 ** j      # 指数衰减
```

### 修改历史期数
```python
# 在 build_markov_weights() 函数中修改
recent_periods = min(100, len(self.data))  # 使用最近100期
```

## 🔧 故障排除

### 常见问题

1. **数据文件不存在**
   ```
   错误: FileNotFoundError: ssq_data.json
   解决: 确保 ssq_data.json 文件在当前目录
   ```

2. **数据格式错误**
   ```
   错误: JSON decode error
   解决: 检查 ssq_data.json 文件格式是否正确
   ```

3. **随机种子错误**
   ```
   错误: Seed must be between 0 and 2**32 - 1
   解决: 已在代码中修复，使用模运算限制种子范围
   ```

### 性能优化

1. **减少历史数据量**
   ```python
   # 如果数据过多导致运行缓慢
   self.data = self.data[-500:]  # 只使用最近500期
   ```

2. **减少组合数量**
   ```python
   # 如果需要快速预测
   result = predictor.predict(num_combinations=3)
   ```

## 📈 效果验证

### 验证方法
1. 多次运行算法，观察结果差异
2. 记录预测结果，与实际开奖对比
3. 统计不同得分区间的命中率

### 预期效果
- ✅ 每次运行结果不同
- ✅ 高分组合理论上命中率更高
- ✅ 包含重复号码的概率符合统计规律

## ⚠️ 重要提醒

1. **理性购彩**: 本算法仅供参考，不保证中奖
2. **风险控制**: 请合理控制投注金额
3. **数据更新**: 建议定期更新历史数据
4. **多样选择**: 可结合其他预测方法
5. **娱乐为主**: 购彩应以娱乐为目的，不可沉迷

---

**祝您好运！** 🍀

如有问题，请查看 `算法改进对比报告.md` 了解更多技术细节。