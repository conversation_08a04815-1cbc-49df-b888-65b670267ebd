#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现实高性能双色球预测算法
基于深度数据挖掘和统计学原理
目标：在500期回测中尽可能接近红球3/6和蓝球31.25%的目标
"""

import json
import numpy as np
from collections import defaultdict, Counter
import random
from datetime import datetime
import itertools
from scipy import stats
import math

class RealisticHighPerformancePredictor:
    def __init__(self, data):
        self.data = data
        self.red_balls = []
        self.blue_balls = []
        self.periods = []
        self.parse_data()
        
    def parse_data(self):
        """解析历史数据"""
        for item in self.data:
            red_str = item['number']
            blue_str = item['refernumber']
            
            red_nums = [int(x) for x in red_str.split()]
            self.red_balls.append(sorted(red_nums))
            self.blue_balls.append(int(blue_str))
            self.periods.append(item['issueno'])
    
    def comprehensive_statistical_analysis(self):
        """全面统计分析"""
        analysis = {
            'red_frequency': self.analyze_red_frequency(),
            'blue_frequency': self.analyze_blue_frequency(),
            'position_analysis': self.analyze_positions(),
            'interval_analysis': self.analyze_intervals(),
            'combination_analysis': self.analyze_combinations(),
            'distribution_analysis': self.analyze_distributions(),
            'correlation_analysis': self.analyze_correlations(),
            'trend_analysis': self.analyze_trends()
        }
        return analysis
    
    def analyze_red_frequency(self):
        """红球频率分析"""
        # 全期频率
        total_freq = Counter()
        for combo in self.red_balls:
            for num in combo:
                total_freq[num] += 1
        
        # 多时间窗口频率
        windows = [10, 20, 30, 50, 100]
        window_freqs = {}
        
        for window in windows:
            freq = Counter()
            for i in range(min(window, len(self.red_balls))):
                for num in self.red_balls[-(i+1)]:
                    freq[num] += 1
            window_freqs[window] = freq
        
        # 计算热度指数
        heat_index = {}
        for num in range(1, 34):
            recent_freq = window_freqs[20][num] if 20 in window_freqs else 0
            total_avg = total_freq[num] / len(self.red_balls) * 20
            heat_index[num] = recent_freq / total_avg if total_avg > 0 else 0
        
        return {
            'total': total_freq,
            'windows': window_freqs,
            'heat_index': heat_index
        }
    
    def analyze_blue_frequency(self):
        """蓝球频率分析"""
        total_freq = Counter(self.blue_balls)
        
        # 近期频率
        windows = [10, 20, 30, 50]
        window_freqs = {}
        
        for window in windows:
            recent_blues = self.blue_balls[-window:] if len(self.blue_balls) >= window else self.blue_balls
            window_freqs[window] = Counter(recent_blues)
        
        # 蓝球热度
        heat_index = {}
        for num in range(1, 17):
            recent_freq = window_freqs[20][num] if 20 in window_freqs and num in window_freqs[20] else 0
            total_avg = total_freq[num] / len(self.blue_balls) * 20
            heat_index[num] = recent_freq / total_avg if total_avg > 0 else 0
        
        return {
            'total': total_freq,
            'windows': window_freqs,
            'heat_index': heat_index
        }
    
    def analyze_positions(self):
        """位置分析"""
        position_freq = [Counter() for _ in range(6)]
        position_ranges = [[] for _ in range(6)]
        
        for combo in self.red_balls:
            for pos, num in enumerate(combo):
                position_freq[pos][num] += 1
                position_ranges[pos].append(num)
        
        # 计算每个位置的统计特征
        position_stats = []
        for pos in range(6):
            if position_ranges[pos]:
                stats_dict = {
                    'mean': np.mean(position_ranges[pos]),
                    'std': np.std(position_ranges[pos]),
                    'min': min(position_ranges[pos]),
                    'max': max(position_ranges[pos]),
                    'median': np.median(position_ranges[pos])
                }
                position_stats.append(stats_dict)
            else:
                position_stats.append({})
        
        return {
            'frequency': position_freq,
            'stats': position_stats
        }
    
    def analyze_intervals(self):
        """间隔分析"""
        red_intervals = defaultdict(list)
        blue_intervals = defaultdict(list)
        
        # 红球间隔
        for num in range(1, 34):
            last_appear = -1
            for i, combo in enumerate(self.red_balls):
                if num in combo:
                    if last_appear != -1:
                        red_intervals[num].append(i - last_appear)
                    last_appear = i
        
        # 蓝球间隔
        for num in range(1, 17):
            last_appear = -1
            for i, blue in enumerate(self.blue_balls):
                if blue == num:
                    if last_appear != -1:
                        blue_intervals[num].append(i - last_appear)
                    last_appear = i
        
        # 计算当前间隔和期望间隔
        red_current_intervals = {}
        blue_current_intervals = {}
        red_expected_intervals = {}
        blue_expected_intervals = {}
        
        for num in range(1, 34):
            # 当前间隔
            for i, combo in enumerate(reversed(self.red_balls)):
                if num in combo:
                    red_current_intervals[num] = i
                    break
            else:
                red_current_intervals[num] = len(self.red_balls)
            
            # 期望间隔
            if red_intervals[num]:
                red_expected_intervals[num] = np.mean(red_intervals[num])
            else:
                red_expected_intervals[num] = len(self.red_balls) / 6  # 理论期望
        
        for num in range(1, 17):
            # 当前间隔
            for i, blue in enumerate(reversed(self.blue_balls)):
                if blue == num:
                    blue_current_intervals[num] = i
                    break
            else:
                blue_current_intervals[num] = len(self.blue_balls)
            
            # 期望间隔
            if blue_intervals[num]:
                blue_expected_intervals[num] = np.mean(blue_intervals[num])
            else:
                blue_expected_intervals[num] = len(self.blue_balls) / 16  # 理论期望
        
        return {
            'red_intervals': red_intervals,
            'blue_intervals': blue_intervals,
            'red_current': red_current_intervals,
            'blue_current': blue_current_intervals,
            'red_expected': red_expected_intervals,
            'blue_expected': blue_expected_intervals
        }
    
    def analyze_combinations(self):
        """组合分析"""
        # 两两组合频率
        pair_freq = Counter()
        for combo in self.red_balls:
            for pair in itertools.combinations(combo, 2):
                pair_freq[pair] += 1
        
        # 三三组合频率
        triple_freq = Counter()
        for combo in self.red_balls:
            for triple in itertools.combinations(combo, 3):
                triple_freq[triple] += 1
        
        # 和值分析
        sum_freq = Counter([sum(combo) for combo in self.red_balls])
        
        # 跨度分析
        span_freq = Counter([max(combo) - min(combo) for combo in self.red_balls])
        
        # 奇偶分析
        odd_even_freq = Counter()
        for combo in self.red_balls:
            odd_count = sum(1 for x in combo if x % 2 == 1)
            odd_even_freq[odd_count] += 1
        
        return {
            'pairs': pair_freq,
            'triples': triple_freq,
            'sums': sum_freq,
            'spans': span_freq,
            'odd_even': odd_even_freq
        }
    
    def analyze_distributions(self):
        """分布分析"""
        # 区间分布
        zone_distributions = []
        for combo in self.red_balls:
            zone1 = sum(1 for x in combo if 1 <= x <= 11)
            zone2 = sum(1 for x in combo if 12 <= x <= 22)
            zone3 = sum(1 for x in combo if 23 <= x <= 33)
            zone_distributions.append((zone1, zone2, zone3))
        
        zone_freq = Counter(zone_distributions)
        
        # 尾数分析
        tail_freq = Counter()
        for combo in self.red_balls:
            for num in combo:
                tail_freq[num % 10] += 1
        
        return {
            'zones': zone_freq,
            'tails': tail_freq
        }
    
    def analyze_correlations(self):
        """相关性分析"""
        # 红球与蓝球关系
        red_blue_correlations = defaultdict(list)
        
        for combo, blue in zip(self.red_balls, self.blue_balls):
            red_sum = sum(combo)
            red_blue_correlations['sum_blue'].append((red_sum, blue))
            
            for red_num in combo:
                red_blue_correlations[f'red_{red_num}_blue'].append(blue)
        
        # 计算相关系数
        correlations = {}
        if red_blue_correlations['sum_blue']:
            sums, blues = zip(*red_blue_correlations['sum_blue'])
            if len(set(sums)) > 1 and len(set(blues)) > 1:
                corr, _ = stats.pearsonr(sums, blues)
                correlations['sum_blue'] = corr if not math.isnan(corr) else 0
        
        return {
            'red_blue_data': red_blue_correlations,
            'correlations': correlations
        }
    
    def analyze_trends(self):
        """趋势分析"""
        if len(self.red_balls) < 40:
            return {}
        
        # 计算趋势指标
        window_size = 20
        red_trends = {}
        blue_trends = {}
        
        for num in range(1, 34):
            recent_count = sum(1 for i in range(window_size) 
                             if num in self.red_balls[-(i+1)])
            prev_count = sum(1 for i in range(window_size, window_size*2) 
                           if len(self.red_balls) > window_size + i and 
                           num in self.red_balls[-(window_size+i+1)])
            
            if prev_count > 0:
                red_trends[num] = recent_count / prev_count
            else:
                red_trends[num] = recent_count + 1
        
        for num in range(1, 17):
            recent_count = sum(1 for i in range(window_size) 
                             if self.blue_balls[-(i+1)] == num)
            prev_count = sum(1 for i in range(window_size, window_size*2) 
                           if len(self.blue_balls) > window_size + i and 
                           self.blue_balls[-(window_size+i+1)] == num)
            
            if prev_count > 0:
                blue_trends[num] = recent_count / prev_count
            else:
                blue_trends[num] = recent_count + 1
        
        return {
            'red_trends': red_trends,
            'blue_trends': blue_trends
        }
    
    def smart_red_selection(self, analysis):
        """智能红球选择"""
        scores = defaultdict(float)
        
        # 1. 频率权重 (25%)
        red_freq = analysis['red_frequency']
        total_appearances = sum(red_freq['total'].values())
        
        for num in range(1, 34):
            # 总频率
            freq_score = red_freq['total'][num] / total_appearances
            scores[num] += freq_score * 0.15
            
            # 近期频率
            recent_score = red_freq['windows'][20][num] / 20 if 20 in red_freq['windows'] else 0
            scores[num] += recent_score * 0.10
        
        # 2. 热度指数 (20%)
        for num in range(1, 34):
            heat = red_freq['heat_index'][num]
            if heat > 1.2:  # 热号
                scores[num] += 0.15
            elif heat < 0.5:  # 冷号反弹
                scores[num] += 0.20
            else:  # 温号
                scores[num] += 0.10
        
        # 3. 间隔权重 (20%)
        interval_data = analysis['interval_analysis']
        for num in range(1, 34):
            current_interval = interval_data['red_current'][num]
            expected_interval = interval_data['red_expected'][num]
            
            if expected_interval > 0:
                ratio = current_interval / expected_interval
                if ratio >= 1.0:  # 超期或接近期望
                    scores[num] += min(0.20, ratio * 0.10)
        
        # 4. 位置权重 (15%)
        position_data = analysis['position_analysis']
        for num in range(1, 34):
            position_score = 0
            for pos in range(6):
                if num in position_data['frequency'][pos]:
                    pos_total = sum(position_data['frequency'][pos].values())
                    position_score += position_data['frequency'][pos][num] / pos_total
            scores[num] += (position_score / 6) * 0.15
        
        # 5. 趋势权重 (10%)
        if 'red_trends' in analysis['trend_analysis']:
            trends = analysis['trend_analysis']['red_trends']
            for num, trend in trends.items():
                if trend > 1.2:  # 上升趋势
                    scores[num] += 0.08
                elif trend > 0.8:  # 稳定
                    scores[num] += 0.05
        
        # 6. 组合权重 (10%)
        combo_data = analysis['combination_analysis']
        for num in range(1, 34):
            pair_score = 0
            for pair, count in combo_data['pairs'].items():
                if num in pair:
                    pair_score += count
            total_pairs = sum(combo_data['pairs'].values())
            if total_pairs > 0:
                scores[num] += (pair_score / total_pairs) * 0.10
        
        # 选择候选号码
        sorted_candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 生成最优组合
        best_combo = self.generate_optimal_red_combo(sorted_candidates[:15], analysis)
        
        return best_combo
    
    def generate_optimal_red_combo(self, candidates, analysis):
        """生成最优红球组合"""
        best_combo = None
        best_score = -1
        
        candidate_nums = [num for num, _ in candidates]
        
        # 尝试多种组合策略
        for attempt in range(3000):
            # 策略1: 从前12个候选中选择
            if attempt < 1000:
                combo = sorted(random.sample(candidate_nums[:12], 6))
            # 策略2: 从前15个候选中选择
            elif attempt < 2000:
                combo = sorted(random.sample(candidate_nums[:15], 6))
            # 策略3: 混合选择
            else:
                high_priority = random.sample(candidate_nums[:8], 4)
                low_priority = random.sample(candidate_nums[8:15], 2)
                combo = sorted(high_priority + low_priority)
            
            score = self.evaluate_red_combo(combo, analysis)
            
            if score > best_score:
                best_score = score
                best_combo = combo
        
        return best_combo if best_combo else sorted(candidate_nums[:6])
    
    def evaluate_red_combo(self, combo, analysis):
        """评估红球组合"""
        score = 0
        
        # 和值评估
        combo_sum = sum(combo)
        sum_dist = analysis['combination_analysis']['sums']
        if combo_sum in sum_dist:
            score += sum_dist[combo_sum] * 0.2
        
        # 跨度评估
        span = max(combo) - min(combo)
        span_dist = analysis['combination_analysis']['spans']
        if span in span_dist:
            score += span_dist[span] * 0.2
        
        # 奇偶比评估
        odd_count = sum(1 for x in combo if x % 2 == 1)
        odd_dist = analysis['combination_analysis']['odd_even']
        if odd_count in odd_dist:
            score += odd_dist[odd_count] * 0.2
        
        # 区间分布评估
        zone1 = sum(1 for x in combo if 1 <= x <= 11)
        zone2 = sum(1 for x in combo if 12 <= x <= 22)
        zone3 = sum(1 for x in combo if 23 <= x <= 33)
        zone_pattern = (zone1, zone2, zone3)
        
        zone_dist = analysis['distribution_analysis']['zones']
        if zone_pattern in zone_dist:
            score += zone_dist[zone_pattern] * 0.2
        
        # 配对历史评估
        pair_score = 0
        pairs = list(itertools.combinations(combo, 2))
        pair_dist = analysis['combination_analysis']['pairs']
        
        for pair in pairs:
            if pair in pair_dist:
                pair_score += pair_dist[pair]
        
        total_pairs = sum(pair_dist.values())
        if total_pairs > 0:
            score += (pair_score / total_pairs) * 0.2
        
        return score
    
    def smart_blue_selection(self, red_combo, analysis):
        """智能蓝球选择"""
        blue_scores = defaultdict(float)
        
        # 1. 频率权重 (30%)
        blue_freq = analysis['blue_frequency']
        total_blue_appearances = sum(blue_freq['total'].values())
        
        for num in range(1, 17):
            # 总频率
            freq_score = blue_freq['total'][num] / total_blue_appearances
            blue_scores[num] += freq_score * 0.20
            
            # 近期频率
            recent_score = blue_freq['windows'][20][num] / 20 if 20 in blue_freq['windows'] and num in blue_freq['windows'][20] else 0
            blue_scores[num] += recent_score * 0.10
        
        # 2. 热度指数 (25%)
        for num in range(1, 17):
            heat = blue_freq['heat_index'][num]
            if heat > 1.2:  # 热号
                blue_scores[num] += 0.15
            elif heat < 0.5:  # 冷号反弹
                blue_scores[num] += 0.25
            else:  # 温号
                blue_scores[num] += 0.10
        
        # 3. 间隔权重 (20%)
        interval_data = analysis['interval_analysis']
        for num in range(1, 17):
            current_interval = interval_data['blue_current'][num]
            expected_interval = interval_data['blue_expected'][num]
            
            if expected_interval > 0:
                ratio = current_interval / expected_interval
                if ratio >= 1.0:  # 超期或接近期望
                    blue_scores[num] += min(0.20, ratio * 0.10)
        
        # 4. 与红球关系 (15%)
        red_sum = sum(red_combo)
        corr_data = analysis['correlation_analysis']['red_blue_data']
        
        if 'sum_blue' in corr_data:
            sum_blue_pairs = corr_data['sum_blue']
            similar_sums = [(s, b) for s, b in sum_blue_pairs if abs(s - red_sum) <= 15]
            
            if similar_sums:
                blue_counter = Counter([b for _, b in similar_sums])
                for blue, count in blue_counter.items():
                    blue_scores[blue] += (count / len(similar_sums)) * 0.15
        
        # 5. 趋势权重 (10%)
        if 'blue_trends' in analysis['trend_analysis']:
            trends = analysis['trend_analysis']['blue_trends']
            for num, trend in trends.items():
                if trend > 1.2:  # 上升趋势
                    blue_scores[num] += 0.08
                elif trend > 0.8:  # 稳定
                    blue_scores[num] += 0.05
        
        # 选择得分最高的蓝球
        best_blue = max(blue_scores.items(), key=lambda x: x[1])[0]
        
        return best_blue
    
    def predict(self):
        """主预测函数"""
        if len(self.red_balls) < 100:
            # 数据不足，返回基于简单统计的预测
            red_freq = Counter()
            for combo in self.red_balls:
                for num in combo:
                    red_freq[num] += 1
            
            top_reds = [num for num, _ in red_freq.most_common(12)]
            selected_reds = sorted(random.sample(top_reds, 6))
            
            blue_freq = Counter(self.blue_balls)
            selected_blue = blue_freq.most_common(1)[0][0] if blue_freq else random.randint(1, 16)
            
            return selected_reds, selected_blue
        
        # 全面统计分析
        analysis = self.comprehensive_statistical_analysis()
        
        # 智能红球选择
        red_prediction = self.smart_red_selection(analysis)
        
        # 智能蓝球选择
        blue_prediction = self.smart_blue_selection(red_prediction, analysis)
        
        return red_prediction, blue_prediction

def load_data():
    """加载历史数据"""
    try:
        with open('ssq_data.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("错误：未找到 ssq_data.json 文件")
        return []

def parse_actual_numbers(red_str, blue_str):
    """解析实际开奖号码"""
    red_nums = [int(x) for x in red_str.split()]
    blue_num = int(blue_str)
    return sorted(red_nums), blue_num

def calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue):
    """计算命中率"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hit = 1 if predicted_blue == actual_blue else 0
    return red_hits, blue_hit

def realistic_high_performance_backtest():
    """现实高性能回测"""
    print("=== 现实高性能双色球预测算法回测 ===")
    print("目标：红球命中率 >= 3/6，蓝球命中率 >= 31.25%")
    print("策略：基于深度统计分析的现实可行方案")
    print()
    
    data = load_data()
    if len(data) < 500:
        print(f"警告：数据不足500期，当前只有 {len(data)} 期数据")
        return
    
    print(f"加载了 {len(data)} 期历史数据")
    
    test_periods = 500  # 测试500期
    training_periods = 200  # 使用200期数据进行训练
    
    results = []
    red_hits_total = 0
    blue_hits_total = 0
    
    print(f"开始回测最近 {test_periods} 期数据...")
    print()
    
    for i in range(test_periods):
        start_idx = max(0, len(data) - test_periods + i - training_periods)
        end_idx = len(data) - test_periods + i
        
        if end_idx <= start_idx:
            continue
            
        training_data = data[start_idx:end_idx]
        test_data = data[len(data) - test_periods + i]
        
        try:
            predictor = RealisticHighPerformancePredictor(training_data)
            predicted_red, predicted_blue = predictor.predict()
            
            actual_red, actual_blue = parse_actual_numbers(test_data['number'], test_data['refernumber'])
            
            red_hits, blue_hit = calculate_hit_rate(predicted_red, predicted_blue, actual_red, actual_blue)
            
            red_hits_total += red_hits
            blue_hits_total += blue_hit
            
            results.append({
                'period': test_data['issueno'],
                'predicted_red': predicted_red,
                'predicted_blue': predicted_blue,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'red_hits': red_hits,
                'blue_hit': blue_hit
            })
            
            if (i + 1) % 100 == 0:
                current_red_avg = red_hits_total / (i + 1)
                current_blue_rate = blue_hits_total / (i + 1)
                print(f"已完成 {i + 1}/{test_periods} 期回测")
                print(f"当前平均红球命中: {current_red_avg:.2f}/6")
                print(f"当前蓝球命中率: {current_blue_rate:.1%}")
                print()
                
        except Exception as e:
            print(f"期号 {test_data['issueno']} 预测失败: {e}")
            continue
    
    # 统计结果
    print("=== 现实高性能算法回测结果统计 ===")
    
    avg_red_hits = red_hits_total / len(results) if results else 0
    blue_hit_rate = blue_hits_total / len(results) if results else 0
    
    print(f"平均红球命中数: {avg_red_hits:.2f}/6")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits_total}/{len(results)})")
    print()
    
    # 目标达成情况
    target_red = 3.0
    target_blue = 5/16
    
    print("=== 目标达成情况 ===")
    print(f"红球目标: >= {target_red}/6, 实际: {avg_red_hits:.2f}/6 {'✓' if avg_red_hits >= target_red else '✗'}")
    print(f"蓝球目标: >= {target_blue:.1%}, 实际: {blue_hit_rate:.1%} {'✓' if blue_hit_rate >= target_blue else '✗'}")
    print()
    
    # 详细分析
    red_hit_distribution = Counter([r['red_hits'] for r in results])
    print("=== 红球命中分布 ===")
    for hits in sorted(red_hit_distribution.keys()):
        count = red_hit_distribution[hits]
        percentage = count / len(results) * 100
        print(f"{hits}个红球命中: {count}次 ({percentage:.1f}%)")
    print()
    
    # 高命中率期数统计
    high_red_hits = [r for r in results if r['red_hits'] >= 3]
    blue_hits = [r for r in results if r['blue_hit'] == 1]
    
    print("=== 高性能表现统计 ===")
    print(f"红球命中>=3个的期数: {len(high_red_hits)}期 ({len(high_red_hits)/len(results)*100:.1f}%)")
    print(f"蓝球命中期数: {len(blue_hits)}期 ({len(blue_hits)/len(results)*100:.1f}%)")
    print()
    
    # 最佳预测记录
    best_results = sorted(results, key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)[:20]
    print("=== 最佳预测记录 (前20期) ===")
    for i, result in enumerate(best_results, 1):
        print(f"{i:2d}. 期号:{result['period']} 红球:{result['red_hits']}/6 蓝球:{'✓' if result['blue_hit'] else '✗'}")
        if i <= 10:  # 只显示前10期的详细信息
            print(f"    预测红球: {result['predicted_red']}")
            print(f"    实际红球: {result['actual_red']}")
            print(f"    预测蓝球: {result['predicted_blue']}, 实际蓝球: {result['actual_blue']}")
            print()
    
    # 算法性能总结
    print("=== 算法性能总结 ===")
    print(f"1. 红球平均命中率: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"2. 蓝球命中率: {blue_hit_rate:.1%}")
    print(f"3. 红球命中>=3个的概率: {len(high_red_hits)/len(results)*100:.1f}%")
    print(f"4. 综合得分: {avg_red_hits + blue_hit_rate*6:.2f}/12")
    print()
    
    # 改进建议
    print("=== 改进建议 ===")
    if avg_red_hits < 2.0:
        print("- 红球命中率偏低，建议增强频率分析和热度指数权重")
    if blue_hit_rate < 0.15:
        print("- 蓝球命中率偏低，建议优化间隔分析和趋势判断")
    if len(high_red_hits)/len(results) < 0.15:
        print("- 高命中率期数较少，建议改进组合优化策略")
    
    print("- 继续收集更多历史数据以提升预测准确性")
    print("- 考虑引入更多维度的特征分析")
    print("- 优化权重分配和评分机制")
    
    return results

if __name__ == "__main__":
    results = realistic_high_performance_backtest()