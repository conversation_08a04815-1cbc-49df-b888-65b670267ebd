#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同号概率分析模块
分析预测号码与开奖号码的重复情况，并基于概率进行调整
"""

import json
import numpy as np
from collections import defaultdict, Counter
import math

class SameNumberAnalyzer:
    """同号概率分析器"""
    
    def __init__(self, data_file='ssq_data.json'):
        self.data = self.load_data(data_file)
        self.same_number_stats = self.calculate_same_number_probability()
        
    def load_data(self, filename):
        """加载双色球数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            processed_data = []
            for item in raw_data:
                red_str = item['number'].split(' ')
                red_balls = sorted([int(x) for x in red_str])
                blue_ball = int(item['refernumber'])
                
                processed_data.append({
                    'issue': item['issueno'],
                    'date': item['opendate'],
                    'red': red_balls,
                    'blue': blue_ball
                })
            
            return processed_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def calculate_same_number_probability(self):
        """计算同号概率统计"""
        if len(self.data) < 2:
            return {}
        
        # 统计红球同号情况
        red_same_counts = []
        blue_same_counts = []
        same_patterns = defaultdict(int)
        
        for i in range(1, len(self.data)):
            current_red = set(self.data[i]['red'])
            prev_red = set(self.data[i-1]['red'])
            current_blue = self.data[i]['blue']
            prev_blue = self.data[i-1]['blue']
            
            # 红球同号数量
            red_same_count = len(current_red & prev_red)
            red_same_counts.append(red_same_count)
            
            # 蓝球同号
            blue_same = 1 if current_blue == prev_blue else 0
            blue_same_counts.append(blue_same)
            
            # 记录同号模式
            pattern = f"red_{red_same_count}_blue_{blue_same}"
            same_patterns[pattern] += 1
        
        # 计算概率分布
        red_same_distribution = Counter(red_same_counts)
        total_periods = len(red_same_counts)
        
        red_same_probabilities = {}
        for count in range(7):  # 0-6个同号
            red_same_probabilities[count] = red_same_distribution.get(count, 0) / total_periods
        
        blue_same_rate = sum(blue_same_counts) / len(blue_same_counts)
        
        return {
            'red_same_counts': red_same_counts,
            'blue_same_counts': blue_same_counts,
            'red_same_probabilities': red_same_probabilities,
            'blue_same_rate': blue_same_rate,
            'same_patterns': dict(same_patterns),
            'avg_red_same': np.mean(red_same_counts),
            'total_periods': total_periods
        }
    
    def analyze_prediction_vs_latest(self, prediction_red, prediction_blue):
        """分析预测号码与最新开奖号码的重复情况"""
        if not self.data:
            return {}
        
        latest_red = set(self.data[0]['red'])
        latest_blue = int(self.data[0]['refernumber'])
        
        # 计算重复情况
        pred_red_set = set(prediction_red)
        red_same_count = len(pred_red_set & latest_red)
        blue_same = 1 if prediction_blue == latest_blue else 0
        
        # 获取这种重复情况的历史概率
        red_same_prob = self.same_number_stats['red_same_probabilities'].get(red_same_count, 0)
        
        return {
            'latest_issue': self.data[0]['issue'],
            'latest_red': list(latest_red),
            'latest_blue': latest_blue,
            'prediction_red': prediction_red,
            'prediction_blue': prediction_blue,
            'red_same_count': red_same_count,
            'blue_same': blue_same,
            'red_same_probability': red_same_prob,
            'blue_same_probability': self.same_number_stats['blue_same_rate'],
            'same_red_numbers': list(pred_red_set & latest_red)
        }
    
    def get_optimal_same_count_range(self):
        """获取最优的同号数量范围"""
        probs = self.same_number_stats['red_same_probabilities']
        
        # 找出概率最高的几个同号数量
        sorted_probs = sorted(probs.items(), key=lambda x: x[1], reverse=True)
        
        # 取前3个概率最高的同号数量作为优选范围
        optimal_range = [count for count, prob in sorted_probs[:3]]
        
        return optimal_range
    
    def adjust_weights_by_same_probability(self, base_weights, target_same_count=None):
        """根据同号概率调整权重"""
        if not self.data or not target_same_count:
            return base_weights
        
        latest_red = set(self.data[0]['red'])
        adjusted_weights = base_weights.copy()
        
        # 根据目标同号数量调整权重
        if target_same_count > 0:
            # 如果目标是有同号，提升上期号码的权重
            for num in latest_red:
                if num in adjusted_weights:
                    adjusted_weights[num] *= (1 + target_same_count * 0.2)
        else:
            # 如果目标是无同号，降低上期号码的权重
            for num in latest_red:
                if num in adjusted_weights:
                    adjusted_weights[num] *= 0.5
        
        return adjusted_weights
    
    def print_same_number_analysis(self):
        """打印同号概率分析报告"""
        stats = self.same_number_stats
        
        print("\n" + "="*60)
        print("📊 同号概率分析报告")
        print("="*60)
        
        print(f"📈 分析期数: {stats['total_periods']}期")
        print(f"📊 红球平均同号数: {stats['avg_red_same']:.2f}个")
        print(f"🔵 蓝球重复率: {stats['blue_same_rate']:.2%}")
        
        print("\n🔴 红球同号概率分布:")
        for count in range(7):
            prob = stats['red_same_probabilities'].get(count, 0)
            bar = "█" * int(prob * 50)
            print(f"  {count}个同号: {prob:.2%} {bar}")
        
        # 找出最常见的同号模式
        print("\n🎯 最常见的同号模式:")
        sorted_patterns = sorted(stats['same_patterns'].items(), key=lambda x: x[1], reverse=True)
        for i, (pattern, count) in enumerate(sorted_patterns[:5]):
            prob = count / stats['total_periods']
            print(f"  {i+1}. {pattern}: {count}次 ({prob:.2%})")
        
        print("="*60)

def main():
    """主函数 - 演示同号概率分析"""
    analyzer = SameNumberAnalyzer()
    
    # 打印分析报告
    analyzer.print_same_number_analysis()
    
    # 分析具体预测
    prediction_red = [2, 6, 9, 10, 14, 17, 27]
    prediction_blue = 1
    
    analysis = analyzer.analyze_prediction_vs_latest(prediction_red, prediction_blue)
    
    print("\n🎯 预测号码分析:")
    print(f"最新开奖: {analysis['latest_red']} + {analysis['latest_blue']}")
    print(f"预测号码: {analysis['prediction_red']} + {analysis['prediction_blue']}")
    print(f"红球同号: {analysis['red_same_count']}个 {analysis['same_red_numbers']}")
    print(f"蓝球同号: {'是' if analysis['blue_same'] else '否'}")
    print(f"此同号情况历史概率: {analysis['red_same_probability']:.2%}")
    
    # 获取最优同号范围
    optimal_range = analyzer.get_optimal_same_count_range()
    print(f"\n💡 建议同号数量范围: {optimal_range}")

if __name__ == "__main__":
    main()