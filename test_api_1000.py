import requests
import json

# 测试获取1000期福彩3D历史数据
url = 'https://api.jisuapi.com/caipiao/history'
params = {
    'appkey': 'eb6cc4f9bf4a23b4',
    'caipiaoid': '12',
    'num': '1000'
}

response = requests.get(url, params=params)
print('API返回状态:', response.status_code)

if response.status_code == 200:
    data = response.json()
    print('总数据量:', data['result']['total'])
    print('返回条数:', len(data['result']['list']))
    print('\n最新10期数据:')
    for i, item in enumerate(data['result']['list'][:10]):
        print(f'{i+1:2d}. 期号:{item["issueno"]} 日期:{item["opendate"]} 号码:{item["number"]}')
    
    # 保存数据到文件
    with open('fc3d_data_1000.json', 'w', encoding='utf-8') as f:
        json.dump(data['result']['list'], f, ensure_ascii=False, indent=2)
    print(f'\n已保存{len(data["result"]["list"])}期数据到 fc3d_data_1000.json')
else:
    print('API请求失败:', response.text)