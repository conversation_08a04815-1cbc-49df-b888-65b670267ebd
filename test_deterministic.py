import json
import numpy as np
from main import UltimateIntegratedPredictor

print("=== 测试算法确定性 ===\n")

# 测试1：使用998期数据
print("1. 使用998期数据进行预测：")
with open('ssq_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 保存原始数据
original_data = data.copy()
print(f"原始数据量: {len(data)}")

# 删除最后2期数据
data_998 = data[2:]
with open('ssq_data.json', 'w', encoding='utf-8') as f:
    json.dump(data_998, f, ensure_ascii=False, indent=2)

predictor1 = UltimateIntegratedPredictor()
analysis1 = predictor1.enhanced_data_analysis()
red_scores1 = predictor1.calculate_red_scores_ultimate(analysis1)
blue_scores1 = predictor1.calculate_blue_scores_ultimate(analysis1)

print(f"数据量: {len(predictor1.data)}")
print(f"红球前5个得分: {dict(list(red_scores1['neural'].items())[:5])}")
print(f"蓝球得分结构: {list(blue_scores1.keys())}")
if 'neural' in blue_scores1:
    print(f"蓝球前5个得分: {dict(list(blue_scores1['neural'].items())[:5])}")
else:
    print(f"蓝球前5个得分: {dict(list(blue_scores1.items())[:5])}")

# 测试2：恢复1000期数据
print("\n2. 使用1000期数据进行预测：")
with open('ssq_data.json', 'w', encoding='utf-8') as f:
    json.dump(original_data, f, ensure_ascii=False, indent=2)

predictor2 = UltimateIntegratedPredictor()
analysis2 = predictor2.enhanced_data_analysis()
red_scores2 = predictor2.calculate_red_scores_ultimate(analysis2)
blue_scores2 = predictor2.calculate_blue_scores_ultimate(analysis2)

print(f"数据量: {len(predictor2.data)}")
print(f"红球前5个得分: {dict(list(red_scores2['neural'].items())[:5])}")
print(f"蓝球得分结构: {list(blue_scores2.keys())}")
if 'neural' in blue_scores2:
    print(f"蓝球前5个得分: {dict(list(blue_scores2['neural'].items())[:5])}")
else:
    print(f"蓝球前5个得分: {dict(list(blue_scores2.items())[:5])}")

# 比较结果
print("\n3. 比较结果：")
red_diff = False
blue_diff = False

for ball in range(1, 6):  # 只比较前5个球
    if abs(red_scores1['neural'][ball] - red_scores2['neural'][ball]) > 0.001:
        red_diff = True
        break

# 比较蓝球得分
if 'neural' in blue_scores1 and 'neural' in blue_scores2:
    for ball in range(1, 6):  # 只比较前5个球
        if abs(blue_scores1['neural'][ball] - blue_scores2['neural'][ball]) > 0.001:
            blue_diff = True
            break
else:
    # 如果没有neural键，直接比较整体得分
    for ball in range(1, 6):
        if ball in blue_scores1 and ball in blue_scores2:
            if abs(blue_scores1[ball] - blue_scores2[ball]) > 0.001:
                blue_diff = True
                break

if red_diff or blue_diff:
    print("✓ 预测结果有差异，算法正常工作")
else:
    print("⚠️ 预测结果完全相同，可能存在确定性问题")

print("\n=== 测试完成 ===")