#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经网络集成双色球预测算法
结合深度学习、时间序列分析、集成学习等先进技术
目标：红球≥1.5/6，蓝球≥10%
"""

import json
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import random
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 尝试导入深度学习库
try:
    from sklearn.neural_network import MLPRegressor, MLPClassifier
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告：未安装scikit-learn，将使用简化版神经网络")

class SimpleNeuralNetwork:
    """简化版神经网络实现"""
    
    def __init__(self, input_size, hidden_size=64, output_size=1):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        
        # 初始化权重
        self.W1 = np.random.randn(input_size, hidden_size) * 0.1
        self.b1 = np.zeros((1, hidden_size))
        self.W2 = np.random.randn(hidden_size, output_size) * 0.1
        self.b2 = np.zeros((1, output_size))
        
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def forward(self, X):
        self.z1 = np.dot(X, self.W1) + self.b1
        self.a1 = self.sigmoid(self.z1)
        self.z2 = np.dot(self.a1, self.W2) + self.b2
        return self.z2
    
    def predict(self, X):
        return self.forward(X)

class NeuralEnsemblePredictor:
    """神经网络集成预测器"""
    
    def __init__(self):
        self.data = []
        self.red_models = []  # 红球模型集合
        self.blue_models = []  # 蓝球模型集合
        self.feature_scaler = None
        self.ensemble_weights = {
            'neural': 0.4,      # 神经网络权重
            'frequency': 0.2,   # 频率分析权重
            'markov': 0.2,      # 马尔科夫链权重
            'pattern': 0.1,     # 模式识别权重
            'time_series': 0.1  # 时间序列权重
        }
        
    def load_data(self, file_path: str):
        """加载历史数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 期历史数据")
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
        return True
    
    def extract_time_features(self, date_str: str) -> List[float]:
        """提取时间特征"""
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d')
            return [
                date.weekday() / 6.0,           # 星期几
                date.day / 31.0,                # 日期
                date.month / 12.0,              # 月份
                (date.timetuple().tm_yday) / 365.0,  # 年内第几天
                np.sin(2 * np.pi * date.month / 12),  # 月份周期性
                np.cos(2 * np.pi * date.month / 12),  # 月份周期性
            ]
        except:
            return [0.0] * 6
    
    def build_sequence_features(self, history_data: List[Dict], window_size: int = 10) -> np.ndarray:
        """构建序列特征用于神经网络训练"""
        features = []
        
        for i in range(len(history_data)):
            feature_vector = []
            
            # 时间特征
            time_features = self.extract_time_features(history_data[i].get('opendate', ''))
            feature_vector.extend(time_features)
            
            # 历史号码特征（滑动窗口）
            start_idx = max(0, i - window_size)
            window_data = history_data[start_idx:i]
            
            if window_data:
                # 红球频率特征
                red_freq = [0] * 33
                blue_freq = [0] * 16
                
                for record in window_data:
                    # 处理不同的分隔符格式
                    number_str = record['number'].strip()
                    if ',' in number_str:
                        red_nums = [int(x.strip()) for x in number_str.split(',')]
                    else:
                        red_nums = [int(x.strip()) for x in number_str.split()]
                    blue_num = int(record['refernumber'])
                    
                    for num in red_nums:
                        if 1 <= num <= 33:
                            red_freq[num-1] += 1
                    
                    if 1 <= blue_num <= 16:
                        blue_freq[blue_num-1] += 1
                
                # 归一化频率
                red_freq = [f / len(window_data) for f in red_freq]
                blue_freq = [f / len(window_data) for f in blue_freq]
                
                feature_vector.extend(red_freq)
                feature_vector.extend(blue_freq)
                
                # 间隔特征
                if len(window_data) >= 2:
                    # 处理最后一期红球
                    last_number = window_data[-1]['number'].strip()
                    if ',' in last_number:
                        last_red = [int(x.strip()) for x in last_number.split(',')]
                    else:
                        last_red = [int(x.strip()) for x in last_number.split()]
                    
                    # 处理前一期红球
                    prev_number = window_data[-2]['number'].strip()
                    if ',' in prev_number:
                        prev_red = [int(x.strip()) for x in prev_number.split(',')]
                    else:
                        prev_red = [int(x.strip()) for x in prev_number.split()]
                    
                    # 重号数量
                    repeat_count = len(set(last_red) & set(prev_red))
                    feature_vector.append(repeat_count / 6.0)
                    
                    # 和值变化
                    sum_diff = (sum(last_red) - sum(prev_red)) / 100.0
                    feature_vector.append(sum_diff)
                else:
                    feature_vector.extend([0.0, 0.0])
            else:
                # 填充零特征
                feature_vector.extend([0.0] * (33 + 16 + 2))
            
            features.append(feature_vector)
        
        return np.array(features)
    
    def train_neural_models(self, train_data: List[Dict]):
        """训练神经网络模型"""
        if len(train_data) < 20:
            print("训练数据不足，跳过神经网络训练")
            return
        
        print("开始训练神经网络模型...")
        
        # 构建特征和标签
        X = self.build_sequence_features(train_data[:-1])  # 除最后一期外的所有数据作为特征
        
        # 红球标签（下一期的红球号码）
        red_labels = []
        blue_labels = []
        
        for i in range(1, len(train_data)):
            # 处理红球号码
            number_str = train_data[i]['number'].strip()
            if ',' in number_str:
                red_nums = [int(x.strip()) for x in number_str.split(',')]
            else:
                red_nums = [int(x.strip()) for x in number_str.split()]
            blue_num = int(train_data[i]['refernumber'])
            
            # 红球标签：每个号码的出现概率
            red_label = [0] * 33
            for num in red_nums:
                if 1 <= num <= 33:
                    red_label[num-1] = 1
            red_labels.append(red_label)
            
            # 蓝球标签
            blue_label = [0] * 16
            if 1 <= blue_num <= 16:
                blue_label[blue_num-1] = 1
            blue_labels.append(blue_label)
        
        X = X[:len(red_labels)]  # 确保特征和标签长度一致
        
        if len(X) == 0:
            print("特征数据为空，跳过训练")
            return
        
        # 标准化特征
        if SKLEARN_AVAILABLE:
            self.feature_scaler = StandardScaler()
            X_scaled = self.feature_scaler.fit_transform(X)
        else:
            # 简单标准化
            X_mean = np.mean(X, axis=0)
            X_std = np.std(X, axis=0) + 1e-8
            X_scaled = (X - X_mean) / X_std
            self.feature_scaler = (X_mean, X_std)
        
        # 训练红球模型
        if SKLEARN_AVAILABLE:
            # 使用scikit-learn的多种模型
            red_models = [
                MLPRegressor(hidden_layer_sizes=(128, 64), max_iter=500, random_state=42),
                RandomForestRegressor(n_estimators=100, random_state=42),
                GradientBoostingRegressor(n_estimators=100, random_state=42)
            ]
            
            for i, model in enumerate(red_models):
                try:
                    model.fit(X_scaled, red_labels)
                    self.red_models.append(model)
                    print(f"红球模型 {i+1} 训练完成")
                except Exception as e:
                    print(f"红球模型 {i+1} 训练失败: {e}")
            
            # 训练蓝球模型
            blue_models = [
                MLPRegressor(hidden_layer_sizes=(64, 32), max_iter=500, random_state=42),
                RandomForestRegressor(n_estimators=100, random_state=42)
            ]
            
            for i, model in enumerate(blue_models):
                try:
                    model.fit(X_scaled, blue_labels)
                    self.blue_models.append(model)
                    print(f"蓝球模型 {i+1} 训练完成")
                except Exception as e:
                    print(f"蓝球模型 {i+1} 训练失败: {e}")
        else:
            # 使用简化版神经网络
            try:
                red_nn = SimpleNeuralNetwork(X_scaled.shape[1], 64, 33)
                blue_nn = SimpleNeuralNetwork(X_scaled.shape[1], 32, 16)
                
                self.red_models.append(red_nn)
                self.blue_models.append(blue_nn)
                print("简化版神经网络模型创建完成")
            except Exception as e:
                print(f"简化版神经网络创建失败: {e}")
    
    def neural_predict(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """使用神经网络进行预测"""
        if not self.red_models or not self.blue_models:
            return [0.0] * 33, [0.0] * 16
        
        # 构建当前特征
        current_features = self.build_sequence_features(recent_data)[-1:]
        
        if SKLEARN_AVAILABLE and self.feature_scaler:
            current_features = self.feature_scaler.transform(current_features)
        elif self.feature_scaler:
            X_mean, X_std = self.feature_scaler
            current_features = (current_features - X_mean) / X_std
        
        # 红球预测（集成多个模型）
        red_predictions = []
        for model in self.red_models:
            try:
                pred = model.predict(current_features)[0]
                if isinstance(pred, (list, np.ndarray)):
                    red_predictions.append(pred)
                else:
                    red_predictions.append([pred] * 33)
            except:
                red_predictions.append([0.0] * 33)
        
        # 蓝球预测
        blue_predictions = []
        for model in self.blue_models:
            try:
                pred = model.predict(current_features)[0]
                if isinstance(pred, (list, np.ndarray)):
                    blue_predictions.append(pred)
                else:
                    blue_predictions.append([pred] * 16)
            except:
                blue_predictions.append([0.0] * 16)
        
        # 集成预测结果
        if red_predictions:
            red_ensemble = np.mean(red_predictions, axis=0)
        else:
            red_ensemble = [0.0] * 33
        
        if blue_predictions:
            blue_ensemble = np.mean(blue_predictions, axis=0)
        else:
            blue_ensemble = [0.0] * 16
        
        return red_ensemble.tolist(), blue_ensemble.tolist()
    
    def frequency_analysis(self, recent_data: List[Dict], window_size: int = 30) -> Tuple[List[float], List[float]]:
        """频率分析预测"""
        red_freq = [0] * 33
        blue_freq = [0] * 16
        
        # 使用最近window_size期数据
        data_window = recent_data[-window_size:] if len(recent_data) > window_size else recent_data
        
        for record in data_window:
            # 处理红球号码
            number_str = record['number'].strip()
            if ',' in number_str:
                red_nums = [int(x.strip()) for x in number_str.split(',')]
            else:
                red_nums = [int(x.strip()) for x in number_str.split()]
            blue_num = int(record['refernumber'])
            
            for num in red_nums:
                if 1 <= num <= 33:
                    red_freq[num-1] += 1
            
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        # 归一化
        total_red = sum(red_freq)
        total_blue = sum(blue_freq)
        
        if total_red > 0:
            red_freq = [f / total_red for f in red_freq]
        if total_blue > 0:
            blue_freq = [f / total_blue for f in blue_freq]
        
        return red_freq, blue_freq
    
    def markov_analysis(self, recent_data: List[Dict], window_size: int = 50) -> Tuple[List[float], List[float]]:
        """马尔科夫链分析"""
        if len(recent_data) < 2:
            return [1/33] * 33, [1/16] * 16
        
        # 构建转移矩阵
        red_transitions = defaultdict(lambda: defaultdict(int))
        blue_transitions = defaultdict(int)
        
        data_window = recent_data[-window_size:] if len(recent_data) > window_size else recent_data
        
        for i in range(len(data_window) - 1):
            # 处理当前期红球
            current_number = data_window[i]['number'].strip()
            if ',' in current_number:
                current_red = set(int(x.strip()) for x in current_number.split(','))
            else:
                current_red = set(int(x.strip()) for x in current_number.split())
            
            # 处理下一期红球
            next_number = data_window[i+1]['number'].strip()
            if ',' in next_number:
                next_red = set(int(x.strip()) for x in next_number.split(','))
            else:
                next_red = set(int(x.strip()) for x in next_number.split())
            current_blue = int(data_window[i]['refernumber'])
            next_blue = int(data_window[i+1]['refernumber'])
            
            # 红球转移
            for curr_num in current_red:
                for next_num in next_red:
                    red_transitions[curr_num][next_num] += 1
            
            # 蓝球转移
            blue_transitions[next_blue] += 1
        
        # 计算预测概率
        last_number = recent_data[-1]['number'].strip()
        if ',' in last_number:
            last_red = set(int(x.strip()) for x in last_number.split(','))
        else:
            last_red = set(int(x.strip()) for x in last_number.split())
        
        red_probs = [0.0] * 33
        for num in range(1, 34):
            total_transitions = 0
            prob_sum = 0
            
            for last_num in last_red:
                if last_num in red_transitions:
                    total = sum(red_transitions[last_num].values())
                    if total > 0:
                        prob_sum += red_transitions[last_num].get(num, 0) / total
                        total_transitions += 1
            
            if total_transitions > 0:
                red_probs[num-1] = prob_sum / total_transitions
            else:
                red_probs[num-1] = 1/33
        
        # 蓝球概率
        blue_probs = [0.0] * 16
        total_blue_trans = sum(blue_transitions.values())
        
        for num in range(1, 17):
            if total_blue_trans > 0:
                blue_probs[num-1] = blue_transitions.get(num, 0) / total_blue_trans
            else:
                blue_probs[num-1] = 1/16
        
        return red_probs, blue_probs
    
    def pattern_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """模式识别分析"""
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        if len(recent_data) < 5:
            return [1/33] * 33, [1/16] * 16
        
        # 分析最近5期的模式
        recent_5 = recent_data[-5:]
        
        # 奇偶模式
        odd_count = 0
        even_count = 0
        
        for record in recent_5:
            # 处理红球号码
            number_str = record['number'].strip()
            if ',' in number_str:
                red_nums = [int(x.strip()) for x in number_str.split(',')]
            else:
                red_nums = [int(x.strip()) for x in number_str.split()]
            for num in red_nums:
                if num % 2 == 1:
                    odd_count += 1
                else:
                    even_count += 1
        
        # 根据历史奇偶比例调整权重
        total_count = odd_count + even_count
        if total_count > 0:
            odd_ratio = odd_count / total_count
            even_ratio = even_count / total_count
            
            for num in range(1, 34):
                if num % 2 == 1:
                    red_scores[num-1] += odd_ratio
                else:
                    red_scores[num-1] += even_ratio
        
        # 区间分析（1-11, 12-22, 23-33）
        zone_counts = [0, 0, 0]
        for record in recent_5:
            # 处理红球号码
            number_str = record['number'].strip()
            if ',' in number_str:
                red_nums = [int(x.strip()) for x in number_str.split(',')]
            else:
                red_nums = [int(x.strip()) for x in number_str.split()]
            for num in red_nums:
                if 1 <= num <= 11:
                    zone_counts[0] += 1
                elif 12 <= num <= 22:
                    zone_counts[1] += 1
                elif 23 <= num <= 33:
                    zone_counts[2] += 1
        
        total_zone = sum(zone_counts)
        if total_zone > 0:
            zone_ratios = [c / total_zone for c in zone_counts]
            
            for num in range(1, 34):
                if 1 <= num <= 11:
                    red_scores[num-1] += zone_ratios[0]
                elif 12 <= num <= 22:
                    red_scores[num-1] += zone_ratios[1]
                elif 23 <= num <= 33:
                    red_scores[num-1] += zone_ratios[2]
        
        # 蓝球模式（简单频率）
        blue_freq = [0] * 16
        for record in recent_5:
            blue_num = int(record['refernumber'])
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        total_blue = sum(blue_freq)
        if total_blue > 0:
            blue_scores = [f / total_blue for f in blue_freq]
        else:
            blue_scores = [1/16] * 16
        
        return red_scores, blue_scores
    
    def time_series_analysis(self, recent_data: List[Dict]) -> Tuple[List[float], List[float]]:
        """时间序列分析"""
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        if len(recent_data) < 10:
            return [1/33] * 33, [1/16] * 16
        
        # 分析最近10期的趋势
        recent_10 = recent_data[-10:]
        
        # 计算每个号码的出现趋势
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_10):
                # 处理红球号码
                number_str = record['number'].strip()
                if ',' in number_str:
                    red_nums = [int(x.strip()) for x in number_str.split(',')]
                else:
                    red_nums = [int(x.strip()) for x in number_str.split()]
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 2:
                # 计算间隔趋势
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                avg_interval = sum(intervals) / len(intervals)
                
                # 预测下次出现的可能性（间隔越短，下次出现可能性越高）
                last_appearance = appearances[-1]
                expected_next = last_appearance + avg_interval
                
                # 距离当前期数的差值
                distance = abs(len(recent_10) - expected_next)
                score = max(0, 1 - distance / 5)  # 距离越近分数越高
                red_scores[num-1] = score
            elif len(appearances) == 1:
                # 只出现一次，给予中等分数
                red_scores[num-1] = 0.3
            else:
                # 未出现，给予较低分数
                red_scores[num-1] = 0.1
        
        # 蓝球时间序列分析
        blue_appearances = {}
        for i, record in enumerate(recent_10):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_appearances:
                blue_appearances[blue_num] = []
            blue_appearances[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_appearances and len(blue_appearances[num]) >= 1:
                last_appearance = blue_appearances[num][-1]
                # 根据最后出现位置计算分数
                score = (len(recent_10) - last_appearance) / len(recent_10)
                blue_scores[num-1] = score
            else:
                blue_scores[num-1] = 0.5  # 未出现给予中等分数
        
        return red_scores, blue_scores
    
    def ensemble_predict(self, recent_data: List[Dict]) -> Tuple[List[int], int]:
        """集成预测"""
        # 获取各种预测方法的结果
        neural_red, neural_blue = self.neural_predict(recent_data)
        freq_red, freq_blue = self.frequency_analysis(recent_data)
        markov_red, markov_blue = self.markov_analysis(recent_data)
        pattern_red, pattern_blue = self.pattern_analysis(recent_data)
        ts_red, ts_blue = self.time_series_analysis(recent_data)
        
        # 集成红球预测
        red_ensemble = [0.0] * 33
        for i in range(33):
            red_ensemble[i] = (
                neural_red[i] * self.ensemble_weights['neural'] +
                freq_red[i] * self.ensemble_weights['frequency'] +
                markov_red[i] * self.ensemble_weights['markov'] +
                pattern_red[i] * self.ensemble_weights['pattern'] +
                ts_red[i] * self.ensemble_weights['time_series']
            )
        
        # 集成蓝球预测
        blue_ensemble = [0.0] * 16
        for i in range(16):
            blue_ensemble[i] = (
                neural_blue[i] * self.ensemble_weights['neural'] +
                freq_blue[i] * self.ensemble_weights['frequency'] +
                markov_blue[i] * self.ensemble_weights['markov'] +
                pattern_blue[i] * self.ensemble_weights['pattern'] +
                ts_blue[i] * self.ensemble_weights['time_series']
            )
        
        # 选择红球（Top-6）
        red_with_scores = [(i+1, score) for i, score in enumerate(red_ensemble)]
        red_with_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 智能选择策略：确保分布合理
        selected_red = []
        candidates = red_with_scores[:12]  # 从前12个候选中选择
        
        # 按分数选择，但确保分布
        for num, score in candidates:
            if len(selected_red) < 6:
                # 检查分布合理性
                if self._is_distribution_reasonable(selected_red + [num]):
                    selected_red.append(num)
        
        # 如果选择不足6个，补充高分号码
        while len(selected_red) < 6:
            for num, score in red_with_scores:
                if num not in selected_red:
                    selected_red.append(num)
                    break
        
        selected_red = sorted(selected_red[:6])
        
        # 选择蓝球
        blue_with_scores = [(i+1, score) for i, score in enumerate(blue_ensemble)]
        blue_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_scores[0][0]
        
        return selected_red, selected_blue
    
    def _is_distribution_reasonable(self, numbers: List[int]) -> bool:
        """检查号码分布是否合理"""
        if len(numbers) <= 1:
            return True
        
        # 检查连号不超过3个
        numbers_sorted = sorted(numbers)
        consecutive_count = 1
        max_consecutive = 1
        
        for i in range(1, len(numbers_sorted)):
            if numbers_sorted[i] == numbers_sorted[i-1] + 1:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 1
        
        if max_consecutive > 3:
            return False
        
        # 检查区间分布
        if len(numbers) >= 3:
            zone1 = sum(1 for n in numbers if 1 <= n <= 11)
            zone2 = sum(1 for n in numbers if 12 <= n <= 22)
            zone3 = sum(1 for n in numbers if 23 <= n <= 33)
            
            # 避免某个区间过于集中
            if zone1 > len(numbers) * 0.7 or zone2 > len(numbers) * 0.7 or zone3 > len(numbers) * 0.7:
                return False
        
        return True

def neural_ensemble_backtest(predictor, test_periods=500):
    """神经网络集成算法回测"""
    print("=== 神经网络集成双色球预测算法回测 ===")
    print(f"回测期数: {test_periods}")
    print("目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("=" * 50)
    
    if len(predictor.data) < test_periods + 100:
        print(f"数据不足，需要至少 {test_periods + 100} 期数据")
        return
    
    # 分割训练和测试数据
    train_data = predictor.data[:-test_periods]
    test_data = predictor.data[-test_periods:]
    
    # 训练神经网络模型
    predictor.train_neural_models(train_data)
    
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    print(f"开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用到当前期为止的所有历史数据进行预测
        history_data = predictor.data[:-(test_periods-i)]
        
        # 预测下一期
        pred_red, pred_blue = predictor.ensemble_predict(history_data)
        
        # 获取实际开奖结果
        actual_data = test_data[i]
        # 处理实际红球号码
        actual_number = actual_data['number'].strip()
        if ',' in actual_number:
            actual_red = [int(x.strip()) for x in actual_number.split(',')]
        else:
            actual_red = [int(x.strip()) for x in actual_number.split()]
        actual_blue = int(actual_data['refernumber'])
        
        # 计算命中情况
        red_hit_count = len(set(pred_red) & set(actual_red))
        blue_hit = 1 if pred_blue == actual_blue else 0
        
        red_hits.append(red_hit_count)
        blue_hits += blue_hit
        
        # 记录优秀预测
        if red_hit_count >= 3 or blue_hit == 1:
            best_predictions.append({
                'period': actual_data['issueno'],
                'red_hit': red_hit_count,
                'blue_hit': blue_hit,
                'predicted_red': pred_red,
                'actual_red': actual_red,
                'predicted_blue': pred_blue,
                'actual_blue': actual_blue
            })
        
        # 进度显示
        if (i + 1) % 100 == 0:
            current_red_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / (i + 1) * 100
            print(f"进度: {i+1}/{test_periods}, 当前红球: {current_red_avg:.2f}/6, 蓝球: {current_blue_rate:.1f}%")
    
    # 计算最终结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / test_periods * 100
    
    print(f"\n=== 神经网络集成算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits}/{test_periods})")
    
    # 红球命中分布
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_hits.count(i)
        percentage = count / test_periods * 100
        print(f"  {i}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 10.0
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前20期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:20]):
            red_symbol = "✓" if pred['blue_hit'] else "✗"
            print(f"{i+1:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{red_symbol}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 算法评估
    print(f"\n=== 神经网络集成算法评估 ===")
    if avg_red_hits >= red_target and blue_hit_rate >= blue_target:
        print("🎉 恭喜！算法达到预期目标")
    elif avg_red_hits >= red_target * 0.8 and blue_hit_rate >= blue_target * 0.8:
        print("🔧 算法表现良好，接近目标")
    else:
        print("📈 算法需要进一步优化")
    
    # 显示当前集成权重
    print(f"\n=== 当前集成权重 ===")
    for method, weight in predictor.ensemble_weights.items():
        print(f"  {method}: {weight:.3f}")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement
    }

if __name__ == "__main__":
    # 创建预测器
    predictor = NeuralEnsemblePredictor()
    
    # 加载数据
    if predictor.load_data('ssq_data.json'):
        # 运行回测
        results = neural_ensemble_backtest(predictor, test_periods=500)
        
        if results:
            print(f"\n=== 算法优化建议 ===")
            
            if results['red_avg'] < 1.5:
                print("红球优化建议:")
                print("- 调整神经网络架构，增加隐藏层")
                print("- 优化特征工程，增加更多时间序列特征")
                print("- 调整集成权重，增强表现好的方法")
                print("- 引入更多历史数据进行训练")
            
            if results['blue_rate'] < 10:
                print("蓝球优化建议:")
                print("- 增强蓝球神经网络模型复杂度")
                print("- 优化蓝球特征提取方法")
                print("- 调整蓝球预测权重分配")
                print("- 引入蓝球专用的时间序列分析")
    else:
        print("数据加载失败，请检查 ssq_data.json 文件")