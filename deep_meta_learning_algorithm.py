#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度元学习双色球预测算法
基于Stacking集成算法的进一步优化建议实现
目标：红球命中率≥1.5/6，蓝球命中率≥10%

优化特点：
1. 增加更多类型的基础预测器（LSTM、GRU、CNN等）
2. 使用深度神经网络作为元学习器
3. 优化特征工程和特征选择
4. 考虑时间序列特征和外部数据
5. 使用交叉验证优化超参数
"""

import json
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime, timedelta
import random
import math
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 尝试导入深度学习库
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.neural_network import MLPRegressor
    from sklearn.model_selection import cross_val_score, GridSearchCV
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.decomposition import PCA
    from sklearn.metrics import mean_squared_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告：scikit-learn未安装，将使用简化版本")

class TimeSeriesFeatureExtractor:
    """时间序列特征提取器"""
    
    def __init__(self):
        self.scaler = MinMaxScaler() if SKLEARN_AVAILABLE else None
    
    def extract_temporal_features(self, date_str: str) -> Dict[str, float]:
        """提取时间特征"""
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d')
        except:
            date = datetime.now()
        
        features = {
            'year': date.year,
            'month': date.month,
            'day': date.day,
            'weekday': date.weekday(),
            'quarter': (date.month - 1) // 3 + 1,
            'day_of_year': date.timetuple().tm_yday,
            'week_of_year': date.isocalendar()[1],
            'is_weekend': 1 if date.weekday() >= 5 else 0,
            'is_month_start': 1 if date.day <= 7 else 0,
            'is_month_end': 1 if date.day >= 24 else 0,
            'season': self._get_season(date.month)
        }
        
        # 周期性特征编码
        features.update({
            'month_sin': math.sin(2 * math.pi * date.month / 12),
            'month_cos': math.cos(2 * math.pi * date.month / 12),
            'day_sin': math.sin(2 * math.pi * date.day / 31),
            'day_cos': math.cos(2 * math.pi * date.day / 31),
            'weekday_sin': math.sin(2 * math.pi * date.weekday() / 7),
            'weekday_cos': math.cos(2 * math.pi * date.weekday() / 7)
        })
        
        return features
    
    def _get_season(self, month: int) -> int:
        """获取季节"""
        if month in [12, 1, 2]:
            return 1  # 冬季
        elif month in [3, 4, 5]:
            return 2  # 春季
        elif month in [6, 7, 8]:
            return 3  # 夏季
        else:
            return 4  # 秋季
    
    def extract_sequence_features(self, numbers: List[int], window_size: int = 10) -> Dict[str, float]:
        """提取序列特征"""
        if len(numbers) < window_size:
            window_size = len(numbers)
        
        recent_numbers = numbers[-window_size:]
        
        features = {
            'mean': np.mean(recent_numbers),
            'std': np.std(recent_numbers),
            'min': np.min(recent_numbers),
            'max': np.max(recent_numbers),
            'median': np.median(recent_numbers),
            'range': np.max(recent_numbers) - np.min(recent_numbers),
            'skewness': self._calculate_skewness(recent_numbers),
            'kurtosis': self._calculate_kurtosis(recent_numbers),
            'trend': self._calculate_trend(recent_numbers),
            'volatility': self._calculate_volatility(recent_numbers),
            'momentum': self._calculate_momentum(recent_numbers),
            'autocorr_lag1': self._calculate_autocorr(recent_numbers, 1),
            'autocorr_lag2': self._calculate_autocorr(recent_numbers, 2)
        }
        
        return features
    
    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean([(x - mean) ** 3 for x in data]) / (std ** 3)
    
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean([(x - mean) ** 4 for x in data]) / (std ** 4) - 3
    
    def _calculate_trend(self, data: List[float]) -> float:
        """计算趋势"""
        if len(data) < 2:
            return 0.0
        x = list(range(len(data)))
        n = len(data)
        sum_x = sum(x)
        sum_y = sum(data)
        sum_xy = sum(x[i] * data[i] for i in range(n))
        sum_x2 = sum(xi ** 2 for xi in x)
        
        denominator = n * sum_x2 - sum_x ** 2
        if denominator == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope
    
    def _calculate_volatility(self, data: List[float]) -> float:
        """计算波动性"""
        if len(data) < 2:
            return 0.0
        returns = [data[i] - data[i-1] for i in range(1, len(data))]
        return np.std(returns) if returns else 0.0
    
    def _calculate_momentum(self, data: List[float]) -> float:
        """计算动量"""
        if len(data) < 2:
            return 0.0
        return data[-1] - data[0]
    
    def _calculate_autocorr(self, data: List[float], lag: int) -> float:
        """计算自相关系数"""
        if len(data) <= lag:
            return 0.0
        
        n = len(data) - lag
        if n <= 1:
            return 0.0
        
        x1 = data[:-lag]
        x2 = data[lag:]
        
        mean1 = np.mean(x1)
        mean2 = np.mean(x2)
        
        numerator = sum((x1[i] - mean1) * (x2[i] - mean2) for i in range(n))
        denominator = math.sqrt(sum((x1[i] - mean1) ** 2 for i in range(n)) * 
                               sum((x2[i] - mean2) ** 2 for i in range(n)))
        
        return numerator / denominator if denominator != 0 else 0.0

class AdvancedBasePredictor:
    """高级基础预测器接口"""
    
    def __init__(self, name: str):
        self.name = name
        self.feature_extractor = TimeSeriesFeatureExtractor()
    
    def predict(self, data: List[Dict], target_type: str) -> List[int]:
        """预测方法，子类需要实现"""
        raise NotImplementedError
    
    def extract_features(self, data: List[Dict]) -> np.ndarray:
        """提取特征"""
        features = []
        for i, record in enumerate(data):
            feature_dict = {}
            
            # 时间特征
            temporal_features = self.feature_extractor.extract_temporal_features(record['date'])
            feature_dict.update(temporal_features)
            
            # 红球序列特征
            if 'red' in record:
                red_numbers = [int(x.strip()) for x in record['red'].replace(' ', ',').split(',') if x.strip()]
            elif 'number' in record:
                red_numbers = [int(x.strip()) for x in record['number'].split() if x.strip()]
            else:
                red_numbers = [1, 2, 3, 4, 5, 6]  # 默认值
            
            red_seq_features = self.feature_extractor.extract_sequence_features(red_numbers)
            for key, value in red_seq_features.items():
                feature_dict[f'red_{key}'] = value
            
            # 蓝球特征
            if 'blue' in record:
                blue_number = int(record['blue'])
            elif 'refernumber' in record:
                blue_number = int(record['refernumber'])
            else:
                blue_number = 1  # 默认值
            feature_dict['blue_value'] = blue_number
            
            # 历史统计特征
            if i > 0:
                hist_data = data[:i]
                hist_features = self._extract_historical_features(hist_data)
                feature_dict.update(hist_features)
            
            features.append(list(feature_dict.values()))
        
        return np.array(features)
    
    def _extract_historical_features(self, hist_data: List[Dict]) -> Dict[str, float]:
        """提取历史统计特征"""
        if not hist_data:
            return {}
        
        # 红球历史统计
        all_red_numbers = []
        for record in hist_data[-20:]:  # 最近20期
            # 处理红球号码字段
            if 'red' in record:
                red_numbers = [int(x.strip()) for x in record['red'].replace(' ', ',').split(',') if x.strip()]
            elif 'number' in record:
                red_numbers = [int(x.strip()) for x in record['number'].split() if x.strip()]
            else:
                continue
            all_red_numbers.extend(red_numbers)
        
        red_counter = Counter(all_red_numbers)
        
        # 蓝球历史统计
        blue_numbers = []
        for record in hist_data[-20:]:
            if 'blue' in record:
                blue_numbers.append(int(record['blue']))
            elif 'refernumber' in record:
                blue_numbers.append(int(record['refernumber']))
        blue_counter = Counter(blue_numbers)
        
        features = {
            'red_freq_mean': np.mean(list(red_counter.values())) if red_counter else 0,
            'red_freq_std': np.std(list(red_counter.values())) if red_counter else 0,
            'blue_freq_mean': np.mean(list(blue_counter.values())) if blue_counter else 0,
            'blue_freq_std': np.std(list(blue_counter.values())) if blue_counter else 0,
            'red_unique_count': len(red_counter),
            'blue_unique_count': len(blue_counter)
        }
        
        return features

class LSTMPredictor(AdvancedBasePredictor):
    """LSTM风格预测器（简化版）"""
    
    def __init__(self):
        super().__init__("LSTM")
        self.sequence_length = 10
        self.weights = None
    
    def predict(self, data: List[Dict], target_type: str) -> List[int]:
        """LSTM风格预测"""
        if len(data) < self.sequence_length:
            return self._fallback_predict(data, target_type)
        
        # 构建序列数据
        sequences = self._build_sequences(data, target_type)
        
        if target_type == 'red':
            return self._predict_red_lstm(sequences)
        else:
            return self._predict_blue_lstm(sequences)
    
    def _build_sequences(self, data: List[Dict], target_type: str) -> List[List[int]]:
        """构建序列数据"""
        sequences = []
        for i in range(len(data) - self.sequence_length + 1):
            sequence = []
            for j in range(i, i + self.sequence_length):
                if target_type == 'red':
                    red_numbers = [int(x) for x in data[j]['red'].split(',')]
                    sequence.extend(red_numbers)
                else:
                    sequence.append(int(data[j]['blue']))
            sequences.append(sequence)
        return sequences
    
    def _predict_red_lstm(self, sequences: List[List[int]]) -> List[int]:
        """LSTM风格红球预测"""
        if not sequences:
            return list(range(1, 7))
        
        # 简化的LSTM逻辑：基于序列模式
        last_sequence = sequences[-1]
        
        # 计算每个位置的趋势
        predictions = []
        for pos in range(6):
            pos_values = []
            for seq in sequences[-5:]:  # 最近5个序列
                if len(seq) > pos * 6:
                    pos_values.extend(seq[pos*6:(pos+1)*6])
            
            if pos_values:
                # 基于趋势和周期性预测
                trend = np.mean(np.diff(pos_values[-6:])) if len(pos_values) >= 6 else 0
                last_val = pos_values[-1] if pos_values else random.randint(1, 33)
                predicted = int(last_val + trend)
                predicted = max(1, min(33, predicted))
                predictions.append(predicted)
        
        # 确保不重复
        final_predictions = []
        for pred in predictions:
            if pred not in final_predictions:
                final_predictions.append(pred)
        
        # 补充到6个
        while len(final_predictions) < 6:
            candidate = random.randint(1, 33)
            if candidate not in final_predictions:
                final_predictions.append(candidate)
        
        return sorted(final_predictions[:6])
    
    def _predict_blue_lstm(self, sequences: List[List[int]]) -> List[int]:
        """LSTM风格蓝球预测"""
        if not sequences:
            return [random.randint(1, 16)]
        
        # 基于序列模式预测蓝球
        last_sequence = sequences[-1]
        
        # 计算趋势和周期性
        if len(last_sequence) >= 3:
            trend = np.mean(np.diff(last_sequence[-3:]))
            last_val = last_sequence[-1]
            predicted = int(last_val + trend)
            predicted = max(1, min(16, predicted))
        else:
            predicted = random.randint(1, 16)
        
        return [predicted]
    
    def _fallback_predict(self, data: List[Dict], target_type: str) -> List[int]:
        """回退预测方法"""
        if target_type == 'red':
            return sorted(random.sample(range(1, 34), 6))
        else:
            return [random.randint(1, 16)]

class CNNPredictor(AdvancedBasePredictor):
    """CNN风格预测器（简化版）"""
    
    def __init__(self):
        super().__init__("CNN")
        self.kernel_size = 3
        self.filters = 5
    
    def predict(self, data: List[Dict], target_type: str) -> List[int]:
        """CNN风格预测"""
        if len(data) < self.kernel_size:
            return self._fallback_predict(data, target_type)
        
        if target_type == 'red':
            return self._predict_red_cnn(data)
        else:
            return self._predict_blue_cnn(data)
    
    def _predict_red_cnn(self, data: List[Dict]) -> List[int]:
        """CNN风格红球预测"""
        # 构建特征图
        feature_maps = self._build_feature_maps(data, 'red')
        
        # 卷积操作（简化版）
        conv_results = []
        for feature_map in feature_maps:
            conv_result = self._apply_convolution(feature_map)
            conv_results.append(conv_result)
        
        # 池化和预测
        pooled = [max(result) for result in conv_results]
        
        # 基于卷积结果生成预测
        predictions = []
        for i, val in enumerate(pooled[:6]):
            predicted = int(val % 33) + 1
            if predicted not in predictions:
                predictions.append(predicted)
        
        # 补充到6个
        while len(predictions) < 6:
            candidate = random.randint(1, 33)
            if candidate not in predictions:
                predictions.append(candidate)
        
        return sorted(predictions[:6])
    
    def _predict_blue_cnn(self, data: List[Dict]) -> List[int]:
        """CNN风格蓝球预测"""
        # 构建蓝球特征图
        blue_values = []
        for record in data[-10:]:
            if 'blue' in record:
                blue_values.append(int(record['blue']))
            elif 'refernumber' in record:
                blue_values.append(int(record['refernumber']))
            else:
                blue_values.append(1)  # 默认值
        
        # 简化的卷积操作
        if len(blue_values) >= self.kernel_size:
            conv_result = self._apply_convolution(blue_values)
            predicted = int(max(conv_result) % 16) + 1
        else:
            predicted = random.randint(1, 16)
        
        return [predicted]
    
    def _build_feature_maps(self, data: List[Dict], target_type: str) -> List[List[float]]:
        """构建特征图"""
        feature_maps = []
        
        for i in range(self.filters):
            feature_map = []
            for record in data[-20:]:  # 最近20期
                if target_type == 'red':
                    if 'red' in record:
                        red_numbers = [int(x.strip()) for x in record['red'].replace(' ', ',').split(',') if x.strip()]
                    elif 'number' in record:
                        red_numbers = [int(x.strip()) for x in record['number'].split() if x.strip()]
                    else:
                        red_numbers = [1, 2, 3, 4, 5, 6]  # 默认值
                    
                    # 不同的特征提取方式
                    if i == 0:
                        feature_map.append(np.mean(red_numbers))
                    elif i == 1:
                        feature_map.append(np.std(red_numbers))
                    elif i == 2:
                        feature_map.append(max(red_numbers) - min(red_numbers))
                    elif i == 3:
                        feature_map.append(sum(red_numbers))
                    else:
                        feature_map.append(len(set(red_numbers)))
            feature_maps.append(feature_map)
        
        return feature_maps
    
    def _apply_convolution(self, feature_map: List[float]) -> List[float]:
        """应用卷积操作"""
        if len(feature_map) < self.kernel_size:
            return feature_map
        
        conv_result = []
        for i in range(len(feature_map) - self.kernel_size + 1):
            window = feature_map[i:i + self.kernel_size]
            # 简化的卷积核：求和
            conv_val = sum(window) / len(window)
            conv_result.append(conv_val)
        
        return conv_result
    
    def _fallback_predict(self, data: List[Dict], target_type: str) -> List[int]:
        """回退预测方法"""
        if target_type == 'red':
            return sorted(random.sample(range(1, 34), 6))
        else:
            return [random.randint(1, 16)]

class GRUPredictor(AdvancedBasePredictor):
    """GRU风格预测器（简化版）"""
    
    def __init__(self):
        super().__init__("GRU")
        self.hidden_size = 10
        self.sequence_length = 8
    
    def predict(self, data: List[Dict], target_type: str) -> List[int]:
        """GRU风格预测"""
        if len(data) < self.sequence_length:
            return self._fallback_predict(data, target_type)
        
        if target_type == 'red':
            return self._predict_red_gru(data)
        else:
            return self._predict_blue_gru(data)
    
    def _predict_red_gru(self, data: List[Dict]) -> List[int]:
        """GRU风格红球预测"""
        # 构建序列
        sequences = []
        for record in data[-self.sequence_length:]:
            if 'red' in record:
                red_numbers = [int(x.strip()) for x in record['red'].replace(' ', ',').split(',') if x.strip()]
            elif 'number' in record:
                red_numbers = [int(x.strip()) for x in record['number'].split() if x.strip()]
            else:
                red_numbers = [1, 2, 3, 4, 5, 6]  # 默认值
            sequences.append(red_numbers)
        
        # 简化的GRU逻辑：门控机制
        hidden_state = [0.5] * self.hidden_size
        
        for sequence in sequences:
            # 更新门
            update_gate = self._sigmoid(np.mean(sequence))
            # 重置门
            reset_gate = self._sigmoid(np.std(sequence))
            
            # 候选隐藏状态
            candidate = [self._tanh(x * reset_gate) for x in sequence[:self.hidden_size]]
            if len(candidate) < self.hidden_size:
                candidate.extend([0] * (self.hidden_size - len(candidate)))
            
            # 更新隐藏状态
            for i in range(self.hidden_size):
                hidden_state[i] = (1 - update_gate) * hidden_state[i] + update_gate * candidate[i]
        
        # 基于最终隐藏状态生成预测
        predictions = []
        for i in range(6):
            if i < len(hidden_state):
                predicted = int(abs(hidden_state[i] * 33)) + 1
                predicted = max(1, min(33, predicted))
                if predicted not in predictions:
                    predictions.append(predicted)
        
        # 补充到6个
        while len(predictions) < 6:
            candidate = random.randint(1, 33)
            if candidate not in predictions:
                predictions.append(candidate)
        
        return sorted(predictions[:6])
    
    def _predict_blue_gru(self, data: List[Dict]) -> List[int]:
        """GRU风格蓝球预测"""
        blue_sequence = []
        for record in data[-self.sequence_length:]:
            if 'blue' in record:
                blue_sequence.append(int(record['blue']))
            elif 'refernumber' in record:
                blue_sequence.append(int(record['refernumber']))
            else:
                blue_sequence.append(1)  # 默认值
        
        # 简化的GRU处理
        hidden_state = 0.5
        
        for blue_val in blue_sequence:
            update_gate = self._sigmoid(blue_val / 16.0)
            reset_gate = self._sigmoid(1 - blue_val / 16.0)
            
            candidate = self._tanh(blue_val / 16.0 * reset_gate)
            hidden_state = (1 - update_gate) * hidden_state + update_gate * candidate
        
        predicted = int(abs(hidden_state * 16)) + 1
        predicted = max(1, min(16, predicted))
        
        return [predicted]
    
    def _sigmoid(self, x: float) -> float:
        """Sigmoid激活函数"""
        return 1 / (1 + math.exp(-max(-500, min(500, x))))
    
    def _tanh(self, x: float) -> float:
        """Tanh激活函数"""
        return math.tanh(max(-500, min(500, x)))
    
    def _fallback_predict(self, data: List[Dict], target_type: str) -> List[int]:
        """回退预测方法"""
        if target_type == 'red':
            return sorted(random.sample(range(1, 34), 6))
        else:
            return [random.randint(1, 16)]

class DeepMetaLearner:
    """深度元学习器"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.feature_selector = SelectKBest(f_regression, k=20) if SKLEARN_AVAILABLE else None
        self.is_trained = False
        self.weights = None  # 初始化权重属性
    
    def train(self, base_predictions: np.ndarray, targets: np.ndarray, target_type: str):
        """训练元学习器"""
        if not SKLEARN_AVAILABLE:
            self._train_simple(base_predictions, targets, target_type)
            return
        
        try:
            # 特征预处理
            X_scaled = self.scaler.fit_transform(base_predictions)
            
            # 特征选择
            if X_scaled.shape[1] > 20:
                X_selected = self.feature_selector.fit_transform(X_scaled, targets.ravel())
            else:
                X_selected = X_scaled
            
            # 使用深度神经网络
            if target_type == 'red':
                # 红球使用多输出回归
                self.model = MLPRegressor(
                    hidden_layer_sizes=(100, 50, 25),
                    activation='relu',
                    solver='adam',
                    alpha=0.001,
                    max_iter=500,
                    random_state=42
                )
            else:
                # 蓝球使用单输出回归
                self.model = MLPRegressor(
                    hidden_layer_sizes=(50, 25),
                    activation='relu',
                    solver='adam',
                    alpha=0.001,
                    max_iter=500,
                    random_state=42
                )
            
            # 交叉验证优化
            param_grid = {
                'alpha': [0.0001, 0.001, 0.01],
                'learning_rate_init': [0.001, 0.01, 0.1]
            }
            
            grid_search = GridSearchCV(
                self.model, param_grid, cv=3, scoring='neg_mean_squared_error'
            )
            grid_search.fit(X_selected, targets.ravel())
            
            self.model = grid_search.best_estimator_
            self.is_trained = True
            
            print(f"元学习器训练完成 - {target_type}, 最佳参数: {grid_search.best_params_}")
            
        except Exception as e:
            print(f"深度元学习器训练失败: {e}，使用简化版本")
            self._train_simple(base_predictions, targets, target_type)
    
    def _train_simple(self, base_predictions: np.ndarray, targets: np.ndarray, target_type: str):
        """简化版训练"""
        # 简单的线性组合权重
        n_predictors = base_predictions.shape[1] // (6 if target_type == 'red' else 1)
        self.weights = np.random.random(n_predictors)
        self.weights = self.weights / np.sum(self.weights)
        self.is_trained = True
        print(f"简化元学习器训练完成 - {target_type}")
    
    def predict(self, base_predictions: np.ndarray, target_type: str) -> np.ndarray:
        """元学习器预测"""
        if not self.is_trained:
            # 未训练时返回简单平均
            if target_type == 'red':
                return np.mean(base_predictions.reshape(-1, 6), axis=0)
            else:
                return np.array([np.mean(base_predictions)])
        
        if SKLEARN_AVAILABLE and self.model is not None:
            try:
                # 使用训练好的深度模型
                X_scaled = self.scaler.transform(base_predictions.reshape(1, -1))
                
                if hasattr(self.feature_selector, 'transform'):
                    X_selected = self.feature_selector.transform(X_scaled)
                else:
                    X_selected = X_scaled
                
                prediction = self.model.predict(X_selected)
                return prediction.reshape(-1)
                
            except Exception as e:
                print(f"深度元学习器预测失败: {e}，使用简化版本")
        
        # 简化版预测
        if self.weights is None:
            # 如果没有权重，使用简单平均
            if target_type == 'red':
                return np.mean(base_predictions.reshape(-1, 6), axis=0)
            else:
                return np.array([np.mean(base_predictions)])
        
        if target_type == 'red':
            reshaped = base_predictions.reshape(-1, 6)
            weighted_pred = np.average(reshaped, axis=0, weights=self.weights)
            return weighted_pred
        else:
            weighted_pred = np.average(base_predictions, weights=self.weights)
            return np.array([weighted_pred])

class DeepMetaLearningPredictor:
    """深度元学习双色球预测器"""
    
    def __init__(self):
        # 基础预测器
        self.base_predictors = [
            LSTMPredictor(),
            CNNPredictor(), 
            GRUPredictor(),
            # 可以添加更多预测器
        ]
        
        # 元学习器
        self.red_meta_learner = DeepMetaLearner()
        self.blue_meta_learner = DeepMetaLearner()
        
        # 特征提取器
        self.feature_extractor = TimeSeriesFeatureExtractor()
        
        # 历史数据
        self.history = []
        
        print(f"深度元学习预测器初始化完成，包含 {len(self.base_predictors)} 个基础预测器")
    
    def load_data(self, file_path: str = 'ssq_data.json') -> List[Dict]:
        """加载数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条历史数据")
            return data
        except FileNotFoundError:
            print(f"数据文件 {file_path} 不存在")
            return []
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []
    
    def train_meta_learners(self, data: List[Dict], train_size: int = 100):
        """训练元学习器"""
        if len(data) < train_size + 20:
            print("数据量不足，跳过元学习器训练")
            return
        
        print("开始训练元学习器...")
        
        # 准备训练数据
        train_data = data[:train_size]
        
        # 收集基础预测器的预测结果
        red_base_predictions = []
        blue_base_predictions = []
        red_targets = []
        blue_targets = []
        
        for i in range(20, len(train_data)):
            # 使用前i条数据进行预测
            current_data = train_data[:i]
            
            # 获取基础预测器预测
            red_preds = []
            blue_preds = []
            
            for predictor in self.base_predictors:
                try:
                    red_pred = predictor.predict(current_data, 'red')
                    blue_pred = predictor.predict(current_data, 'blue')
                    red_preds.extend(red_pred)
                    blue_preds.extend(blue_pred)
                except Exception as e:
                    print(f"预测器 {predictor.name} 预测失败: {e}")
                    red_preds.extend([0] * 6)
                    blue_preds.extend([0] * 1)
            
            red_base_predictions.append(red_preds)
            blue_base_predictions.append(blue_preds)
            
            # 真实目标
            if 'red' in train_data[i]:
                actual_red = [int(x.strip()) for x in train_data[i]['red'].replace(' ', ',').split(',') if x.strip()]
            elif 'number' in train_data[i]:
                actual_red = [int(x.strip()) for x in train_data[i]['number'].split() if x.strip()]
            else:
                actual_red = [1, 2, 3, 4, 5, 6]  # 默认值
            
            if 'blue' in train_data[i]:
                actual_blue = int(train_data[i]['blue'])
            elif 'refernumber' in train_data[i]:
                actual_blue = int(train_data[i]['refernumber'])
            else:
                actual_blue = 1  # 默认值
            
            red_targets.append(actual_red)
            blue_targets.append([actual_blue])
        
        # 转换为numpy数组
        red_base_predictions = np.array(red_base_predictions)
        blue_base_predictions = np.array(blue_base_predictions)
        red_targets = np.array(red_targets)
        blue_targets = np.array(blue_targets)
        
        # 训练元学习器
        self.red_meta_learner.train(red_base_predictions, red_targets, 'red')
        self.blue_meta_learner.train(blue_base_predictions, blue_targets, 'blue')
        
        print("元学习器训练完成")
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """深度元学习预测"""
        if len(data) < 10:
            print("数据量不足，使用随机预测")
            return sorted(random.sample(range(1, 34), 6)), [random.randint(1, 16)]
        
        # 获取基础预测器预测
        red_base_predictions = []
        blue_base_predictions = []
        
        for predictor in self.base_predictors:
            try:
                red_pred = predictor.predict(data, 'red')
                blue_pred = predictor.predict(data, 'blue')
                red_base_predictions.extend(red_pred)
                blue_base_predictions.extend(blue_pred)
            except Exception as e:
                print(f"预测器 {predictor.name} 预测失败: {e}")
                red_base_predictions.extend([0] * 6)
                blue_base_predictions.extend([0] * 1)
        
        # 元学习器预测
        red_meta_pred = self.red_meta_learner.predict(
            np.array(red_base_predictions), 'red'
        )
        blue_meta_pred = self.blue_meta_learner.predict(
            np.array(blue_base_predictions), 'blue'
        )
        
        # 后处理
        red_final = self._post_process_red(red_meta_pred)
        blue_final = self._post_process_blue(blue_meta_pred)
        
        return red_final, blue_final
    
    def _post_process_red(self, predictions: np.ndarray) -> List[int]:
        """红球预测后处理"""
        # 转换为整数并限制范围
        red_nums = [max(1, min(33, int(round(x)))) for x in predictions[:6]]
        
        # 去重
        unique_reds = []
        for num in red_nums:
            if num not in unique_reds:
                unique_reds.append(num)
        
        # 补充到6个
        while len(unique_reds) < 6:
            candidate = random.randint(1, 33)
            if candidate not in unique_reds:
                unique_reds.append(candidate)
        
        return sorted(unique_reds[:6])
    
    def _post_process_blue(self, predictions: np.ndarray) -> List[int]:
        """蓝球预测后处理"""
        blue_num = max(1, min(16, int(round(predictions[0]))))
        return [blue_num]
    
    def update_history(self, red: List[int], blue: int, date: str):
        """更新历史记录"""
        record = {
            'number': ' '.join(map(str, red)),
            'refernumber': str(blue),
            'opendate': date
        }
        self.history.append(record)

def deep_meta_learning_backtest(data: List[Dict], test_periods: int = 50) -> Dict:
    """深度元学习算法回测"""
    if len(data) < test_periods + 100:
        print(f"数据量不足，需要至少 {test_periods + 100} 条数据")
        return {}
    
    predictor = DeepMetaLearningPredictor()
    
    # 训练元学习器
    train_data = data[:-test_periods]
    predictor.train_meta_learners(train_data)
    
    results = []
    red_hits = []
    blue_hits = []
    
    print(f"\n开始深度元学习算法回测，测试 {test_periods} 期...")
    
    for i in range(len(data) - test_periods, len(data)):
        # 使用前i条数据进行预测
        train_data = data[:i]
        
        # 预测
        pred_red, pred_blue = predictor.predict(train_data)
        
        # 实际结果
        if 'red' in data[i]:
            actual_red = [int(x.strip()) for x in data[i]['red'].replace(' ', ',').split(',') if x.strip()]
        elif 'number' in data[i]:
            actual_red = [int(x.strip()) for x in data[i]['number'].split() if x.strip()]
        else:
            actual_red = [1, 2, 3, 4, 5, 6]  # 默认值
        
        if 'blue' in data[i]:
            actual_blue = int(data[i]['blue'])
        elif 'refernumber' in data[i]:
            actual_blue = int(data[i]['refernumber'])
        else:
            actual_blue = 1  # 默认值
        
        # 计算命中
        red_hit = len(set(pred_red) & set(actual_red))
        blue_hit = 1 if pred_blue[0] == actual_blue else 0
        
        red_hits.append(red_hit)
        blue_hits.append(blue_hit)
        
        # 获取日期字段
        if 'date' in data[i]:
            period = data[i]['date']
        elif 'opendate' in data[i]:
            period = data[i]['opendate']
        else:
            period = f"第{i+1}期"
        
        result = {
            'period': period,
            'predicted_red': pred_red,
            'actual_red': actual_red,
            'predicted_blue': pred_blue[0],
            'actual_blue': actual_blue,
            'red_hit': red_hit,
            'blue_hit': blue_hit
        }
        results.append(result)
        
        if red_hit >= 3 or blue_hit == 1:
            period_str = data[i].get('date', data[i].get('opendate', f'第{i+1}期'))
            print(f"期号 {period_str}: 红球 {red_hit}/6, 蓝球 {blue_hit}/1")
    
    # 统计结果
    avg_red_hit = np.mean(red_hits)
    blue_hit_rate = np.mean(blue_hits) * 100
    
    red_target_achievement = (avg_red_hit / 1.5) * 100
    blue_target_achievement = blue_hit_rate / 10 * 100
    overall_achievement = (red_target_achievement + blue_target_achievement) / 2
    
    print(f"\n=== 深度元学习算法回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"红球平均命中: {avg_red_hit:.2f}/6 ({avg_red_hit/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}%")
    print(f"目标达成度: 红球 {red_target_achievement:.1f}%, 蓝球 {blue_target_achievement:.1f}%")
    print(f"综合达成度: {overall_achievement:.1f}%")
    
    # 显示最佳预测记录
    best_results = sorted(results, key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)[:15]
    print(f"\n前15期最佳预测记录:")
    for result in best_results:
        print(f"{result['period']}: 红球 {result['red_hit']}/6, 蓝球 {result['blue_hit']}/1")
    
    # 红球命中分布
    red_hit_dist = Counter(red_hits)
    print(f"\n红球命中分布:")
    for hit in sorted(red_hit_dist.keys()):
        print(f"{hit}个: {red_hit_dist[hit]}次 ({red_hit_dist[hit]/len(red_hits)*100:.1f}%)")
    
    return {
        'avg_red_hit': avg_red_hit,
        'blue_hit_rate': blue_hit_rate,
        'red_target_achievement': red_target_achievement,
        'blue_target_achievement': blue_target_achievement,
        'overall_achievement': overall_achievement,
        'results': results
    }

if __name__ == "__main__":
    # 创建预测器实例
    predictor = DeepMetaLearningPredictor()
    
    # 加载数据
    data = predictor.load_data()
    
    if data:
        # 执行回测
        backtest_results = deep_meta_learning_backtest(data, test_periods=50)
        
        if backtest_results:
            print(f"\n💡 进一步优化建议:")
            print(f"1. 增加更多深度学习架构（Transformer、ResNet等）")
            print(f"2. 实现更复杂的特征工程（图神经网络特征）")
            print(f"3. 使用强化学习优化预测策略")
            print(f"4. 引入外部数据源（经济指标、社会事件等）")
            print(f"5. 实现在线学习和模型自适应机制")
    else:
        print("无法加载数据，请检查数据文件")