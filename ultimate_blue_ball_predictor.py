#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极蓝球预测器
集成所有最佳策略的终极蓝球预测系统
目标：蓝球命中率≥12%

核心策略：
1. 多模型集成学习
2. 自适应权重优化
3. 深度特征挖掘
4. 时序模式识别
5. 概率分布建模
6. 反向工程验证
7. 动态策略调整
8. 智能投票机制
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class UltimateBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化终极蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 多模型权重（自适应调整）
        self.model_weights = {
            'frequency_model': 0.15,
            'interval_model': 0.14,
            'sequence_model': 0.13,
            'distribution_model': 0.12,
            'trend_model': 0.11,
            'neural_model': 0.10,
            'pattern_model': 0.09,
            'reverse_model': 0.08,
            'ensemble_model': 0.08
        }
        
        # 预测历史和性能统计
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'model_performance': {model: [] for model in self.model_weights.keys()},
            'recent_performance': []
        }
        
        # 学习参数
        self.learning_rate = 0.02
        self.momentum = 0.9
        self.weight_decay = 0.95
        
        # 缓存
        self.feature_cache = {}
        self.prediction_cache = {}
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def frequency_model(self, windows=[10, 20, 30, 50]):
        """频率模型 - 多窗口频率分析"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency_scores = {ball: 0.0 for ball in self.blue_range}
        
        for window in windows:
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                frequency = Counter(recent_blues)
                expected_freq = window / 16
                
                for ball in self.blue_range:
                    actual_freq = frequency[ball]
                    # 使用逆频率策略：出现少的给高分
                    deviation = expected_freq - actual_freq
                    score = 1.0 / (1.0 + np.exp(-deviation))  # sigmoid
                    frequency_scores[ball] += score / len(windows)
        
        return frequency_scores
    
    def interval_model(self):
        """间隔模型 - 基于间隔周期的预测"""
        if not self.data or len(self.data) < 10:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        interval_scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 2:
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                if intervals:
                    # 计算间隔统计
                    avg_interval = np.mean(intervals)
                    std_interval = np.std(intervals) if len(intervals) > 1 else avg_interval * 0.3
                    
                    current_interval = len(blue_sequence) - positions[-1]
                    
                    # 基于正态分布的概率
                    if std_interval > 0:
                        z_score = (current_interval - avg_interval) / std_interval
                        prob = np.exp(-0.5 * z_score ** 2)  # 高斯概率
                        interval_scores[ball] = prob
                    else:
                        interval_scores[ball] = 0.5
                else:
                    interval_scores[ball] = 0.5
            else:
                interval_scores[ball] = 0.9  # 很少出现的球给高分
        
        return interval_scores
    
    def sequence_model(self, seq_lengths=[3, 4, 5]):
        """序列模型 - 多长度序列模式匹配"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        sequence_scores = {ball: 0.0 for ball in self.blue_range}
        
        for seq_len in seq_lengths:
            if len(blue_sequence) >= seq_len + 1:
                current_seq = tuple(blue_sequence[-seq_len:])
                
                # 寻找历史匹配
                matches = []
                for i in range(len(blue_sequence) - seq_len):
                    hist_seq = tuple(blue_sequence[i:i+seq_len])
                    
                    # 计算序列相似度
                    similarity = sum(1 for a, b in zip(current_seq, hist_seq) if a == b)
                    
                    if similarity >= seq_len - 1:  # 允许一个不同
                        next_ball = blue_sequence[i + seq_len]
                        weight = similarity / seq_len  # 相似度权重
                        matches.append((next_ball, weight))
                
                # 计算加权得分
                if matches:
                    weighted_count = defaultdict(float)
                    total_weight = 0
                    
                    for ball, weight in matches:
                        weighted_count[ball] += weight
                        total_weight += weight
                    
                    for ball in self.blue_range:
                        if total_weight > 0:
                            sequence_scores[ball] += (weighted_count[ball] / total_weight) / len(seq_lengths)
                        else:
                            sequence_scores[ball] += (1.0 / 16) / len(seq_lengths)
                else:
                    for ball in self.blue_range:
                        sequence_scores[ball] += (1.0 / 16) / len(seq_lengths)
        
        return sequence_scores
    
    def distribution_model(self):
        """分布模型 - 基于统计分布的预测"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        
        distribution_scores = {}
        
        for ball in self.blue_range:
            actual_count = frequency[ball]
            expected_count = total_count / 16
            
            # 多种分布模型的综合
            # 1. 泊松分布
            poisson_prob = self.poisson_pmf(actual_count, expected_count)
            
            # 2. 均匀分布偏差
            uniform_deviation = abs(actual_count - expected_count)
            uniform_score = 1.0 / (1.0 + uniform_deviation / expected_count)
            
            # 3. 正态分布
            if total_count > 30:
                std_dev = np.sqrt(expected_count)
                z_score = (actual_count - expected_count) / std_dev
                normal_prob = np.exp(-0.5 * z_score ** 2)
            else:
                normal_prob = uniform_score
            
            # 综合得分
            distribution_scores[ball] = 0.4 * poisson_prob + 0.3 * uniform_score + 0.3 * normal_prob
        
        return distribution_scores
    
    def trend_model(self, trend_windows=[5, 10, 15, 20]):
        """趋势模型 - 多窗口趋势分析"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        trend_scores = {ball: 0.0 for ball in self.blue_range}
        
        for window in trend_windows:
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                
                for ball in self.blue_range:
                    # 计算在窗口内的出现位置
                    positions = [i for i, blue in enumerate(recent_blues) if blue == ball]
                    
                    if positions:
                        # 计算趋势强度
                        avg_position = np.mean(positions)
                        trend_strength = avg_position / (window - 1)  # 归一化到[0,1]
                        
                        # 计算出现频率
                        frequency = len(positions) / window
                        
                        # 趋势得分：最近出现的给低分，很久没出现的给高分
                        trend_score = 0.7 * (1 - trend_strength) + 0.3 * (1 - frequency)
                        trend_scores[ball] += trend_score / len(trend_windows)
                    else:
                        # 未出现的球给高分
                        trend_scores[ball] += 0.9 / len(trend_windows)
        
        return trend_scores
    
    def neural_model(self):
        """神经网络模型 - 简化的神经网络预测"""
        if not self.data or len(self.data) < 20:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        
        # 构建特征向量
        features = []
        
        # 最近20期的蓝球
        recent_20 = blue_sequence[-20:] if len(blue_sequence) >= 20 else blue_sequence
        
        # 特征1：频率特征
        frequency = Counter(recent_20)
        freq_features = [frequency[i] / len(recent_20) for i in range(1, 17)]
        features.extend(freq_features)
        
        # 特征2：间隔特征
        if len(recent_20) > 1:
            intervals = [recent_20[i] - recent_20[i-1] for i in range(1, len(recent_20))]
            interval_features = [
                np.mean(intervals),
                np.std(intervals) if len(intervals) > 1 else 0,
                np.max(intervals),
                np.min(intervals)
            ]
        else:
            interval_features = [0, 0, 0, 0]
        features.extend(interval_features)
        
        # 特征3：趋势特征
        if len(recent_20) >= 5:
            recent_5 = recent_20[-5:]
            trend_features = [
                np.mean(recent_5),
                recent_5[-1],
                (recent_5[-1] - recent_5[0]) / 5 if len(recent_5) > 1 else 0
            ]
        else:
            trend_features = [0, 0, 0]
        features.extend(trend_features)
        
        # 简化的神经网络计算
        input_dim = len(features)
        hidden_dim = 32
        output_dim = 16
        
        # 随机权重（在实际应用中应该是训练好的）
        np.random.seed(42)  # 固定种子保证一致性
        W1 = np.random.randn(input_dim, hidden_dim) * 0.1
        W2 = np.random.randn(hidden_dim, output_dim) * 0.1
        
        # 前向传播
        features = np.array(features)
        h1 = np.tanh(np.dot(features, W1))
        output = np.dot(h1, W2)
        
        # Softmax
        exp_output = np.exp(output - np.max(output))
        probs = exp_output / np.sum(exp_output)
        
        # 转换为字典
        neural_scores = {}
        for i, ball in enumerate(self.blue_range):
            neural_scores[ball] = probs[i]
        
        return neural_scores
    
    def pattern_model(self):
        """模式模型 - 复杂模式识别"""
        if not self.data or len(self.data) < 30:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        pattern_scores = {ball: 0.0 for ball in self.blue_range}
        
        # 模式1：周期性模式
        for period in [7, 14, 21, 28]:  # 检查不同周期
            if len(blue_sequence) >= period * 2:
                current_pos = len(blue_sequence) % period
                
                # 找到相同位置的历史球号
                same_position_balls = []
                for i in range(period, len(blue_sequence), period):
                    if i + current_pos < len(blue_sequence):
                        same_position_balls.append(blue_sequence[i + current_pos])
                
                if same_position_balls:
                    position_count = Counter(same_position_balls)
                    total_same_pos = len(same_position_balls)
                    
                    for ball in self.blue_range:
                        pattern_scores[ball] += (position_count[ball] / total_same_pos) * 0.25
        
        # 模式2：奇偶模式
        recent_30 = blue_sequence[-30:] if len(blue_sequence) >= 30 else blue_sequence
        odd_count = sum(1 for x in recent_30 if x % 2 == 1)
        even_count = len(recent_30) - odd_count
        
        if odd_count > even_count:
            # 奇数较多，偏向偶数
            for ball in self.blue_range:
                if ball % 2 == 0:
                    pattern_scores[ball] += 0.1
        else:
            # 偶数较多，偏向奇数
            for ball in self.blue_range:
                if ball % 2 == 1:
                    pattern_scores[ball] += 0.1
        
        # 模式3：大小号模式
        big_count = sum(1 for x in recent_30 if x > 8)
        small_count = len(recent_30) - big_count
        
        if big_count > small_count:
            # 大号较多，偏向小号
            for ball in self.blue_range:
                if ball <= 8:
                    pattern_scores[ball] += 0.1
        else:
            # 小号较多，偏向大号
            for ball in self.blue_range:
                if ball > 8:
                    pattern_scores[ball] += 0.1
        
        return pattern_scores
    
    def reverse_model(self):
        """反向模型 - 反向工程验证"""
        if not self.data or len(self.data) < 50:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        # 使用历史数据进行反向验证
        split_point = int(len(self.data) * 0.9)
        train_data = self.data[:split_point]
        test_data = self.data[split_point:]
        
        reverse_scores = {ball: 0.0 for ball in self.blue_range}
        
        # 临时切换数据进行验证
        original_data = self.data
        self.data = train_data
        
        correct_predictions = {ball: 0 for ball in self.blue_range}
        total_predictions = {ball: 0 for ball in self.blue_range}
        
        for i, test_entry in enumerate(test_data[:min(20, len(test_data))]):  # 验证最多20期
            # 使用简化的预测方法
            freq_scores = self.frequency_model([10, 20])
            interval_scores = self.interval_model()
            
            # 简单组合
            combined_scores = {}
            for ball in self.blue_range:
                combined_scores[ball] = 0.6 * freq_scores[ball] + 0.4 * interval_scores[ball]
            
            # 找到最高分的球
            predicted_ball = max(combined_scores.items(), key=lambda x: x[1])[0]
            actual_ball = test_entry['blue']
            
            total_predictions[predicted_ball] += 1
            if predicted_ball == actual_ball:
                correct_predictions[predicted_ball] += 1
            
            # 更新训练数据
            self.data.append(test_entry)
        
        # 恢复原始数据
        self.data = original_data
        
        # 计算反向验证得分
        for ball in self.blue_range:
            if total_predictions[ball] > 0:
                accuracy = correct_predictions[ball] / total_predictions[ball]
                reverse_scores[ball] = accuracy
            else:
                reverse_scores[ball] = 1.0 / 16
        
        return reverse_scores
    
    def ensemble_model(self, all_predictions):
        """集成模型 - 智能集成多个预测结果"""
        ensemble_scores = {ball: 0.0 for ball in self.blue_range}
        
        # 方法1：加权平均
        for predictions in all_predictions:
            for ball in self.blue_range:
                ensemble_scores[ball] += predictions.get(ball, 0)
        
        # 归一化
        total_score = sum(ensemble_scores.values())
        if total_score > 0:
            for ball in self.blue_range:
                ensemble_scores[ball] /= total_score
        
        # 方法2：投票机制
        vote_scores = {ball: 0.0 for ball in self.blue_range}
        for predictions in all_predictions:
            # 找到每个模型的前3名
            top_3 = sorted(predictions.items(), key=lambda x: x[1], reverse=True)[:3]
            for i, (ball, score) in enumerate(top_3):
                vote_scores[ball] += (3 - i) / 6  # 第1名得3分，第2名得2分，第3名得1分
        
        # 综合两种方法
        final_ensemble = {}
        for ball in self.blue_range:
            final_ensemble[ball] = 0.7 * ensemble_scores[ball] + 0.3 * vote_scores[ball]
        
        return final_ensemble
    
    def poisson_pmf(self, k, lam):
        """泊松分布概率质量函数"""
        if lam <= 0:
            return 0
        try:
            return (lam ** k) * np.exp(-lam) / math.factorial(int(k))
        except:
            return 0
    
    def adaptive_weight_update(self):
        """自适应权重更新"""
        if len(self.performance_stats['recent_performance']) < 20:
            return
        
        recent_performance = self.performance_stats['recent_performance'][-20:]
        avg_performance = np.mean(recent_performance)
        
        # 如果性能不佳，调整权重
        if avg_performance < 0.10:  # 低于10%
            # 增加表现好的模型权重
            for model in self.model_weights:
                if model in self.performance_stats['model_performance']:
                    model_perf = self.performance_stats['model_performance'][model]
                    if model_perf:
                        model_avg = np.mean(model_perf[-10:]) if len(model_perf) >= 10 else np.mean(model_perf)
                        if model_avg > avg_performance:
                            self.model_weights[model] *= 1.05  # 增加5%
                        else:
                            self.model_weights[model] *= 0.95  # 减少5%
        
        # 归一化权重
        total_weight = sum(self.model_weights.values())
        for model in self.model_weights:
            self.model_weights[model] /= total_weight
    
    def predict_blue_ball(self):
        """终极蓝球预测"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取所有模型的预测
        freq_scores = self.frequency_model()
        interval_scores = self.interval_model()
        sequence_scores = self.sequence_model()
        distribution_scores = self.distribution_model()
        trend_scores = self.trend_model()
        neural_scores = self.neural_model()
        pattern_scores = self.pattern_model()
        reverse_scores = self.reverse_model()
        
        # 集成所有预测
        all_predictions = [
            freq_scores, interval_scores, sequence_scores, distribution_scores,
            trend_scores, neural_scores, pattern_scores, reverse_scores
        ]
        ensemble_scores = self.ensemble_model(all_predictions)
        
        # 计算最终得分
        final_scores = {}
        model_results = {
            'frequency_model': freq_scores,
            'interval_model': interval_scores,
            'sequence_model': sequence_scores,
            'distribution_model': distribution_scores,
            'trend_model': trend_scores,
            'neural_model': neural_scores,
            'pattern_model': pattern_scores,
            'reverse_model': reverse_scores,
            'ensemble_model': ensemble_scores
        }
        
        for ball in self.blue_range:
            final_score = 0
            for model, weight in self.model_weights.items():
                final_score += model_results[model][ball] * weight
            final_scores[ball] = final_score
        
        # 多策略选择
        predictions = []
        
        # 策略1：最高分
        best_ball = max(final_scores.items(), key=lambda x: x[1])[0]
        predictions.append(best_ball)
        
        # 策略2：概率采样
        total_score = sum(final_scores.values())
        if total_score > 0:
            probs = [final_scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 策略3：集成模型优先
        ensemble_best = max(ensemble_scores.items(), key=lambda x: x[1])[0]
        predictions.append(ensemble_best)
        
        # 策略4：神经网络优先
        neural_best = max(neural_scores.items(), key=lambda x: x[1])[0]
        predictions.append(neural_best)
        
        # 策略5：频率模型优先（冷号策略）
        freq_best = max(freq_scores.items(), key=lambda x: x[1])[0]
        predictions.append(freq_best)
        
        # 智能投票决策
        vote_count = Counter(predictions)
        
        # 如果有明显的多数票，选择它
        most_common = vote_count.most_common(1)[0]
        if most_common[1] >= 3:  # 至少3票
            final_prediction = most_common[0]
        else:
            # 否则选择最高分
            final_prediction = best_ball
        
        # 记录预测
        self.prediction_history.append({
            'prediction': final_prediction,
            'final_scores': final_scores,
            'model_results': {k: v[final_prediction] for k, v in model_results.items()},
            'vote_results': dict(vote_count),
            'timestamp': datetime.now().isoformat()
        })
        
        return final_prediction
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 记录最近性能
        self.performance_stats['recent_performance'].append(1.0 if is_correct else 0.0)
        if len(self.performance_stats['recent_performance']) > 100:
            self.performance_stats['recent_performance'].pop(0)
        
        # 更新模型性能
        if self.prediction_history:
            last_prediction = self.prediction_history[-1]
            for model in self.model_weights.keys():
                if model in last_prediction['model_results']:
                    model_score = last_prediction['model_results'][model]
                    # 如果该模型给出的分数较高且预测正确，记录为好的性能
                    if model_score > 0.5 and is_correct:
                        self.performance_stats['model_performance'][model].append(1.0)
                    elif model_score <= 0.5 and not is_correct:
                        self.performance_stats['model_performance'][model].append(1.0)
                    else:
                        self.performance_stats['model_performance'][model].append(0.0)
                    
                    # 保持最近50次记录
                    if len(self.performance_stats['model_performance'][model]) > 50:
                        self.performance_stats['model_performance'][model].pop(0)
        
        # 自适应权重更新
        self.adaptive_weight_update()
    
    def get_performance_report(self):
        """获取性能报告"""
        recent_hit_rate = 0.0
        if self.performance_stats['recent_performance']:
            recent_hit_rate = np.mean(self.performance_stats['recent_performance'][-50:])
        
        model_performance = {}
        for model, perf_list in self.performance_stats['model_performance'].items():
            if perf_list:
                model_performance[model] = np.mean(perf_list[-20:])
            else:
                model_performance[model] = 0.0
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'overall_hit_rate': self.performance_stats['hit_rate'],
            'recent_hit_rate': recent_hit_rate,
            'model_weights': self.model_weights.copy(),
            'model_performance': model_performance
        }

def test_ultimate_blue_predictor():
    """测试终极蓝球预测器"""
    print("=== 终极蓝球预测器测试 ===")
    
    predictor = UltimateBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(150, len(original_data) - 30)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 10 or i % 40 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    for key, value in report.items():
        if key == 'model_weights':
            print(f"模型权重配置:")
            for weight_key, weight_value in value.items():
                print(f"  {weight_key}: {weight_value:.3f}")
        elif key == 'model_performance':
            print(f"模型性能:")
            for perf_key, perf_value in value.items():
                print(f"  {perf_key}: {perf_value:.3f}")
        else:
            if isinstance(value, float):
                print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"各模型得分:")
        for model, score in last_prediction['model_results'].items():
            print(f"  {model}: {score:.4f}")
        
        print(f"投票结果: {last_prediction['vote_results']}")
    
    return predictor

if __name__ == "__main__":
    test_ultimate_blue_predictor()