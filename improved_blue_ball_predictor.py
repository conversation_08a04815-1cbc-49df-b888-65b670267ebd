import numpy as np
from collections import Counter, defaultdict
from datetime import datetime
import random

class ImprovedBlueBallPredictor:
    """改进的蓝球预测器 - 重点降低同号权重，加强间隔、奇偶、大小号规律分析"""
    
    def __init__(self, data=None):
        """初始化改进的蓝球预测器"""
        self.data = data if data else []
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 调整后的特征权重 - 降低频率分析权重，提高间隔和规律分析权重
        self.feature_weights = {
            'anti_consecutive': 0.25,      # 反连号分析 - 最高权重
            'interval_probability': 0.20,  # 间隔概率分析
            'odd_even_pattern': 0.15,      # 奇偶规律
            'size_pattern': 0.15,          # 大小号规律
            'frequency_analysis': 0.10,    # 频率分析 - 降低权重
            'trend_analysis': 0.08,        # 趋势分析
            'distribution_balance': 0.07   # 分布平衡
        }
        
        # 预测历史
        self.prediction_history = []
    
    def get_last_blue_ball(self):
        """获取上一期蓝球号码"""
        if not self.data:
            return None
        return self.data[0]['blue'] if 'blue' in self.data[0] else int(self.data[0]['refernumber'])
    
    def anti_consecutive_analysis(self):
        """反连号分析 - 降低与上期相同号码的权重"""
        scores = {ball: 1.0 for ball in self.blue_range}
        
        last_blue = self.get_last_blue_ball()
        if last_blue is not None:
            # 大幅降低上期号码的权重
            scores[last_blue] = 0.1  # 降低到10%
            
            # 统计历史连号概率
            consecutive_count = 0
            total_count = 0
            
            for i in range(1, min(len(self.data), 100)):  # 分析最近100期
                current_blue = self.data[i-1]['blue'] if 'blue' in self.data[i-1] else int(self.data[i-1]['refernumber'])
                prev_blue = self.data[i]['blue'] if 'blue' in self.data[i] else int(self.data[i]['refernumber'])
                
                if current_blue == prev_blue:
                    consecutive_count += 1
                total_count += 1
            
            # 根据历史连号概率进一步调整
            if total_count > 0:
                consecutive_prob = consecutive_count / total_count
                # 如果历史连号概率很低，进一步降低同号权重
                if consecutive_prob < 0.05:  # 如果连号概率小于5%
                    scores[last_blue] = 0.05
        
        return scores
    
    def interval_probability_analysis(self):
        """间隔概率分析 - 基于历史间隔模式预测"""
        if not self.data or len(self.data) < 10:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {}
        
        for ball in self.blue_range:
            intervals = []
            last_occurrence = -1
            
            # 计算历史间隔
            for i, entry in enumerate(self.data):
                current_blue = entry['blue'] if 'blue' in entry else int(entry['refernumber'])
                if current_blue == ball:
                    if last_occurrence >= 0:
                        intervals.append(i - last_occurrence)
                    last_occurrence = i
            
            if intervals and last_occurrence >= 0:
                # 当前间隔
                current_interval = len(self.data) - last_occurrence - 1
                
                # 计算间隔分布
                interval_counts = Counter(intervals)
                total_intervals = len(intervals)
                
                # 预测下一个最可能的间隔
                if current_interval in interval_counts:
                    # 如果当前间隔在历史中出现过，计算其概率
                    prob = interval_counts[current_interval] / total_intervals
                else:
                    # 使用平均间隔估算
                    avg_interval = np.mean(intervals)
                    # 基于正态分布估算概率
                    std_interval = np.std(intervals) if len(intervals) > 1 else avg_interval * 0.5
                    prob = np.exp(-0.5 * ((current_interval - avg_interval) / std_interval) ** 2) if std_interval > 0 else 0.1
                
                # 间隔越接近历史平均值，概率越高
                avg_interval = np.mean(intervals)
                interval_score = 1.0 / (1.0 + abs(current_interval - avg_interval) / avg_interval) if avg_interval > 0 else 0.5
                
                scores[ball] = min(prob * 2 + interval_score, 1.0)
            else:
                # 如果该球从未出现或只出现一次，给予中等分数
                scores[ball] = 0.6
        
        return scores
    
    def odd_even_pattern_analysis(self):
        """奇偶规律分析"""
        if not self.data or len(self.data) < 10:
            return {ball: 0.5 for ball in self.blue_range}
        
        # 分析最近20期的奇偶模式
        recent_data = self.data[:20]
        odd_count = 0
        even_count = 0
        
        for entry in recent_data:
            blue = entry['blue'] if 'blue' in entry else int(entry['refernumber'])
            if blue % 2 == 1:
                odd_count += 1
            else:
                even_count += 1
        
        total = odd_count + even_count
        if total == 0:
            return {ball: 0.5 for ball in self.blue_range}
        
        odd_ratio = odd_count / total
        even_ratio = even_count / total
        
        # 获取上期奇偶性
        last_blue = self.get_last_blue_ball()
        last_is_odd = last_blue % 2 == 1 if last_blue else None
        
        scores = {}
        for ball in self.blue_range:
            is_odd = ball % 2 == 1
            
            # 基础奇偶概率
            if is_odd:
                base_score = 1.0 - abs(odd_ratio - 0.5)  # 越接近50%越好
            else:
                base_score = 1.0 - abs(even_ratio - 0.5)
            
            # 如果与上期奇偶性相同，略微降低权重（避免连续同奇偶性）
            if last_is_odd is not None and is_odd == last_is_odd:
                base_score *= 0.8
            
            scores[ball] = base_score
        
        return scores
    
    def size_pattern_analysis(self):
        """大小号规律分析（1-8为小号，9-16为大号）"""
        if not self.data or len(self.data) < 10:
            return {ball: 0.5 for ball in self.blue_range}
        
        # 分析最近20期的大小号模式
        recent_data = self.data[:20]
        small_count = 0  # 1-8
        large_count = 0  # 9-16
        
        for entry in recent_data:
            blue = entry['blue'] if 'blue' in entry else int(entry['refernumber'])
            if blue <= 8:
                small_count += 1
            else:
                large_count += 1
        
        total = small_count + large_count
        if total == 0:
            return {ball: 0.5 for ball in self.blue_range}
        
        small_ratio = small_count / total
        large_ratio = large_count / total
        
        # 获取上期大小号
        last_blue = self.get_last_blue_ball()
        last_is_small = last_blue <= 8 if last_blue else None
        
        scores = {}
        for ball in self.blue_range:
            is_small = ball <= 8
            
            # 基础大小号概率
            if is_small:
                base_score = 1.0 - abs(small_ratio - 0.5)  # 越接近50%越好
            else:
                base_score = 1.0 - abs(large_ratio - 0.5)
            
            # 如果与上期大小号相同，略微降低权重
            if last_is_small is not None and is_small == last_is_small:
                base_score *= 0.85
            
            scores[ball] = base_score
        
        return scores
    
    def frequency_analysis(self):
        """频率分析 - 权重已降低"""
        if not self.data:
            return {ball: 1/16 for ball in self.blue_range}
        
        # 分析最近50期的频率
        recent_data = self.data[:50]
        blue_counts = Counter()
        
        for entry in recent_data:
            blue = entry['blue'] if 'blue' in entry else int(entry['refernumber'])
            blue_counts[blue] += 1
        
        total_count = len(recent_data)
        scores = {}
        
        for ball in self.blue_range:
            observed_freq = blue_counts[ball] / total_count if total_count > 0 else 0
            expected_freq = 1 / 16
            
            # 计算偏差，偏差越小分数越高
            deviation = abs(observed_freq - expected_freq)
            scores[ball] = 1.0 - deviation * 8  # 归一化
            scores[ball] = max(0.1, min(1.0, scores[ball]))  # 限制在0.1-1.0之间
        
        return scores
    
    def trend_analysis(self):
        """趋势分析"""
        if not self.data or len(self.data) < 15:
            return {ball: 0.5 for ball in self.blue_range}
        
        scores = {}
        
        for ball in self.blue_range:
            # 分析最近15期的趋势
            recent_15 = self.data[:15]
            occurrences = []
            
            for i, entry in enumerate(recent_15):
                blue = entry['blue'] if 'blue' in entry else int(entry['refernumber'])
                occurrences.append(1 if blue == ball else 0)
            
            if len(occurrences) > 5:
                # 计算趋势斜率
                x = np.arange(len(occurrences))
                y = np.array(occurrences)
                
                if np.var(x) > 0:
                    slope = np.cov(x, y)[0, 1] / np.var(x)
                    # 正斜率表示上升趋势，负斜率表示下降趋势
                    scores[ball] = 0.5 + slope * 2  # 调整系数
                    scores[ball] = max(0.1, min(1.0, scores[ball]))
                else:
                    scores[ball] = 0.5
            else:
                scores[ball] = 0.5
        
        return scores
    
    def distribution_balance_analysis(self):
        """分布平衡分析"""
        if not self.data:
            return {ball: 0.5 for ball in self.blue_range}
        
        # 分析整体分布的平衡性
        all_blues = []
        for entry in self.data:
            blue = entry['blue'] if 'blue' in entry else int(entry['refernumber'])
            all_blues.append(blue)
        
        blue_counts = Counter(all_blues)
        total_count = len(all_blues)
        expected_count = total_count / 16
        
        scores = {}
        for ball in self.blue_range:
            actual_count = blue_counts[ball]
            # 如果某个号码出现次数远低于期望，给予更高分数
            if actual_count < expected_count:
                scores[ball] = 0.7 + (expected_count - actual_count) / expected_count * 0.3
            else:
                scores[ball] = 0.7 - (actual_count - expected_count) / expected_count * 0.2
            
            scores[ball] = max(0.1, min(1.0, scores[ball]))
        
        return scores
    
    def predict_blue_ball(self):
        """预测蓝球 - 综合所有改进的分析方法"""
        if not self.data:
            return random.randint(1, 16)
        
        # 添加时间种子确保每次调用都有不同的随机性
        import time
        current_time = int(time.time() * 1000) % 10000
        np.random.seed(current_time)
        random.seed(current_time)
        
        # 获取各种分析得分
        anti_consecutive_scores = self.anti_consecutive_analysis()
        interval_scores = self.interval_probability_analysis()
        odd_even_scores = self.odd_even_pattern_analysis()
        size_scores = self.size_pattern_analysis()
        frequency_scores = self.frequency_analysis()
        trend_scores = self.trend_analysis()
        distribution_scores = self.distribution_balance_analysis()
        
        # 计算最终得分
        final_scores = {}
        for ball in self.blue_range:
            # 添加小量随机噪声增加多样性
            random_factor = 1.0 + (random.random() - 0.5) * 0.1  # ±5%的随机变化
            final_score = (
                anti_consecutive_scores[ball] * self.feature_weights['anti_consecutive'] +
                interval_scores[ball] * self.feature_weights['interval_probability'] +
                odd_even_scores[ball] * self.feature_weights['odd_even_pattern'] +
                size_scores[ball] * self.feature_weights['size_pattern'] +
                frequency_scores[ball] * self.feature_weights['frequency_analysis'] +
                trend_scores[ball] * self.feature_weights['trend_analysis'] +
                distribution_scores[ball] * self.feature_weights['distribution_balance']
            ) * random_factor
            final_scores[ball] = final_score
        
        # 多策略选择
        predictions = []
        
        # 策略1：最高分
        best_ball = max(final_scores.items(), key=lambda x: x[1])[0]
        predictions.append(best_ball)
        
        # 策略2：概率采样（排除上期号码）
        last_blue = self.get_last_blue_ball()
        sampling_scores = final_scores.copy()
        if last_blue and last_blue in sampling_scores:
            sampling_scores[last_blue] *= 0.1  # 进一步降低上期号码被选中的概率
        
        total_score = sum(sampling_scores.values())
        if total_score > 0:
            probs = [sampling_scores[ball] / total_score for ball in self.blue_range]
            sampled_ball = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_ball)
        
        # 策略3：间隔优先
        interval_best = max(interval_scores.items(), key=lambda x: x[1])[0]
        if interval_best != last_blue:  # 确保不是上期号码
            predictions.append(interval_best)
        
        # 策略4：随机高分选择（从前5名中随机选择）
        top_5_balls = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)[:5]
        random_top_ball = random.choice(top_5_balls)[0]
        predictions.append(random_top_ball)
        
        # 投票决策 - 增加随机性
        vote_count = Counter(predictions)
        top_votes = vote_count.most_common(2)  # 获取前2名
        
        if len(top_votes) > 1 and top_votes[0][1] == top_votes[1][1]:  # 如果票数相同
            # 随机选择其中一个
            final_prediction = random.choice([top_votes[0][0], top_votes[1][0]])
        else:
            final_prediction = top_votes[0][0]
        
        # 记录预测详情
        self.prediction_history.append({
            'prediction': final_prediction,
            'last_blue': last_blue,
            'final_scores': final_scores,
            'component_scores': {
                'anti_consecutive': anti_consecutive_scores[final_prediction],
                'interval_probability': interval_scores[final_prediction],
                'odd_even_pattern': odd_even_scores[final_prediction],
                'size_pattern': size_scores[final_prediction],
                'frequency_analysis': frequency_scores[final_prediction],
                'trend_analysis': trend_scores[final_prediction],
                'distribution_balance': distribution_scores[final_prediction]
            },
            'timestamp': datetime.now().isoformat()
        })
        
        return final_prediction
    
    def get_prediction_analysis(self):
        """获取预测分析报告"""
        if not self.prediction_history:
            return "暂无预测历史"
        
        latest = self.prediction_history[-1]
        last_blue = latest['last_blue']
        prediction = latest['prediction']
        
        analysis = f"""蓝球预测分析报告：
上期蓝球：{last_blue}
预测蓝球：{prediction}

各项分析得分：
• 反连号分析：{latest['component_scores']['anti_consecutive']:.3f}
• 间隔概率分析：{latest['component_scores']['interval_probability']:.3f}
• 奇偶规律分析：{latest['component_scores']['odd_even_pattern']:.3f}
• 大小号规律：{latest['component_scores']['size_pattern']:.3f}
• 频率分析：{latest['component_scores']['frequency_analysis']:.3f}
• 趋势分析：{latest['component_scores']['trend_analysis']:.3f}
• 分布平衡：{latest['component_scores']['distribution_balance']:.3f}

预测理由：
- {'避开上期同号' if prediction != last_blue else '特殊情况选择同号'}
- 奇偶性：{'奇数' if prediction % 2 == 1 else '偶数'}
- 大小号：{'小号(1-8)' if prediction <= 8 else '大号(9-16)'}
"""
        return analysis