#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化蓝球预测器
基于最有效策略的简化蓝球预测系统
目标：蓝球命中率≥12%

核心策略（经过验证的最有效方法）：
1. 冷号回补策略（主要）
2. 间隔周期预测（辅助）
3. 历史频率平衡（辅助）
4. 简单趋势判断（辅助）
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class OptimizedBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化优化蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 简化的策略权重（基于实际效果）
        self.strategy_weights = {
            'cold_number_strategy': 0.50,    # 冷号策略（主要）
            'interval_strategy': 0.25,       # 间隔策略
            'frequency_balance': 0.15,       # 频率平衡
            'simple_trend': 0.10             # 简单趋势
        }
        
        # 预测历史
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'recent_performance': []
        }
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def cold_number_strategy(self):
        """冷号策略 - 专注于长期未出现的号码"""
        if not self.data or len(self.data) < 20:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 分析不同窗口的冷号情况
        windows = [20, 30, 50, 80]
        window_weights = [0.4, 0.3, 0.2, 0.1]
        
        for window, weight in zip(windows, window_weights):
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                frequency = Counter(recent_blues)
                
                # 计算每个号码的"冷度"
                for ball in self.blue_range:
                    appear_count = frequency[ball]
                    expected_count = window / 16  # 期望出现次数
                    
                    # 冷度计算：出现次数越少，冷度越高
                    if appear_count == 0:
                        coldness = 2.0  # 完全未出现
                    else:
                        coldness = max(0, (expected_count - appear_count) / expected_count)
                        if coldness > 0:
                            coldness = coldness * 1.5  # 放大冷号效应
                    
                    scores[ball] += coldness * weight
        
        # 特别关注超级冷号（很长时间未出现）
        for ball in self.blue_range:
            # 找到该球最后一次出现的位置
            last_positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if last_positions:
                last_position = last_positions[-1]
                gap = len(blue_sequence) - last_position - 1  # 距离现在的间隔
                
                # 如果间隔超过平均间隔的2倍，给予额外分数
                avg_interval = len(blue_sequence) / 16
                if gap > avg_interval * 2:
                    super_cold_bonus = min(1.0, gap / (avg_interval * 4))  # 最多1分
                    scores[ball] += super_cold_bonus
            else:
                # 从未出现过（理论上不应该存在）
                scores[ball] += 2.0
        
        return scores
    
    def interval_strategy(self):
        """间隔策略 - 基于历史间隔模式预测"""
        if not self.data or len(self.data) < 30:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            # 找到该球的所有出现位置
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 2:
                # 计算历史间隔
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                if intervals:
                    # 统计间隔信息
                    avg_interval = np.mean(intervals)
                    median_interval = np.median(intervals)
                    
                    # 当前间隔
                    current_interval = len(blue_sequence) - positions[-1]
                    
                    # 基于平均间隔的预测
                    if current_interval >= avg_interval * 0.8:  # 接近或超过平均间隔
                        interval_score = min(2.0, current_interval / avg_interval)
                        scores[ball] = interval_score
                    
                    # 基于中位数间隔的预测
                    if current_interval >= median_interval * 0.9:
                        median_score = min(1.5, current_interval / median_interval)
                        scores[ball] = max(scores[ball], median_score)
                    
                    # 特殊情况：如果当前间隔明显超过历史最大间隔
                    max_interval = max(intervals)
                    if current_interval > max_interval:
                        scores[ball] = 2.0
                        
            elif len(positions) == 1:
                # 只出现过一次
                current_interval = len(blue_sequence) - positions[0]
                # 根据间隔给分
                if current_interval > 30:
                    scores[ball] = 1.8
                elif current_interval > 20:
                    scores[ball] = 1.5
                elif current_interval > 10:
                    scores[ball] = 1.2
                else:
                    scores[ball] = 0.8
            else:
                # 从未出现
                scores[ball] = 2.0
        
        return scores
    
    def frequency_balance_strategy(self):
        """频率平衡策略 - 确保长期频率平衡"""
        if not self.data:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        
        scores = {ball: 0.0 for ball in self.blue_range}
        expected_count = total_count / 16
        
        for ball in self.blue_range:
            actual_count = frequency[ball]
            
            # 频率偏差
            deviation = expected_count - actual_count
            
            if deviation > 0:  # 出现次数少于期望
                # 偏差越大，分数越高
                balance_score = min(2.0, deviation / expected_count * 2)
                scores[ball] = balance_score
            else:
                # 出现次数多于期望，给予较低分数
                scores[ball] = 0.2
        
        return scores
    
    def simple_trend_strategy(self):
        """简单趋势策略 - 基于最近趋势的简单判断"""
        if not self.data or len(self.data) < 10:
            return {ball: 1.0/16 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 分析最近10期的情况
        recent_10 = blue_sequence[-10:] if len(blue_sequence) >= 10 else blue_sequence
        recent_frequency = Counter(recent_10)
        
        for ball in self.blue_range:
            recent_count = recent_frequency[ball]
            
            # 简单的反向策略：最近出现多的给低分，最近未出现的给高分
            if recent_count == 0:
                scores[ball] = 1.0  # 最近未出现
            elif recent_count == 1:
                scores[ball] = 0.6  # 最近出现1次
            elif recent_count == 2:
                scores[ball] = 0.3  # 最近出现2次
            else:
                scores[ball] = 0.1  # 最近出现3次或以上
        
        # 分析最近5期的数值趋势
        if len(blue_sequence) >= 5:
            recent_5 = blue_sequence[-5:]
            
            # 计算平均值趋势
            avg_recent = np.mean(recent_5)
            
            for ball in self.blue_range:
                # 如果最近平均值偏小，给大号加分；如果偏大，给小号加分
                if avg_recent < 8:  # 最近偏小
                    if ball > 8:
                        scores[ball] += 0.3
                elif avg_recent > 8:  # 最近偏大
                    if ball <= 8:
                        scores[ball] += 0.3
        
        return scores
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各策略的得分
        cold_scores = self.cold_number_strategy()
        interval_scores = self.interval_strategy()
        frequency_scores = self.frequency_balance_strategy()
        trend_scores = self.simple_trend_strategy()
        
        # 计算最终得分
        final_scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            final_scores[ball] = (
                cold_scores[ball] * self.strategy_weights['cold_number_strategy'] +
                interval_scores[ball] * self.strategy_weights['interval_strategy'] +
                frequency_scores[ball] * self.strategy_weights['frequency_balance'] +
                trend_scores[ball] * self.strategy_weights['simple_trend']
            )
        
        # 归一化得分
        max_score = max(final_scores.values()) if final_scores.values() else 1
        if max_score > 0:
            for ball in self.blue_range:
                final_scores[ball] /= max_score
        
        # 选择策略：优先选择得分最高的，但也考虑随机性
        sorted_balls = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 80%概率选择前3名，20%概率选择前8名
        if random.random() < 0.8:
            top_candidates = sorted_balls[:3]
        else:
            top_candidates = sorted_balls[:8]
        
        # 在候选中按权重随机选择
        candidates = [ball for ball, score in top_candidates]
        weights = [score for ball, score in top_candidates]
        
        if sum(weights) > 0:
            # 归一化权重
            normalized_weights = [w / sum(weights) for w in weights]
            predicted_ball = np.random.choice(candidates, p=normalized_weights)
        else:
            predicted_ball = candidates[0] if candidates else random.randint(1, 16)
        
        # 记录预测详情
        self.prediction_history.append({
            'prediction': predicted_ball,
            'final_scores': final_scores,
            'strategy_scores': {
                'cold_number_strategy': cold_scores[predicted_ball],
                'interval_strategy': interval_scores[predicted_ball],
                'frequency_balance': frequency_scores[predicted_ball],
                'simple_trend': trend_scores[predicted_ball]
            },
            'top_candidates': [(ball, score) for ball, score in sorted_balls[:5]],
            'timestamp': datetime.now().isoformat()
        })
        
        return predicted_ball
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 记录最近性能
        self.performance_stats['recent_performance'].append(1.0 if is_correct else 0.0)
        if len(self.performance_stats['recent_performance']) > 50:
            self.performance_stats['recent_performance'].pop(0)
        
        # 简单的权重调整
        if self.performance_stats['total'] >= 30 and self.performance_stats['total'] % 10 == 0:
            recent_hit_rate = np.mean(self.performance_stats['recent_performance'][-20:])
            
            if recent_hit_rate < 0.08:  # 如果最近表现不佳
                # 增加冷号策略的权重
                self.strategy_weights['cold_number_strategy'] = min(0.7, self.strategy_weights['cold_number_strategy'] * 1.1)
                self.strategy_weights['interval_strategy'] = max(0.1, self.strategy_weights['interval_strategy'] * 0.9)
                
                # 重新归一化
                total_weight = sum(self.strategy_weights.values())
                for key in self.strategy_weights:
                    self.strategy_weights[key] /= total_weight
    
    def get_performance_report(self):
        """获取性能报告"""
        recent_hit_rate = 0.0
        if self.performance_stats['recent_performance']:
            recent_hit_rate = np.mean(self.performance_stats['recent_performance'][-30:])
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'hit_rate': self.performance_stats['hit_rate'],
            'recent_hit_rate': recent_hit_rate,
            'strategy_weights': self.strategy_weights.copy()
        }

def test_optimized_blue_predictor():
    """测试优化蓝球预测器"""
    print("=== 优化蓝球预测器测试 ===")
    
    predictor = OptimizedBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(100, len(original_data) - 30)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 10 or i % 25 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    print(f"总预测次数: {report['total_predictions']}")
    print(f"正确预测次数: {report['correct_predictions']}")
    print(f"整体命中率: {report['hit_rate']:.4f}")
    print(f"最近命中率: {report['recent_hit_rate']:.4f}")
    
    print(f"\n策略权重配置:")
    for strategy, weight in report['strategy_weights'].items():
        print(f"  {strategy}: {weight:.3f}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"各策略得分:")
        for strategy, score in last_prediction['strategy_scores'].items():
            print(f"  {strategy}: {score:.4f}")
        
        print(f"\n候选球号排序:")
        for i, (ball, score) in enumerate(last_prediction['top_candidates']):
            print(f"  第{i+1}名: 球号{ball}, 得分{score:.4f}")
    
    return predictor

if __name__ == "__main__":
    test_optimized_blue_predictor()