#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级图神经网络+注意力机制双色球预测算法
结合GraphSAGE、多头注意力、外部数据源和动态图学习
目标：红球命中率 >= 1.5/6，蓝球命中率 >= 10%
"""

import json
import random
import math
import numpy as np
from typing import List, Dict, Tuple, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scikit-learn
try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.model_selection import GridSearchCV
    from sklearn.cluster import KMeans
    from sklearn.decomposition import PCA
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("scikit-learn不可用，使用简化版本")

class MultiHeadAttention:
    """多头注意力机制"""
    
    def __init__(self, d_model: int = 64, num_heads: int = 8):
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 权重矩阵
        self.W_q = np.random.randn(num_heads, d_model, self.d_k) * 0.1
        self.W_k = np.random.randn(num_heads, d_model, self.d_k) * 0.1
        self.W_v = np.random.randn(num_heads, d_model, self.d_k) * 0.1
        self.W_o = np.random.randn(d_model, d_model) * 0.1
        
        print(f"多头注意力初始化: {num_heads}头, 维度{d_model}")
    
    def forward(self, x: np.ndarray) -> np.ndarray:
        """前向传播"""
        batch_size, seq_len, _ = x.shape
        
        # 多头注意力
        heads = []
        for h in range(self.num_heads):
            # Q, K, V
            Q = np.dot(x, self.W_q[h])  # (batch, seq, d_k)
            K = np.dot(x, self.W_k[h])  # (batch, seq, d_k)
            V = np.dot(x, self.W_v[h])  # (batch, seq, d_k)
            
            # 注意力分数
            scores = np.matmul(Q, K.transpose(0, 2, 1)) / math.sqrt(self.d_k)
            attention_weights = self._softmax(scores)
            
            # 加权值
            head_output = np.matmul(attention_weights, V)
            heads.append(head_output)
        
        # 拼接多头
        concat_heads = np.concatenate(heads, axis=-1)
        
        # 输出投影
        output = np.dot(concat_heads, self.W_o)
        
        return output
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

class GraphSAGE:
    """GraphSAGE图神经网络"""
    
    def __init__(self, input_dim: int = 64, hidden_dim: int = 64, num_layers: int = 3):
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 权重矩阵
        self.W_layers = []
        for i in range(num_layers):
            in_dim = input_dim if i == 0 else hidden_dim
            W = np.random.randn(in_dim * 2, hidden_dim) * 0.1  # *2 for concatenation
            self.W_layers.append(W)
        
        # 输出层
        self.W_output = np.random.randn(hidden_dim, 1) * 0.1
        
        print(f"GraphSAGE初始化: {num_layers}层, 隐藏维度{hidden_dim}")
    
    def aggregate_neighbors(self, node_features: np.ndarray, adjacency: np.ndarray, 
                          node_idx: int, aggregation: str = 'mean') -> np.ndarray:
        """邻居聚合"""
        neighbors = np.where(adjacency[node_idx] > 0)[0]
        
        if len(neighbors) == 0:
            return np.zeros(node_features.shape[1])
        
        neighbor_features = node_features[neighbors]
        
        if aggregation == 'mean':
            return np.mean(neighbor_features, axis=0)
        elif aggregation == 'max':
            return np.max(neighbor_features, axis=0)
        elif aggregation == 'sum':
            return np.sum(neighbor_features, axis=0)
        else:
            return np.mean(neighbor_features, axis=0)
    
    def forward(self, node_features: np.ndarray, adjacency: np.ndarray) -> np.ndarray:
        """前向传播"""
        current_features = node_features.copy()
        
        for layer in range(self.num_layers):
            new_features = np.zeros((current_features.shape[0], self.hidden_dim))
            
            for node_idx in range(current_features.shape[0]):
                # 聚合邻居特征
                neighbor_agg = self.aggregate_neighbors(current_features, adjacency, node_idx)
                
                # 拼接自身特征和邻居特征
                concat_features = np.concatenate([current_features[node_idx], neighbor_agg])
                
                # 线性变换
                new_features[node_idx] = self._relu(np.dot(concat_features, self.W_layers[layer]))
            
            current_features = new_features
        
        return current_features
    
    def predict_probabilities(self, node_features: np.ndarray, adjacency: np.ndarray) -> np.ndarray:
        """预测概率"""
        hidden_features = self.forward(node_features, adjacency)
        
        # 输出层
        logits = np.dot(hidden_features, self.W_output).flatten()
        
        # Softmax
        probabilities = self._softmax(logits)
        
        return probabilities
    
    def _relu(self, x: np.ndarray) -> np.ndarray:
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)

class ExternalDataSource:
    """外部数据源模拟器"""
    
    def __init__(self):
        self.weather_patterns = ['晴', '雨', '阴', '雪', '雾']
        self.economic_indicators = ['上涨', '下跌', '平稳']
        self.social_events = ['节假日', '工作日', '周末']
        
        print("外部数据源初始化完成")
    
    def get_external_features(self, date_str: str) -> np.ndarray:
        """获取外部特征（模拟）"""
        features = np.zeros(20)
        
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d')
            
            # 天气特征（模拟）
            weather_idx = hash(date_str) % len(self.weather_patterns)
            features[weather_idx] = 1.0
            
            # 经济指标（模拟）
            economic_idx = (hash(date_str) // 7) % len(self.economic_indicators)
            features[5 + economic_idx] = 1.0
            
            # 社会事件（模拟）
            if date.weekday() >= 5:  # 周末
                features[10] = 1.0
            elif date.month in [1, 2, 10]:  # 节假日月份
                features[11] = 1.0
            else:  # 工作日
                features[12] = 1.0
            
            # 季节特征
            season = (date.month - 1) // 3
            features[13 + season] = 1.0
            
            # 月相特征（模拟）
            moon_phase = date.day % 4
            features[17 + moon_phase] = 1.0
            
        except:
            # 默认特征
            features[0] = 1.0
            features[5] = 1.0
            features[12] = 1.0
        
        return features

class DynamicGraphLearner:
    """动态图结构学习器"""
    
    def __init__(self, num_nodes: int = 49):
        self.num_nodes = num_nodes
        self.base_adjacency = self._create_base_adjacency()
        self.learned_weights = np.ones((num_nodes, num_nodes)) * 0.1
        
        print(f"动态图学习器初始化: {num_nodes}个节点")
    
    def _create_base_adjacency(self) -> np.ndarray:
        """创建基础邻接矩阵"""
        adj = np.zeros((self.num_nodes, self.num_nodes))
        
        # 红球之间的连接
        for i in range(33):
            for j in range(33):
                if i != j:
                    distance = abs(i - j)
                    if distance == 1:
                        adj[i, j] = 1.0
                    elif distance == 2:
                        adj[i, j] = 0.7
                    elif distance <= 5:
                        adj[i, j] = 0.3
                    elif distance <= 10:
                        adj[i, j] = 0.1
        
        # 蓝球之间的连接
        for i in range(33, 49):
            for j in range(33, 49):
                if i != j:
                    distance = abs(i - j)
                    if distance == 1:
                        adj[i, j] = 1.0
                    elif distance == 2:
                        adj[i, j] = 0.5
        
        # 红球和蓝球之间的连接
        for i in range(33):
            for j in range(33, 49):
                adj[i, j] = 0.05
                adj[j, i] = 0.05
        
        return adj
    
    def update_graph(self, historical_data: List[Dict]) -> np.ndarray:
        """根据历史数据更新图结构"""
        # 统计共现频率
        cooccurrence = np.zeros((self.num_nodes, self.num_nodes))
        
        for record in historical_data[-50:]:  # 最近50期
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                # 红球共现
                for i, num1 in enumerate(red_nums):
                    for j, num2 in enumerate(red_nums):
                        if i != j and 1 <= num1 <= 33 and 1 <= num2 <= 33:
                            cooccurrence[num1-1, num2-1] += 1
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                if 1 <= blue_num <= 16:
                    # 红球和蓝球的关联
                    if 'number' in record:
                        red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                        for red_num in red_nums:
                            if 1 <= red_num <= 33:
                                cooccurrence[red_num-1, 33+blue_num-1] += 1
                                cooccurrence[33+blue_num-1, red_num-1] += 1
        
        # 归一化共现矩阵
        max_cooccur = np.max(cooccurrence)
        if max_cooccur > 0:
            cooccurrence = cooccurrence / max_cooccur
        
        # 结合基础邻接矩阵和学习到的权重
        dynamic_adjacency = self.base_adjacency * 0.5 + cooccurrence * 0.3 + self.learned_weights * 0.2
        
        return dynamic_adjacency

class AdvancedGNNAttentionPredictor:
    """高级图神经网络+注意力预测器"""
    
    def __init__(self):
        # 图神经网络
        self.graphsage = GraphSAGE(input_dim=64, hidden_dim=64, num_layers=3)
        
        # 多头注意力
        self.attention = MultiHeadAttention(d_model=64, num_heads=8)
        
        # 外部数据源
        self.external_data = ExternalDataSource()
        
        # 动态图学习器
        self.graph_learner = DynamicGraphLearner()
        
        # 特征缩放器
        if SKLEARN_AVAILABLE:
            self.scaler = StandardScaler()
            self.feature_selector = SelectKBest(f_regression, k=30)
        else:
            self.scaler = None
            self.feature_selector = None
        
        # 集成学习器
        if SKLEARN_AVAILABLE:
            self.red_models = [
                RandomForestRegressor(n_estimators=200, max_depth=10, random_state=42),
                GradientBoostingRegressor(n_estimators=200, max_depth=6, random_state=42)
            ]
            self.blue_models = [
                RandomForestRegressor(n_estimators=200, max_depth=8, random_state=42),
                GradientBoostingRegressor(n_estimators=200, max_depth=5, random_state=42)
            ]
        else:
            self.red_models = None
            self.blue_models = None
        
        # 节点嵌入
        self.node_embeddings = np.random.randn(49, 64) * 0.1
        
        # 历史记录
        self.history = []
        self.is_trained = False
        
        print(f"高级图神经网络+注意力预测器初始化完成")
    
    def extract_comprehensive_features(self, data: List[Dict]) -> np.ndarray:
        """提取综合特征"""
        features = []
        
        if not data:
            return np.zeros(100)
        
        # 基础统计特征
        features.extend(self._extract_basic_stats(data))
        
        # 时间序列特征
        features.extend(self._extract_time_series_features(data))
        
        # 模式特征
        features.extend(self._extract_pattern_features(data))
        
        # 外部数据特征
        if data:
            last_date = data[-1].get('opendate', '2023-01-01')
            external_features = self.external_data.get_external_features(last_date)
            features.extend(external_features)
        else:
            features.extend(np.zeros(20))
        
        # 图特征
        features.extend(self._extract_graph_features(data))
        
        return np.array(features[:100])  # 限制特征数量
    
    def _extract_basic_stats(self, data: List[Dict]) -> List[float]:
        """提取基础统计特征"""
        features = []
        
        if len(data) < 5:
            return [0.0] * 20
        
        recent_data = data[-10:]
        red_nums_all = []
        blue_nums_all = []
        
        for record in recent_data:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                red_nums_all.extend(red_nums)
            
            if 'refernumber' in record:
                blue_nums_all.append(int(record['refernumber']))
        
        if red_nums_all:
            features.extend([
                np.mean(red_nums_all),
                np.std(red_nums_all),
                np.min(red_nums_all),
                np.max(red_nums_all),
                np.median(red_nums_all),
                len(set(red_nums_all)) / len(red_nums_all),  # 唯一性比例
                sum(1 for x in red_nums_all if x % 2 == 1) / len(red_nums_all),  # 奇数比例
                sum(1 for x in red_nums_all if x > 16.5) / len(red_nums_all),  # 大数比例
            ])
        else:
            features.extend([0.0] * 8)
        
        if blue_nums_all:
            features.extend([
                np.mean(blue_nums_all),
                np.std(blue_nums_all),
                np.min(blue_nums_all),
                np.max(blue_nums_all)
            ])
        else:
            features.extend([0.0] * 4)
        
        # 补充到20个特征
        while len(features) < 20:
            features.append(0.0)
        
        return features[:20]
    
    def _extract_time_series_features(self, data: List[Dict]) -> List[float]:
        """提取时间序列特征"""
        features = []
        
        if len(data) < 5:
            return [0.0] * 15
        
        # 红球和值序列
        red_sums = []
        for record in data[-15:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                red_sums.append(sum(red_nums))
        
        if len(red_sums) >= 3:
            # 趋势特征
            x = np.arange(len(red_sums))
            slope = np.polyfit(x, red_sums, 1)[0]
            features.append(slope)
            
            # 自相关
            if len(red_sums) >= 5:
                lag1_corr = np.corrcoef(red_sums[:-1], red_sums[1:])[0, 1]
                features.append(lag1_corr if not np.isnan(lag1_corr) else 0.0)
            else:
                features.append(0.0)
            
            # 变化率
            changes = [red_sums[i] - red_sums[i-1] for i in range(1, len(red_sums))]
            if changes:
                features.extend([
                    np.mean(changes),
                    np.std(changes),
                    np.min(changes),
                    np.max(changes)
                ])
            else:
                features.extend([0.0] * 4)
        else:
            features.extend([0.0] * 6)
        
        # 补充到15个特征
        while len(features) < 15:
            features.append(0.0)
        
        return features[:15]
    
    def _extract_pattern_features(self, data: List[Dict]) -> List[float]:
        """提取模式特征"""
        features = []
        
        if len(data) < 5:
            return [0.0] * 15
        
        # 连号分析
        consecutive_counts = []
        for record in data[-10:]:
            if 'number' in record:
                red_nums = sorted([int(x.strip()) for x in record['number'].split() if x.strip()])
                consecutive = 0
                for i in range(len(red_nums) - 1):
                    if red_nums[i+1] - red_nums[i] == 1:
                        consecutive += 1
                consecutive_counts.append(consecutive)
        
        if consecutive_counts:
            features.extend([
                np.mean(consecutive_counts),
                np.std(consecutive_counts),
                np.max(consecutive_counts)
            ])
        else:
            features.extend([0.0] * 3)
        
        # 重号分析
        repeat_counts = []
        for i in range(1, min(len(data), 10)):
            if 'number' in data[-i] and 'number' in data[-i-1]:
                curr_red = set([int(x.strip()) for x in data[-i]['number'].split() if x.strip()])
                prev_red = set([int(x.strip()) for x in data[-i-1]['number'].split() if x.strip()])
                repeat_counts.append(len(curr_red & prev_red))
        
        if repeat_counts:
            features.extend([
                np.mean(repeat_counts),
                np.std(repeat_counts),
                np.max(repeat_counts)
            ])
        else:
            features.extend([0.0] * 3)
        
        # 补充到15个特征
        while len(features) < 15:
            features.append(0.0)
        
        return features[:15]
    
    def _extract_graph_features(self, data: List[Dict]) -> List[float]:
        """提取图特征"""
        features = []
        
        if len(data) < 5:
            return [0.0] * 30
        
        # 更新动态图
        dynamic_adj = self.graph_learner.update_graph(data)
        
        # 节点度数
        node_degrees = np.sum(dynamic_adj > 0.1, axis=1)
        features.extend([
            np.mean(node_degrees[:33]),  # 红球平均度数
            np.std(node_degrees[:33]),   # 红球度数标准差
            np.mean(node_degrees[33:49]), # 蓝球平均度数
            np.std(node_degrees[33:49])   # 蓝球度数标准差
        ])
        
        # 图密度
        red_density = np.sum(dynamic_adj[:33, :33] > 0.1) / (33 * 32)
        blue_density = np.sum(dynamic_adj[33:49, 33:49] > 0.1) / (16 * 15)
        features.extend([red_density, blue_density])
        
        # 补充到30个特征
        while len(features) < 30:
            features.append(0.0)
        
        return features[:30]
    
    def train(self, data: List[Dict], train_size: int = 300):
        """训练模型"""
        if len(data) < train_size + 50:
            print("数据量不足，跳过训练")
            return
        
        print("开始训练高级图神经网络+注意力模型...")
        
        train_data = data[:train_size]
        
        # 准备训练数据
        X_features = []
        y_red_sums = []
        y_blue = []
        
        for i in range(30, len(train_data)):
            # 特征
            current_data = train_data[:i]
            features = self.extract_comprehensive_features(current_data)
            X_features.append(features)
            
            # 目标
            if 'number' in train_data[i]:
                red_nums = [int(x.strip()) for x in train_data[i]['number'].split() if x.strip()]
                y_red_sums.append(sum(red_nums))
            else:
                y_red_sums.append(100)
            
            if 'refernumber' in train_data[i]:
                blue_num = int(train_data[i]['refernumber'])
                y_blue.append(blue_num)
            else:
                y_blue.append(8)
        
        X_features = np.array(X_features)
        y_red_sums = np.array(y_red_sums)
        y_blue = np.array(y_blue)
        
        # 特征预处理
        if SKLEARN_AVAILABLE and self.scaler:
            try:
                X_scaled = self.scaler.fit_transform(X_features)
                
                # 特征选择
                if self.feature_selector:
                    X_selected = self.feature_selector.fit_transform(X_scaled, y_red_sums)
                else:
                    X_selected = X_scaled
                
                # 训练模型
                if self.red_models:
                    for model in self.red_models:
                        model.fit(X_selected, y_red_sums)
                
                if self.blue_models:
                    for model in self.blue_models:
                        model.fit(X_selected, y_blue)
                
                self.is_trained = True
                print("模型训练完成")
                
            except Exception as e:
                print(f"模型训练失败: {e}")
        else:
            print("使用简化版本")
            self.is_trained = True
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """预测"""
        if len(data) < 10:
            return self._fallback_predict()
        
        try:
            # 图神经网络预测
            gnn_red, gnn_blue = self._gnn_predict(data)
            
            # 注意力机制预测
            att_red, att_blue = self._attention_predict(data)
            
            # 集成学习预测
            ml_red, ml_blue = self._ml_predict(data)
            
            # 融合预测结果
            final_red = self._ensemble_predictions([gnn_red, att_red, ml_red], 'red')
            final_blue = self._ensemble_predictions([gnn_blue, att_blue, ml_blue], 'blue')
            
            return final_red, final_blue
            
        except Exception as e:
            print(f"预测失败: {e}，使用回退策略")
            return self._fallback_predict()
    
    def _gnn_predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """图神经网络预测"""
        # 更新动态图
        dynamic_adj = self.graph_learner.update_graph(data)
        
        # 更新节点特征
        node_features = self._update_node_features(data)
        
        # GraphSAGE预测
        probabilities = self.graphsage.predict_probabilities(node_features, dynamic_adj)
        
        # 红球预测
        red_probs = probabilities[:33]
        red_indices = np.argsort(red_probs)[-6:]
        red_pred = sorted([i + 1 for i in red_indices])
        
        # 蓝球预测
        blue_probs = probabilities[33:49]
        blue_idx = np.argmax(blue_probs)
        blue_pred = [blue_idx + 1]
        
        return red_pred, blue_pred
    
    def _attention_predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """注意力机制预测"""
        # 构造序列特征
        sequence_features = []
        for record in data[-20:]:
            features = np.zeros(64)
            
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                for i, num in enumerate(red_nums[:6]):
                    if i < 6:
                        features[i] = num / 33.0  # 归一化
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                features[6] = blue_num / 16.0  # 归一化
            
            sequence_features.append(features)
        
        # 补充序列长度
        while len(sequence_features) < 20:
            sequence_features.insert(0, np.zeros(64))
        
        sequence_features = np.array(sequence_features).reshape(1, 20, 64)
        
        # 注意力预测
        attention_output = self.attention.forward(sequence_features)
        
        # 从注意力输出生成预测
        final_output = np.mean(attention_output, axis=(0, 1))
        
        # 红球预测
        red_scores = final_output[:33] if len(final_output) >= 33 else np.random.rand(33)
        red_indices = np.argsort(red_scores)[-6:]
        red_pred = sorted([i + 1 for i in red_indices])
        
        # 蓝球预测
        blue_scores = final_output[33:49] if len(final_output) >= 49 else np.random.rand(16)
        blue_idx = np.argmax(blue_scores)
        blue_pred = [blue_idx + 1]
        
        return red_pred, blue_pred
    
    def _ml_predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """机器学习预测"""
        if not self.is_trained or not SKLEARN_AVAILABLE:
            return self._fallback_predict()
        
        # 提取特征
        features = self.extract_comprehensive_features(data)
        
        try:
            # 特征预处理
            features_scaled = self.scaler.transform(features.reshape(1, -1))
            
            if self.feature_selector:
                features_selected = self.feature_selector.transform(features_scaled)
            else:
                features_selected = features_scaled
            
            # 红球预测（预测和值）
            red_sum_predictions = []
            for model in self.red_models:
                pred_sum = model.predict(features_selected)[0]
                red_sum_predictions.append(pred_sum)
            
            avg_sum = np.mean(red_sum_predictions)
            red_pred = self._generate_red_from_sum(avg_sum)
            
            # 蓝球预测
            blue_predictions = []
            for model in self.blue_models:
                pred_blue = model.predict(features_selected)[0]
                blue_predictions.append(pred_blue)
            
            avg_blue = np.mean(blue_predictions)
            blue_pred = [max(1, min(16, int(round(avg_blue))))]
            
            return red_pred, blue_pred
            
        except Exception as e:
            print(f"机器学习预测失败: {e}")
            return self._fallback_predict()
    
    def _update_node_features(self, data: List[Dict]) -> np.ndarray:
        """更新节点特征"""
        features = np.copy(self.node_embeddings)
        
        # 基于历史数据更新特征
        red_freq = np.zeros(33)
        blue_freq = np.zeros(16)
        
        for record in data[-30:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                for num in red_nums:
                    if 1 <= num <= 33:
                        red_freq[num-1] += 1
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                if 1 <= blue_num <= 16:
                    blue_freq[blue_num-1] += 1
        
        # 归一化频率
        red_freq = red_freq / (np.sum(red_freq) + 1e-6)
        blue_freq = blue_freq / (np.sum(blue_freq) + 1e-6)
        
        # 更新节点特征
        for i in range(33):
            features[i, 0] = red_freq[i]
            features[i, 1] = i / 33.0  # 位置编码
        
        for i in range(16):
            features[33 + i, 0] = blue_freq[i]
            features[33 + i, 1] = i / 16.0  # 位置编码
        
        return features
    
    def _generate_red_from_sum(self, target_sum: float) -> List[int]:
        """根据目标和值生成红球组合"""
        target_sum = max(21, min(183, target_sum))
        
        best_combination = None
        best_diff = float('inf')
        
        for _ in range(200):  # 增加尝试次数
            combination = sorted(random.sample(range(1, 34), 6))
            current_sum = sum(combination)
            diff = abs(current_sum - target_sum)
            
            if diff < best_diff:
                best_diff = diff
                best_combination = combination
        
        return best_combination or sorted(random.sample(range(1, 34), 6))
    
    def _ensemble_predictions(self, predictions: List[List[int]], pred_type: str) -> List[int]:
        """集成预测结果"""
        if pred_type == 'red':
            # 红球投票机制
            vote_count = defaultdict(int)
            
            for pred in predictions:
                for num in pred:
                    vote_count[num] += 1
            
            # 选择得票最多的6个
            sorted_votes = sorted(vote_count.items(), key=lambda x: x[1], reverse=True)
            result = [num for num, _ in sorted_votes[:6]]
            
            # 补充不足的
            while len(result) < 6:
                candidate = random.randint(1, 33)
                if candidate not in result:
                    result.append(candidate)
            
            return sorted(result)
        
        else:  # blue
            # 蓝球简单平均
            blue_nums = [pred[0] for pred in predictions]
            avg_blue = np.mean(blue_nums)
            return [max(1, min(16, int(round(avg_blue))))]
    
    def _fallback_predict(self) -> Tuple[List[int], List[int]]:
        """回退预测"""
        red_pred = sorted(random.sample(range(1, 34), 6))
        blue_pred = [random.randint(1, 16)]
        return red_pred, blue_pred
    
    def load_data(self, file_path: str = 'ssq_data.json') -> List[Dict]:
        """加载数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条历史数据")
            return data
        except FileNotFoundError:
            print(f"数据文件 {file_path} 不存在")
            return []
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []

def advanced_gnn_attention_backtest(data: List[Dict], test_periods: int = 50) -> Dict:
    """高级图神经网络+注意力算法回测"""
    if len(data) < test_periods + 150:
        print(f"数据量不足，需要至少 {test_periods + 150} 条数据")
        return {}
    
    predictor = AdvancedGNNAttentionPredictor()
    
    # 训练模型
    train_data = data[:-test_periods]
    predictor.train(train_data)
    
    # 回测结果
    results = {
        'total_tests': test_periods,
        'red_hits': [],
        'blue_hits': [],
        'predictions': [],
        'best_records': []
    }
    
    print(f"\n开始高级图神经网络+注意力算法回测，测试期数: {test_periods}")
    
    for i in range(test_periods):
        # 训练数据（不包括当前测试期）
        train_data = data[:-(test_periods-i)]
        
        # 预测
        pred_red, pred_blue = predictor.predict(train_data)
        
        # 实际结果
        test_record = data[-(test_periods-i)]
        
        if 'number' in test_record:
            actual_red = [int(x.strip()) for x in test_record['number'].split() if x.strip()]
        else:
            actual_red = [1, 2, 3, 4, 5, 6]
        
        if 'refernumber' in test_record:
            actual_blue = [int(test_record['refernumber'])]
        else:
            actual_blue = [1]
        
        # 计算命中
        red_hit = len(set(pred_red) & set(actual_red))
        blue_hit = 1 if pred_blue[0] == actual_blue[0] else 0
        
        results['red_hits'].append(red_hit)
        results['blue_hits'].append(blue_hit)
        results['predictions'].append({
            'date': test_record.get('opendate', f'期号{i+1}'),
            'pred_red': pred_red,
            'pred_blue': pred_blue,
            'actual_red': actual_red,
            'actual_blue': actual_blue,
            'red_hit': red_hit,
            'blue_hit': blue_hit
        })
        
        # 记录最佳预测
        if red_hit >= 2 or blue_hit >= 1:
            results['best_records'].append({
                'date': test_record.get('opendate', f'期号{i+1}'),
                'red_hit': red_hit,
                'blue_hit': blue_hit,
                'total_score': red_hit * 10 + blue_hit * 50
            })
    
    # 计算统计结果
    avg_red_hit = np.mean(results['red_hits'])
    blue_hit_rate = np.mean(results['blue_hits']) * 100
    
    # 目标达成度
    red_target_achievement = (avg_red_hit / 1.5) * 100
    blue_target_achievement = (blue_hit_rate / 10) * 100
    overall_achievement = (red_target_achievement + blue_target_achievement) / 2
    
    results.update({
        'avg_red_hit': avg_red_hit,
        'blue_hit_rate': blue_hit_rate,
        'red_target_achievement': red_target_achievement,
        'blue_target_achievement': blue_target_achievement,
        'overall_achievement': overall_achievement
    })
    
    return results

def print_advanced_gnn_attention_results(results: Dict):
    """打印高级图神经网络+注意力结果"""
    if not results:
        print("没有回测结果")
        return
    
    print(f"\n=== 高级图神经网络+注意力算法回测结果 ===")
    print(f"测试期数: {results['total_tests']}")
    print(f"红球平均命中: {results['avg_red_hit']:.2f}/6 ({results['avg_red_hit']/6*100:.1f}%)")
    print(f"蓝球命中率: {results['blue_hit_rate']:.1f}%")
    print(f"目标达成度: 红球 {results['red_target_achievement']:.1f}%, 蓝球 {results['blue_target_achievement']:.1f}%")
    print(f"综合达成度: {results['overall_achievement']:.1f}%")
    
    # 最佳预测记录
    if results['best_records']:
        print(f"\n前15期最佳预测记录:")
        sorted_records = sorted(results['best_records'], 
                              key=lambda x: x['total_score'], reverse=True)[:15]
        for record in sorted_records:
            print(f"{record['date']}: 红球 {record['red_hit']}/6, 蓝球 {record['blue_hit']}/1")
    
    # 红球命中分布
    red_distribution = defaultdict(int)
    for hit in results['red_hits']:
        red_distribution[hit] += 1
    
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_distribution[i]
        percentage = count / results['total_tests'] * 100
        print(f"{i}个: {count}次 ({percentage:.1f}%)")
    
    print(f"\n💡 高级图神经网络+注意力优化建议:")
    print(f"1. 实现更复杂的图注意力网络（GAT）")
    print(f"2. 引入Transformer架构进行序列建模")
    print(f"3. 使用强化学习优化预测策略")
    print(f"4. 实现多任务学习同时预测红球和蓝球")
    print(f"5. 集成更多外部数据源和实时信息")

if __name__ == "__main__":
    # 创建预测器
    predictor = AdvancedGNNAttentionPredictor()
    
    # 加载数据
    data = predictor.load_data()
    
    if data:
        # 回测
        backtest_results = advanced_gnn_attention_backtest(data, test_periods=50)
        
        # 打印结果
        print_advanced_gnn_attention_results(backtest_results)
    else:
        print("无法加载数据，退出程序")