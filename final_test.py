import json
from main import smart_get_ssq_data, predict_ssq_7red_1blue

print("=== 最终验证测试 ===\n")

# 先删除最后2期数据
with open('ssq_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

original_data = data.copy()
data_998 = data[2:]
with open('ssq_data.json', 'w', encoding='utf-8') as f:
    json.dump(data_998, f, ensure_ascii=False, indent=2)

print("1. 使用998期数据进行预测：")
data1 = smart_get_ssq_data(1000)  # 这会自动补全数据
print(f"数据量: {len(data1)}, 最新期号: {data1[0]['issueno']}")
prediction1 = predict_ssq_7red_1blue()
print(f"预测结果1: 红球 {prediction1['red']}, 蓝球 {prediction1['blue']}")
print(f"预测结果1结构: {list(prediction1.keys())}")
if 'red_confidence' in prediction1:
    print(f"置信度1: 红球 {prediction1['red_confidence']:.3f}, 蓝球 {prediction1['blue_confidence']:.3f}")
else:
    print("置信度1: 未提供置信度信息")

# 再次删除数据，模拟不同的起始状态
data_997 = data[3:]
with open('ssq_data.json', 'w', encoding='utf-8') as f:
    json.dump(data_997, f, ensure_ascii=False, indent=2)

print("\n2. 使用997期数据进行预测：")
data2 = smart_get_ssq_data(1000)  # 这会自动补全数据
print(f"数据量: {len(data2)}, 最新期号: {data2[0]['issueno']}")
prediction2 = predict_ssq_7red_1blue()
print(f"预测结果2: 红球 {prediction2['red']}, 蓝球 {prediction2['blue']}")
print(f"预测结果2结构: {list(prediction2.keys())}")
if 'red_confidence' in prediction2:
    print(f"置信度2: 红球 {prediction2['red_confidence']:.3f}, 蓝球 {prediction2['blue_confidence']:.3f}")
else:
    print("置信度2: 未提供置信度信息")

# 恢复原始数据
with open('ssq_data.json', 'w', encoding='utf-8') as f:
    json.dump(original_data, f, ensure_ascii=False, indent=2)

# 比较结果
print("\n3. 比较结果：")
red_same = prediction1['red'] == prediction2['red']
blue_same = prediction1['blue'] == prediction2['blue']

# 检查是否有置信度信息
confidence_diff = False
if 'red_confidence' in prediction1 and 'red_confidence' in prediction2:
    confidence_diff = abs(prediction1['red_confidence'] - prediction2['red_confidence']) > 0.01

if red_same and blue_same and not confidence_diff:
    print("⚠️ 预测结果完全相同")
else:
    print("✓ 预测结果有差异：")
    if not red_same:
        print(f"  - 红球不同: {prediction1['red']} vs {prediction2['red']}")
    if not blue_same:
        print(f"  - 蓝球不同: {prediction1['blue']} vs {prediction2['blue']}")
    if confidence_diff and 'red_confidence' in prediction1:
        print(f"  - 置信度不同: {prediction1['red_confidence']:.3f} vs {prediction2['red_confidence']:.3f}")

print("\n=== 测试完成 ===")