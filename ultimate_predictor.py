#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球终极集成预测系统
融合所有算法优势的最终预测程序
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import requests
import warnings
warnings.filterwarnings('ignore')

class UltimateSSQPredictor:
    """双色球终极集成预测器"""
    
    def __init__(self, data_file='ssq_data.json'):
        self.data_file = data_file
        self.data = self.load_and_update_data()
        self.red_range = list(range(1, 34))
        self.blue_range = list(range(1, 17))
        
        # 集成学习权重
        self.ensemble_weights = {
            'frequency': 0.20,
            'position': 0.15,
            'interval': 0.15,
            'trend': 0.15,
            'neural': 0.20,
            'quantum': 0.15
        }
        
        # 动态策略权重
        self.strategy_performance = {
            'conservative': {'score': 0.6, 'weight': 0.3},
            'balanced': {'score': 0.7, 'weight': 0.4},
            'aggressive': {'score': 0.65, 'weight': 0.3}
        }
        
        # 神经网络权重（简化版）
        # 使用数据相关的种子，让预测结果随数据变化
        data_seed = len(self.data) + hash(str(self.data[0] if self.data else {})) % 1000
        np.random.seed(data_seed)
        self.neural_weights = np.random.randn(8, 16) * 0.1
        self.neural_hidden = np.random.randn(16, 8) * 0.1
        self.neural_output = np.random.randn(8, 1) * 0.1
        
        # 蓝球专用神经网络
        self.blue_neural_weights = np.random.randn(8, 12) * 0.1
        self.blue_hidden_weights = np.random.randn(12, 8) * 0.1
        self.blue_output_weights = np.random.randn(8, 1) * 0.1
        
        print("🚀 终极集成预测器初始化完成")
    
    def load_and_update_data(self):
        """加载并更新数据"""
        try:
            # 尝试从本地加载
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换数据格式
            formatted_data = []
            for entry in data:
                red_str = entry['number'].split(' ')
                red = sorted([int(x) for x in red_str])
                blue = int(entry['refernumber'])
                formatted_data.append({'red': red, 'blue': blue})
            
            print(f"✅ 成功加载 {len(formatted_data)} 期历史数据")
            return formatted_data
            
        except Exception as e:
            print(f"⚠️ 数据加载失败: {e}")
            return []
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def enhanced_data_analysis(self):
        """增强型数据分析"""
        if not self.data:
            return self._default_analysis()
        
        analysis = {
            'red_frequency': defaultdict(int),
            'blue_frequency': defaultdict(int),
            'red_position_freq': [defaultdict(int) for _ in range(6)],
            'red_intervals': defaultdict(list),
            'blue_intervals': defaultdict(list),
            'red_hot_trend': defaultdict(list),
            'blue_hot_trend': defaultdict(list),
            'red_distribution': {'low': 0, 'mid': 0, 'high': 0},
            'blue_distribution': {'low': 0, 'mid': 0, 'high': 0},
            'time_series_red': [],
            'time_series_blue': []
        }
        
        # 统计频率和位置
        for i, record in enumerate(self.data):
            red_balls = record['red']
            blue_ball = record['blue']
            
            # 红球统计
            for j, ball in enumerate(red_balls):
                analysis['red_frequency'][ball] += 1
                if j < 6:  # 确保不超出位置范围
                    analysis['red_position_freq'][j][ball] += 1
                analysis['time_series_red'].append(ball)
            
            # 蓝球统计
            analysis['blue_frequency'][blue_ball] += 1
            analysis['time_series_blue'].append(blue_ball)
            
            # 分布统计
            low_count = sum(1 for ball in red_balls if ball <= 11)
            mid_count = sum(1 for ball in red_balls if 12 <= ball <= 22)
            high_count = sum(1 for ball in red_balls if ball >= 23)
            
            analysis['red_distribution']['low'] += low_count
            analysis['red_distribution']['mid'] += mid_count
            analysis['red_distribution']['high'] += high_count
            
            if blue_ball <= 5:
                analysis['blue_distribution']['low'] += 1
            elif blue_ball <= 10:
                analysis['blue_distribution']['mid'] += 1
            else:
                analysis['blue_distribution']['high'] += 1
        
        # 计算间隔和趋势
        self._calculate_intervals(analysis)
        self._calculate_trends(analysis)
        
        return analysis
    
    def _calculate_intervals(self, analysis):
        """计算号码间隔"""
        # 红球间隔
        for ball in self.red_range:
            last_appear = -1
            for i, record in enumerate(self.data):
                if ball in record['red']:
                    if last_appear >= 0:
                        analysis['red_intervals'][ball].append(i - last_appear)
                    last_appear = i
        
        # 蓝球间隔
        for ball in self.blue_range:
            last_appear = -1
            for i, record in enumerate(self.data):
                if ball == record['blue']:
                    if last_appear >= 0:
                        analysis['blue_intervals'][ball].append(i - last_appear)
                    last_appear = i
    
    def _calculate_trends(self, analysis):
        """计算热度趋势"""
        recent_periods = min(30, len(self.data))
        if recent_periods == 0:
            return
        
        recent_data = self.data[-recent_periods:]
        
        # 红球趋势
        for ball in self.red_range:
            for record in recent_data:
                if ball in record['red']:
                    analysis['red_hot_trend'][ball].append(1)
        
        # 蓝球趋势
        for ball in self.blue_range:
            for record in recent_data:
                if ball == record['blue']:
                    analysis['blue_hot_trend'][ball].append(1)
    
    def neural_network_prediction(self, features):
        """神经网络预测"""
        try:
            # 输入层到隐藏层
            hidden = self.sigmoid(np.dot(features, self.neural_weights))
            # 隐藏层到输出层
            hidden2 = np.tanh(np.dot(hidden, self.neural_hidden))
            # 输出层
            output = self.sigmoid(np.dot(hidden2, self.neural_output))
            return float(output[0])
        except:
            return 0.5
    
    def blue_neural_network_prediction(self, features):
        """蓝球专用神经网络预测"""
        try:
            # 输入层到隐藏层
            hidden = self.sigmoid(np.dot(features, self.blue_neural_weights))
            # 隐藏层到输出层
            hidden2 = np.tanh(np.dot(hidden, self.blue_hidden_weights))
            # 输出层
            output = self.sigmoid(np.dot(hidden2, self.blue_output_weights))
            return float(output[0])
        except:
            return 0.5
    
    def quantum_inspired_fusion(self, scores_dict):
        """量子启发的特征融合"""
        quantum_scores = {}
        
        for ball in self.red_range:
            # 量子叠加态
            amplitudes = []
            for feature, weight in self.ensemble_weights.items():
                if feature in scores_dict and ball in scores_dict[feature]:
                    amplitude = math.sqrt(max(0, scores_dict[feature][ball] * weight))
                    amplitudes.append(amplitude)
            
            if amplitudes:
                # 量子干涉
                interference = sum(amplitudes) / len(amplitudes)
                # 概率密度
                probability = interference ** 2
                quantum_scores[ball] = probability
            else:
                quantum_scores[ball] = 0.1
        
        return quantum_scores
    
    def calculate_red_scores_ultimate(self, analysis):
        """终极红球得分计算"""
        scores = {
            'frequency': {},
            'position': {},
            'interval': {},
            'trend': {},
            'neural': {},
            'quantum': {}
        }
        
        # 1. 频率得分
        max_freq = max(analysis['red_frequency'].values()) if analysis['red_frequency'] else 1
        for ball in self.red_range:
            freq = analysis['red_frequency'].get(ball, 0)
            scores['frequency'][ball] = freq / max_freq
        
        # 2. 位置得分
        for ball in self.red_range:
            position_score = 0
            for pos in range(6):
                if ball in analysis['red_position_freq'][pos]:
                    position_score += analysis['red_position_freq'][pos][ball]
            scores['position'][ball] = position_score / max(1, len(self.data))
        
        # 3. 间隔得分
        for ball in self.red_range:
            intervals = analysis['red_intervals'].get(ball, [10])
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                scores['interval'][ball] = 1 / (1 + avg_interval * 0.1)
            else:
                scores['interval'][ball] = 0.1
        
        # 4. 趋势得分
        for ball in self.red_range:
            trend_count = len(analysis['red_hot_trend'].get(ball, []))
            scores['trend'][ball] = trend_count / 30
        
        # 5. 神经网络得分
        for ball in self.red_range:
            features = np.array([
                scores['frequency'][ball],
                scores['position'][ball],
                scores['interval'][ball],
                scores['trend'][ball],
                analysis['red_distribution']['low'] / max(1, len(self.data)),
                analysis['red_distribution']['mid'] / max(1, len(self.data)),
                analysis['red_distribution']['high'] / max(1, len(self.data)),
                random.random() * 0.1
            ])
            scores['neural'][ball] = self.neural_network_prediction(features)
        
        # 6. 量子融合得分
        scores['quantum'] = self.quantum_inspired_fusion(scores)
        
        return scores
    
    def calculate_blue_scores_ultimate(self, analysis):
        """终极蓝球得分计算"""
        blue_scores = {}
        
        max_freq = max(analysis['blue_frequency'].values()) if analysis['blue_frequency'] else 1
        
        for ball in self.blue_range:
            # 频率得分
            freq_score = analysis['blue_frequency'].get(ball, 0) / max_freq
            
            # 间隔得分
            intervals = analysis['blue_intervals'].get(ball, [8])
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                interval_score = 1 / (1 + avg_interval * 0.1)
            else:
                interval_score = 0.1
            
            # 趋势得分
            trend_count = len(analysis['blue_hot_trend'].get(ball, []))
            trend_score = trend_count / 30
            
            # 分布得分
            total_periods = max(1, len(self.data))
            if ball <= 5:
                dist_score = analysis['blue_distribution']['low'] / total_periods
            elif ball <= 10:
                dist_score = analysis['blue_distribution']['mid'] / total_periods
            else:
                dist_score = analysis['blue_distribution']['high'] / total_periods
            
            # 神经网络特征
            features = np.array([
                freq_score, interval_score, trend_score, dist_score,
                math.sin(ball * math.pi / 16),
                math.cos(ball * math.pi / 16),
                ball / 16,
                random.random() * 0.05
            ])
            
            # 深度神经网络预测
            neural_score = self.blue_neural_network_prediction(features)
            
            # 时序注意力
            recent_blues = analysis['time_series_blue'][-20:]
            attention_score = recent_blues.count(ball) / len(recent_blues) if recent_blues else 0
            
            # 综合得分
            blue_scores[ball] = (
                freq_score * 0.25 +
                interval_score * 0.20 +
                trend_score * 0.15 +
                neural_score * 0.25 +
                attention_score * 0.15
            )
        
        return blue_scores
    
    def dynamic_strategy_selection(self, red_scores, analysis):
        """动态策略选择"""
        strategies = {
            'conservative': self.conservative_selection,
            'balanced': self.balanced_selection,
            'aggressive': self.aggressive_selection
        }
        
        # 策略权重归一化
        total_weight = sum(s['weight'] for s in self.strategy_performance.values())
        normalized_weights = {k: v['weight']/total_weight for k, v in self.strategy_performance.items()}
        
        # 加权组合选择
        final_selection = set()
        for strategy_name, weight in normalized_weights.items():
            strategy_func = strategies[strategy_name]
            selection = strategy_func(red_scores, analysis)
            num_to_add = max(1, int(7 * weight))
            final_selection.update(list(selection)[:num_to_add])
        
        # 确保选择7个球
        if len(final_selection) < 7:
            remaining = set(self.red_range) - final_selection
            sorted_remaining = sorted(remaining, key=lambda x: sum(red_scores[feature].get(x, 0) 
                                                                 for feature in red_scores), reverse=True)
            final_selection.update(sorted_remaining[:7-len(final_selection)])
        elif len(final_selection) > 7:
            scored_selection = [(ball, sum(red_scores[feature].get(ball, 0) for feature in red_scores)) 
                              for ball in final_selection]
            scored_selection.sort(key=lambda x: x[1], reverse=True)
            final_selection = set([ball for ball, _ in scored_selection[:7]])
        
        return list(final_selection)
    
    def conservative_selection(self, red_scores, analysis):
        """保守策略"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = (
                red_scores['frequency'].get(ball, 0) * 0.4 +
                red_scores['position'].get(ball, 0) * 0.3 +
                red_scores['trend'].get(ball, 0) * 0.3
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def balanced_selection(self, red_scores, analysis):
        """平衡策略"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = sum(
                red_scores[feature].get(ball, 0) * weight 
                for feature, weight in self.ensemble_weights.items()
                if feature in red_scores
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def aggressive_selection(self, red_scores, analysis):
        """激进策略"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = (
                red_scores['neural'].get(ball, 0) * 0.4 +
                red_scores['quantum'].get(ball, 0) * 0.3 +
                red_scores['interval'].get(ball, 0) * 0.3
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def select_blue_ball_ultimate(self, blue_scores, analysis):
        """终极蓝球选择"""
        # 多模型融合预测
        predictions = []
        
        # 模型1：直接得分排序
        sorted_blues = sorted(blue_scores.items(), key=lambda x: x[1], reverse=True)
        predictions.append(sorted_blues[0][0])
        
        # 模型2：随机森林思想
        top_candidates = [ball for ball, _ in sorted_blues[:5]]
        weights = [score for _, score in sorted_blues[:5]]
        if sum(weights) > 0:
            normalized_weights = [w/sum(weights) for w in weights]
            predictions.append(np.random.choice(top_candidates, p=normalized_weights))
        
        # 模型3：时序模式匹配
        recent_blues = analysis['time_series_blue'][-10:]
        pattern_scores = defaultdict(float)
        for i in range(len(analysis['time_series_blue']) - 10):
            if i + 10 < len(analysis['time_series_blue']):
                historical_pattern = analysis['time_series_blue'][i:i+10]
                similarity = sum(1 for a, b in zip(recent_blues, historical_pattern) if a == b)
                if similarity >= 3:
                    next_blue = analysis['time_series_blue'][i+10]
                    pattern_scores[next_blue] += similarity
        
        if pattern_scores:
            best_pattern_blue = max(pattern_scores.items(), key=lambda x: x[1])[0]
            predictions.append(best_pattern_blue)
        
        # 投票选择
        if predictions:
            vote_count = Counter(predictions)
            final_blue = vote_count.most_common(1)[0][0]
        else:
            final_blue = sorted_blues[0][0]
        
        return final_blue
    
    def _default_analysis(self):
        """默认分析（当没有数据时）"""
        return {
            'red_frequency': defaultdict(int),
            'blue_frequency': defaultdict(int),
            'red_position_freq': [defaultdict(int) for _ in range(6)],
            'red_intervals': defaultdict(list),
            'blue_intervals': defaultdict(list),
            'red_hot_trend': defaultdict(list),
            'blue_hot_trend': defaultdict(list),
            'red_distribution': {'low': 33, 'mid': 33, 'high': 34},
            'blue_distribution': {'low': 33, 'mid': 33, 'high': 34},
            'time_series_red': [],
            'time_series_blue': []
        }
    
    def predict(self):
        """终极预测方法"""
        print("\n=== 🎯 终极集成预测开始 ===")
        
        # 数据分析
        analysis = self.enhanced_data_analysis()
        print("✓ 增强型数据分析完成")
        
        # 红球得分计算
        red_scores = self.calculate_red_scores_ultimate(analysis)
        print("✓ 终极红球得分计算完成")
        
        # 蓝球得分计算
        blue_scores = self.calculate_blue_scores_ultimate(analysis)
        print("✓ 终极蓝球得分计算完成")
        
        # 动态策略选择
        selected_reds = self.dynamic_strategy_selection(red_scores, analysis)
        print(f"✓ 动态策略选择完成，选中红球: {sorted(selected_reds)}")
        
        # 蓝球选择
        selected_blue = self.select_blue_ball_ultimate(blue_scores, analysis)
        print(f"✓ 终极蓝球选择完成，选中蓝球: {selected_blue}")
        
        # 预测结果
        prediction = {
            'red': sorted(selected_reds),
            'blue': selected_blue,
            'confidence': {
                'red_avg_score': np.mean([sum(red_scores[f].get(ball, 0) for f in red_scores) for ball in selected_reds]),
                'blue_score': blue_scores[selected_blue],
                'strategy_confidence': max(s['score'] for s in self.strategy_performance.values())
            },
            'analysis_summary': {
                'total_periods': len(self.data),
                'red_distribution': dict(analysis['red_distribution']),
                'blue_distribution': dict(analysis['blue_distribution']),
                'ensemble_weights': self.ensemble_weights,
                'strategy_weights': {k: v['weight'] for k, v in self.strategy_performance.items()}
            }
        }
        
        return prediction
    
    def format_output(self, prediction):
        """格式化输出预测结果"""
        print("\n" + "="*70)
        print("🎯 双色球终极集成预测系统 🎯")
        print("="*70)
        
        # 预测号码
        red_balls = ' '.join([f"{num:02d}" for num in prediction['red']])
        blue_ball = f"{prediction['blue']:02d}"
        
        print(f"\n🔴 红球预测: {red_balls}")
        print(f"🔵 蓝球预测: {blue_ball}")
        print(f"\n📊 预测组合: {red_balls} + {blue_ball}")
        
        # 置信度信息
        conf = prediction['confidence']
        print(f"\n📈 置信度分析:")
        print(f"   红球平均得分: {conf['red_avg_score']:.3f}")
        print(f"   蓝球得分: {conf['blue_score']:.3f}")
        print(f"   策略置信度: {conf['strategy_confidence']:.3f}")
        
        # 分析摘要
        analysis = prediction['analysis_summary']
        print(f"\n📋 分析摘要:")
        print(f"   历史数据期数: {analysis['total_periods']}")
        
        red_dist = analysis['red_distribution']
        print(f"   红球分布 - 低区: {red_dist['low']}, 中区: {red_dist['mid']}, 高区: {red_dist['high']}")
        
        print(f"\n🔬 技术特点:")
        print(f"   ✓ 集成学习框架 - 融合6种预测模型")
        print(f"   ✓ 动态策略切换 - 自适应调整预测策略")
        print(f"   ✓ 深度蓝球分析 - 专用神经网络预测")
        print(f"   ✓ 量子启发融合 - 量子叠加态特征融合")
        print(f"   ✓ 时序注意力机制 - 历史模式匹配")
        
        print("\n" + "="*70)
        print("💡 本预测基于终极集成算法，融合了多种先进技术")
        print("⚠️  仅供参考，请理性购彩")
        print("="*70)
        
        return f"{red_balls} + {blue_ball}"

def check_data_integrity(data):
    """检查数据完整性"""
    if not data:
        return False, "数据为空"
    
    if len(data) < 10:
        return False, f"数据量不足，仅有 {len(data)} 期数据"
    
    # 检查数据格式
    for i, record in enumerate(data[:5]):  # 检查前5条记录
        if not isinstance(record, dict):
            return False, f"第 {i+1} 条记录格式错误"
        
        required_fields = ['red', 'blue']
        for field in required_fields:
            if field not in record:
                return False, f"第 {i+1} 条记录缺少字段: {field}"
        
        # 检查红球格式
        if not isinstance(record['red'], list) or len(record['red']) != 6:
            return False, f"第 {i+1} 条记录红球格式错误，应为6个红球"
        
        # 检查红球范围
        for ball in record['red']:
            if not isinstance(ball, int) or not (1 <= ball <= 33):
                return False, f"第 {i+1} 条记录红球号码范围错误: {ball}"
        
        # 检查蓝球格式
        if not isinstance(record['blue'], int) or not (1 <= record['blue'] <= 16):
            return False, f"第 {i+1} 条记录蓝球格式错误: {record['blue']}"
    
    # 检查数据一致性
    red_ball_counts = [len(record['red']) for record in data[:10]]
    if not all(count == 6 for count in red_ball_counts):
        return False, "红球数量不一致，应为6个红球"
    
    # 检查数据时效性（简化版）
    try:
        from datetime import datetime
        current_year = datetime.now().year
        # 假设数据是按时间顺序排列的，检查是否有合理的数据量
        if len(data) < 50:
            return False, f"历史数据量过少: {len(data)} 期"
    except:
        pass  # 如果无法检查时效性，跳过
    
    return True, "数据完整性检查通过"

def main():
    """主程序入口"""
    print("🚀 双色球终极集成预测系统")
    print("融合所有算法优势的最终预测程序")
    print("="*50)
    
    try:
        # 创建预测器
        predictor = UltimateSSQPredictor()
        
        # 数据完整性检查
        if predictor.data:
            print("\n🔍 正在进行数据完整性检查...")
            is_valid, message = check_data_integrity(predictor.data)
            
            if not is_valid:
                print(f"⚠️ 数据完整性检查失败: {message}")
                print("⚠️ 将继续使用现有数据进行预测")
            else:
                print(f"✅ {message}")
        
        # 进行预测
        prediction = predictor.predict()
        
        # 格式化输出
        result = predictor.format_output(prediction)
        
        # 生成特定格式输出
        red_str = ''.join([f"{num:02d}" for num in prediction['red']])
        blue_str = f"{prediction['blue']:02d}"
        special_format = f"00CP#01#{red_str}*{blue_str}#1"
        print(f"\n📋 特定格式输出: {special_format}")
        
        # 保存预测记录
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prediction_record = {
            'timestamp': timestamp,
            'prediction': prediction,
            'special_format': special_format,
            'algorithm': 'UltimateIntegratedPredictor',
            'result': result,
            'data_integrity': is_valid if 'is_valid' in locals() else True
        }
        
        try:
            with open('ultimate_prediction_history.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(prediction_record, ensure_ascii=False) + '\n')
            print(f"\n💾 预测记录已保存 ({timestamp})")
        except Exception as e:
            print(f"\n❌ 保存预测记录失败: {e}")
        
        return prediction, special_format
        
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("\n🔄 使用备用随机预测...")
        
        backup_prediction = {
            'red': sorted(random.sample(range(1, 34), 7)),
            'blue': random.randint(1, 16)
        }
        
        red_balls = ' '.join([f"{num:02d}" for num in backup_prediction['red']])
        blue_ball = f"{backup_prediction['blue']:02d}"
        print(f"\n🎲 随机预测: {red_balls} + {blue_ball}")
        
        # 生成特定格式输出
        red_str = ''.join([f"{num:02d}" for num in backup_prediction['red']])
        blue_str = f"{backup_prediction['blue']:02d}"
        special_format = f"00CP#01#{red_str}*{blue_str}#1"
        print(f"\n📋 特定格式输出: {special_format}")
        
        return backup_prediction, special_format

if __name__ == "__main__":
    main()