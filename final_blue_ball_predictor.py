#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终蓝球预测器
基于深度数据分析和实际验证的蓝球预测系统
目标：蓝球命中率≥12%

核心理念：
1. 基于真实数据的统计规律
2. 简化但有效的预测策略
3. 动态权重调整
4. 多候选投票机制
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class FinalBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化最终蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 核心策略权重
        self.strategy_weights = {
            'gap_analysis': 0.35,        # 间隔分析（主要策略）
            'cold_hot_balance': 0.30,    # 冷热平衡
            'frequency_deviation': 0.20, # 频率偏差
            'recent_trend': 0.15         # 最近趋势
        }
        
        # 预测历史和统计
        self.prediction_history = []
        self.performance_stats = {
            'total': 0,
            'correct': 0,
            'hit_rate': 0.0,
            'recent_performance': [],
            'strategy_success': {strategy: 0 for strategy in self.strategy_weights.keys()}
        }
        
        # 自适应参数
        self.adaptation_period = 20
        self.min_data_requirement = 50
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def gap_analysis_strategy(self):
        """间隔分析策略 - 基于出现间隔的深度分析"""
        if not self.data or len(self.data) < self.min_data_requirement:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            # 找到所有出现位置
            positions = [i for i, blue in enumerate(blue_sequence) if blue == ball]
            
            if len(positions) >= 2:
                # 计算所有间隔
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                
                # 当前距离上次出现的间隔
                current_gap = len(blue_sequence) - positions[-1]
                
                # 统计分析
                avg_interval = np.mean(intervals)
                std_interval = np.std(intervals) if len(intervals) > 1 else avg_interval * 0.5
                max_interval = max(intervals)
                min_interval = min(intervals)
                
                # 评分策略
                score = 0.0
                
                # 1. 基于平均间隔
                if current_gap >= avg_interval * 0.8:
                    score += min(2.0, current_gap / avg_interval)
                
                # 2. 基于标准差
                if std_interval > 0:
                    z_score = (current_gap - avg_interval) / std_interval
                    if z_score > 0:  # 当前间隔大于平均值
                        score += min(1.0, z_score / 2)
                
                # 3. 超长间隔奖励
                if current_gap > max_interval:
                    score += 1.5
                elif current_gap > avg_interval * 1.5:
                    score += 1.0
                
                # 4. 间隔稳定性
                if std_interval < avg_interval * 0.5:  # 间隔比较稳定
                    if abs(current_gap - avg_interval) < std_interval:
                        score += 0.5
                
                scores[ball] = score
                
            elif len(positions) == 1:
                # 只出现过一次
                current_gap = len(blue_sequence) - positions[0]
                avg_gap = len(blue_sequence) / 16  # 理论平均间隔
                
                if current_gap > avg_gap * 1.5:
                    scores[ball] = 2.0
                elif current_gap > avg_gap:
                    scores[ball] = 1.5
                else:
                    scores[ball] = 1.0
            else:
                # 从未出现（理论上不存在）
                scores[ball] = 3.0
        
        return scores
    
    def cold_hot_balance_strategy(self):
        """冷热平衡策略 - 多窗口冷热分析"""
        if not self.data:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 多个时间窗口分析
        windows = [15, 25, 40, 60]
        weights = [0.4, 0.3, 0.2, 0.1]
        
        for window, weight in zip(windows, weights):
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                frequency = Counter(recent_blues)
                expected_freq = window / 16
                
                for ball in self.blue_range:
                    actual_freq = frequency[ball]
                    
                    # 冷号得分：出现频率低于期望
                    if actual_freq < expected_freq:
                        cold_score = (expected_freq - actual_freq) / expected_freq
                        scores[ball] += cold_score * weight * 2.0
                    
                    # 完全未出现的冷号
                    if actual_freq == 0:
                        scores[ball] += weight * 1.5
                    
                    # 轻微热号的回调机会
                    elif actual_freq == expected_freq + 1:
                        scores[ball] += weight * 0.3
        
        return scores
    
    def frequency_deviation_strategy(self):
        """频率偏差策略 - 基于长期频率偏差"""
        if not self.data:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        frequency = Counter(blue_sequence)
        total_count = len(blue_sequence)
        expected_count = total_count / 16
        
        scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            actual_count = frequency[ball]
            deviation = expected_count - actual_count
            
            if deviation > 0:  # 出现次数少于期望
                # 偏差越大，分数越高
                deviation_score = min(3.0, deviation / expected_count * 3)
                scores[ball] = deviation_score
                
                # 特别奖励严重偏少的号码
                if deviation > expected_count * 0.3:
                    scores[ball] += 1.0
            else:
                # 出现次数多于期望，给予很低分数
                scores[ball] = 0.1
        
        return scores
    
    def recent_trend_strategy(self):
        """最近趋势策略 - 分析最近的出现模式"""
        if not self.data or len(self.data) < 20:
            return {ball: 1.0 for ball in self.blue_range}
        
        blue_sequence = [entry['blue'] for entry in self.data]
        scores = {ball: 0.0 for ball in self.blue_range}
        
        # 分析最近不同长度的趋势
        trend_windows = [8, 12, 16]
        trend_weights = [0.5, 0.3, 0.2]
        
        for window, weight in zip(trend_windows, trend_weights):
            if len(blue_sequence) >= window:
                recent_blues = blue_sequence[-window:]
                recent_freq = Counter(recent_blues)
                
                for ball in self.blue_range:
                    recent_count = recent_freq[ball]
                    
                    # 反向趋势：最近未出现的给高分
                    if recent_count == 0:
                        scores[ball] += 2.0 * weight
                    elif recent_count == 1:
                        scores[ball] += 1.0 * weight
                    elif recent_count == 2:
                        scores[ball] += 0.3 * weight
                    else:
                        scores[ball] += 0.1 * weight
        
        # 分析连续性模式
        if len(blue_sequence) >= 6:
            last_6 = blue_sequence[-6:]
            
            for ball in self.blue_range:
                # 检查是否有连续出现模式
                consecutive_gaps = []
                last_pos = -1
                
                for i, blue in enumerate(last_6):
                    if blue == ball:
                        if last_pos >= 0:
                            consecutive_gaps.append(i - last_pos)
                        last_pos = i
                
                # 如果有规律的间隔，预测下次出现
                if len(consecutive_gaps) >= 2:
                    avg_gap = np.mean(consecutive_gaps)
                    current_gap = len(last_6) - last_pos - 1
                    
                    if abs(current_gap - avg_gap) <= 1:
                        scores[ball] += 0.8
        
        return scores
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 获取各策略得分
        gap_scores = self.gap_analysis_strategy()
        cold_hot_scores = self.cold_hot_balance_strategy()
        freq_dev_scores = self.frequency_deviation_strategy()
        trend_scores = self.recent_trend_strategy()
        
        # 计算加权总分
        final_scores = {ball: 0.0 for ball in self.blue_range}
        
        for ball in self.blue_range:
            final_scores[ball] = (
                gap_scores[ball] * self.strategy_weights['gap_analysis'] +
                cold_hot_scores[ball] * self.strategy_weights['cold_hot_balance'] +
                freq_dev_scores[ball] * self.strategy_weights['frequency_deviation'] +
                trend_scores[ball] * self.strategy_weights['recent_trend']
            )
        
        # 归一化
        max_score = max(final_scores.values()) if final_scores.values() else 1
        if max_score > 0:
            for ball in self.blue_range:
                final_scores[ball] /= max_score
        
        # 多候选策略
        sorted_candidates = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 候选池策略
        top_5 = sorted_candidates[:5]
        top_8 = sorted_candidates[:8]
        
        candidates = []
        candidate_weights = []
        
        # 70%概率从前5名选择，30%概率从前8名选择
        if random.random() < 0.7:
            candidates = [ball for ball, score in top_5]
            candidate_weights = [score for ball, score in top_5]
        else:
            candidates = [ball for ball, score in top_8]
            candidate_weights = [score for ball, score in top_8]
        
        # 按权重随机选择
        if sum(candidate_weights) > 0:
            normalized_weights = [w / sum(candidate_weights) for w in candidate_weights]
            predicted_ball = np.random.choice(candidates, p=normalized_weights)
        else:
            predicted_ball = candidates[0] if candidates else random.randint(1, 16)
        
        # 记录预测详情
        self.prediction_history.append({
            'prediction': predicted_ball,
            'final_scores': final_scores,
            'strategy_scores': {
                'gap_analysis': gap_scores[predicted_ball],
                'cold_hot_balance': cold_hot_scores[predicted_ball],
                'frequency_deviation': freq_dev_scores[predicted_ball],
                'recent_trend': trend_scores[predicted_ball]
            },
            'candidates': [(ball, score) for ball, score in sorted_candidates[:8]],
            'timestamp': datetime.now().isoformat()
        })
        
        return predicted_ball
    
    def update_performance(self, predicted, actual):
        """更新性能统计"""
        self.performance_stats['total'] += 1
        
        is_correct = (predicted == actual)
        if is_correct:
            self.performance_stats['correct'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct'] / self.performance_stats['total']
        )
        
        # 记录最近表现
        self.performance_stats['recent_performance'].append(1.0 if is_correct else 0.0)
        if len(self.performance_stats['recent_performance']) > 50:
            self.performance_stats['recent_performance'].pop(0)
        
        # 策略成功统计
        if self.prediction_history and is_correct:
            last_prediction = self.prediction_history[-1]
            
            # 找出贡献最大的策略
            strategy_contributions = last_prediction['strategy_scores']
            max_contributor = max(strategy_contributions.items(), key=lambda x: x[1])[0]
            self.performance_stats['strategy_success'][max_contributor] += 1
        
        # 自适应权重调整
        if self.performance_stats['total'] % self.adaptation_period == 0:
            self.adaptive_weight_adjustment()
    
    def adaptive_weight_adjustment(self):
        """自适应权重调整"""
        if self.performance_stats['total'] < self.adaptation_period:
            return
        
        # 计算最近表现
        recent_performance = self.performance_stats['recent_performance'][-self.adaptation_period:]
        recent_hit_rate = np.mean(recent_performance)
        
        # 如果表现不佳，调整策略权重
        if recent_hit_rate < 0.08:  # 低于8%
            # 增加表现好的策略权重
            total_success = sum(self.performance_stats['strategy_success'].values())
            
            if total_success > 0:
                for strategy in self.strategy_weights.keys():
                    success_rate = self.performance_stats['strategy_success'][strategy] / total_success
                    
                    if success_rate > 0.3:  # 成功率高的策略
                        self.strategy_weights[strategy] *= 1.1
                    elif success_rate < 0.15:  # 成功率低的策略
                        self.strategy_weights[strategy] *= 0.9
                
                # 重新归一化
                total_weight = sum(self.strategy_weights.values())
                for strategy in self.strategy_weights.keys():
                    self.strategy_weights[strategy] /= total_weight
    
    def get_performance_report(self):
        """获取性能报告"""
        recent_hit_rate = 0.0
        if self.performance_stats['recent_performance']:
            recent_hit_rate = np.mean(self.performance_stats['recent_performance'][-30:])
        
        return {
            'total_predictions': self.performance_stats['total'],
            'correct_predictions': self.performance_stats['correct'],
            'hit_rate': self.performance_stats['hit_rate'],
            'recent_hit_rate': recent_hit_rate,
            'strategy_weights': self.strategy_weights.copy(),
            'strategy_success': self.performance_stats['strategy_success'].copy()
        }

def test_final_blue_predictor():
    """测试最终蓝球预测器"""
    print("=== 最终蓝球预测器测试 ===")
    
    predictor = FinalBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(80, len(original_data) - 50)  # 确保有足够的训练数据
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            if i < 8 or i % 20 == 0:  # 只显示部分结果
                print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = original_data
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    print(f"总预测次数: {report['total_predictions']}")
    print(f"正确预测次数: {report['correct_predictions']}")
    print(f"整体命中率: {report['hit_rate']:.4f}")
    print(f"最近命中率: {report['recent_hit_rate']:.4f}")
    
    print(f"\n策略权重配置:")
    for strategy, weight in report['strategy_weights'].items():
        print(f"  {strategy}: {weight:.3f}")
    
    print(f"\n策略成功次数:")
    for strategy, success in report['strategy_success'].items():
        print(f"  {strategy}: {success}次")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    # 预测详情
    if predictor.prediction_history:
        last_prediction = predictor.prediction_history[-1]
        print(f"\n=== 预测详情 ===")
        print(f"各策略得分:")
        for strategy, score in last_prediction['strategy_scores'].items():
            print(f"  {strategy}: {score:.4f}")
        
        print(f"\n候选球号排序:")
        for i, (ball, score) in enumerate(last_prediction['candidates']):
            print(f"  第{i+1}名: 球号{ball}, 得分{score:.4f}")
    
    return predictor

if __name__ == "__main__":
    test_final_blue_predictor()