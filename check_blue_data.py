#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# 读取数据
with open('ssq_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print('最近20期蓝球数据:')
for i, item in enumerate(data[:20]):
    print(f'期号:{item["issueno"]}, 蓝球:{item["refernumber"]}')

# 统计蓝球分布
from collections import Counter
blue_balls = [int(item['refernumber']) for item in data[:100]]  # 最近100期
blue_counter = Counter(blue_balls)

print('\n最近100期蓝球分布:')
for ball in sorted(blue_counter.keys()):
    print(f'蓝球{ball}: {blue_counter[ball]}次')

print(f'\n蓝球11出现次数: {blue_counter.get(11, 0)}次')
print(f'是否所有蓝球都是11: {all(ball == 11 for ball in blue_balls)}')