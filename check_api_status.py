#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查API状态和数据更新情况
"""

import requests
import json
import os
from datetime import datetime

# JisuAPI参数
APPKEY = 'eb6cc4f9bf4a23b4'
DATA_FILE = 'ssq_data.json'

def check_api_status():
    """检查API状态"""
    print("🔍 检查API状态...")
    url = f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=11&num=1&start=0'
    
    try:
        response = requests.get(url, timeout=10)
        data = response.json()
        
        print(f"API响应状态: {data.get('status', '未知')}")
        print(f"API响应消息: {data.get('msg', '无消息')}")
        
        if data.get('status') == 0 or data.get('status') == '0':
            if data.get('result', {}).get('list'):
                latest_data = data['result']['list'][0]
                print(f"✅ API正常，最新期号: {latest_data.get('issueno', '未知')}")
                print(f"开奖日期: {latest_data.get('opendate', '未知')}")
                return True, latest_data
            else:
                print("⚠️ API响应正常但无数据")
                return False, None
        else:
            print(f"❌ API调用失败: {data.get('msg', '未知错误')}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False, None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False, None

def check_local_data():
    """检查本地数据状态"""
    print("\n📁 检查本地数据...")
    
    if not os.path.exists(DATA_FILE):
        print(f"❌ 数据文件 {DATA_FILE} 不存在")
        return None
    
    try:
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data:
            print("❌ 数据文件为空")
            return None
        
        latest_local = data[0]
        print(f"✅ 本地数据正常")
        print(f"本地最新期号: {latest_local.get('issueno', '未知')}")
        print(f"本地开奖日期: {latest_local.get('opendate', '未知')}")
        print(f"本地数据总数: {len(data)} 期")
        
        return latest_local
        
    except Exception as e:
        print(f"❌ 读取本地数据失败: {e}")
        return None

def analyze_data_status():
    """分析数据状态"""
    print("\n📊 数据状态分析")
    print("=" * 50)
    
    # 检查API状态
    api_ok, api_data = check_api_status()
    
    # 检查本地数据
    local_data = check_local_data()
    
    print("\n📋 分析结果:")
    print("-" * 30)
    
    if not api_ok:
        # 检查具体的API错误
        try:
            response = requests.get(f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=11&num=1&start=0', timeout=5)
            error_data = response.json()
            if error_data.get('status') == '104' or error_data.get('status') == 104:
                print("🚫 API调用次数已达限制")
                print("💡 建议解决方案:")
                print("   1. 等待API限制重置（通常是每日重置）")
                print("   2. 使用其他API密钥")
                print("   3. 手动更新数据文件")
                print("   4. 暂时使用现有本地数据进行预测")
            else:
                print(f"❌ API调用失败: {error_data.get('msg', '未知错误')}")
        except:
            print("❌ API调用失败，可能是网络问题或API服务异常")
    else:
        print("✅ API调用正常")
    
    if local_data:
        print(f"✅ 本地数据可用，可以进行预测")
        
        # 检查数据是否需要更新
        if api_ok and api_data:
            local_period = local_data.get('issueno', '')
            api_period = api_data.get('issueno', '')
            
            if local_period == api_period:
                print("✅ 本地数据已是最新")
            else:
                print(f"⚠️ 本地数据需要更新: 本地 {local_period} vs API {api_period}")
    else:
        print("❌ 本地数据不可用")
    
    print("\n🎯 当前状态总结:")
    if local_data:
        print("✅ 可以正常运行预测程序")
        print("💡 即使API受限，现有数据足够进行预测")
    else:
        print("❌ 无法运行预测程序，需要先获取数据")

def main():
    """主函数"""
    print("🔍 双色球数据状态检查工具")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    analyze_data_status()
    
    print("\n" + "=" * 50)
    print("检查完成")

if __name__ == '__main__':
    main()