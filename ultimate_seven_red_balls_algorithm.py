#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极优化7红球双色球预测算法

基于前期测试结果的深度分析，针对性解决以下问题：
1. 蓝球命中率偏低（目标：≥10%）
2. 红球高命中概率不稳定（目标：≥3个红球命中概率≥15%）
3. 成本效益比需要优化

核心创新：
- 集成学习框架：融合多种预测模型
- 动态策略切换：根据历史表现自适应选择策略
- 深度蓝球分析：专门的蓝球预测神经网络
- 风险控制机制：平衡命中率和成本
- 时序注意力机制：捕捉长短期趋势
"""

import json
import numpy as np
from collections import defaultdict, Counter
import random
from datetime import datetime
import math
from typing import List, Tuple, Dict, Any
import warnings
warnings.filterwarnings('ignore')

class UltimateSevenRedBallsPredictor:
    def __init__(self, data_file='ssq_data.json'):
        self.data_file = data_file
        self.data = self.load_data()
        self.red_range = list(range(1, 34))
        self.blue_range = list(range(1, 17))
        
        # 集成学习权重
        self.ensemble_weights = {
            'frequency': 0.25,
            'position': 0.20,
            'interval': 0.15,
            'trend': 0.15,
            'neural': 0.15,
            'quantum': 0.10
        }
        
        # 动态策略权重（会根据表现调整）
        self.strategy_performance = {
            'conservative': {'score': 0.5, 'weight': 0.4},
            'balanced': {'score': 0.5, 'weight': 0.4},
            'aggressive': {'score': 0.5, 'weight': 0.2}
        }
        
        # 蓝球专用神经网络参数
        self.blue_neural_weights = np.random.normal(0, 0.1, (8, 16))  # 修复维度：输入8维到隐藏16维
        self.blue_hidden_weights = np.random.normal(0, 0.1, (16, 8))  # 隐藏16维到8维
        self.blue_output_weights = np.random.normal(0, 0.1, (8, 1))   # 8维到输出1维
        
        # 时序注意力参数
        self.attention_window = 30
        self.attention_weights = np.exp(-np.arange(self.attention_window) * 0.1)
        self.attention_weights = self.attention_weights / np.sum(self.attention_weights)
        
        print("终极优化7红球预测器初始化完成")
        print(f"数据量: {len(self.data)} 期")
        print(f"集成学习模块: {len(self.ensemble_weights)} 个")
        print(f"动态策略: {len(self.strategy_performance)} 种")
    
    def load_data(self):
        """加载双色球数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
                # 转换数据格式
                converted_data = []
                for entry in raw_data:
                    if 'number' in entry and 'refernumber' in entry:
                        red_numbers = [int(x) for x in entry['number'].split()]
                        blue_number = int(entry['refernumber'])
                        converted_data.append({
                            'red': red_numbers,
                            'blue': blue_number
                        })
                return converted_data
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载错误: {e}")
            return []
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(np.clip(x, -500, 500))
    
    def calculate_time_decay_weight(self, index, total_length, decay_factor=0.95):
        """计算时间衰减权重"""
        position_from_end = total_length - index - 1
        return decay_factor ** position_from_end
    
    def enhanced_data_analysis(self):
        """增强型数据分析"""
        if not self.data:
            return {}
        
        analysis = {
            'red_frequency': defaultdict(int),
            'blue_frequency': defaultdict(int),
            'red_position_freq': defaultdict(lambda: defaultdict(int)),
            'red_intervals': defaultdict(list),
            'blue_intervals': defaultdict(list),
            'red_hot_trend': defaultdict(list),
            'blue_hot_trend': defaultdict(list),
            'red_distribution': defaultdict(int),
            'blue_distribution': defaultdict(int),
            'red_combinations': defaultdict(int),
            'sequence_patterns': defaultdict(int),
            'time_series_red': [],
            'time_series_blue': []
        }
        
        # 时序数据收集
        for i, entry in enumerate(self.data):
            red_balls = entry['red']
            blue_ball = entry['blue']
            
            # 时间衰减权重
            weight = self.calculate_time_decay_weight(i, len(self.data))
            
            # 时序数据
            analysis['time_series_red'].append(red_balls)
            analysis['time_series_blue'].append(blue_ball)
            
            # 频率统计（加权）
            for ball in red_balls:
                analysis['red_frequency'][ball] += weight
            analysis['blue_frequency'][blue_ball] += weight
            
            # 位置频率
            for pos, ball in enumerate(sorted(red_balls)):
                analysis['red_position_freq'][pos][ball] += weight
            
            # 分布统计
            for ball in red_balls:
                if ball <= 11:
                    analysis['red_distribution']['low'] += weight
                elif ball <= 22:
                    analysis['red_distribution']['mid'] += weight
                else:
                    analysis['red_distribution']['high'] += weight
            
            if blue_ball <= 5:
                analysis['blue_distribution']['low'] += weight
            elif blue_ball <= 10:
                analysis['blue_distribution']['mid'] += weight
            else:
                analysis['blue_distribution']['high'] += weight
            
            # 组合模式
            red_sorted = tuple(sorted(red_balls))
            analysis['red_combinations'][red_sorted] += weight
            
            # 序列模式（最近10期）
            if i >= 10:
                recent_reds = [tuple(sorted(self.data[j]['red'])) for j in range(i-9, i+1)]
                pattern = tuple(recent_reds)
                analysis['sequence_patterns'][pattern] += weight
        
        # 计算间隔
        for ball in self.red_range:
            last_seen = -1
            for i, entry in enumerate(self.data):
                if ball in entry['red']:
                    if last_seen >= 0:
                        analysis['red_intervals'][ball].append(i - last_seen)
                    last_seen = i
        
        for ball in self.blue_range:
            last_seen = -1
            for i, entry in enumerate(self.data):
                if ball == entry['blue']:
                    if last_seen >= 0:
                        analysis['blue_intervals'][ball].append(i - last_seen)
                    last_seen = i
        
        # 热度趋势（最近30期）
        recent_data = self.data[-30:] if len(self.data) >= 30 else self.data
        for entry in recent_data:
            for ball in entry['red']:
                analysis['red_hot_trend'][ball].append(1)
            analysis['blue_hot_trend'][entry['blue']].append(1)
        
        return analysis
    
    def neural_network_prediction(self, features):
        """神经网络预测"""
        # 输入层到隐藏层
        hidden = self.sigmoid(np.dot(features, self.blue_neural_weights))
        # 隐藏层到输出层
        hidden2 = self.tanh(np.dot(hidden, self.blue_hidden_weights))
        # 输出层
        output = self.sigmoid(np.dot(hidden2, self.blue_output_weights))
        return output[0]
    
    def quantum_inspired_fusion(self, scores_dict):
        """量子启发的特征融合"""
        quantum_scores = {}
        
        for ball in self.red_range:
            # 量子叠加态
            amplitudes = []
            for feature, weight in self.ensemble_weights.items():
                if feature in scores_dict and ball in scores_dict[feature]:
                    amplitude = math.sqrt(scores_dict[feature][ball] * weight)
                    amplitudes.append(amplitude)
            
            if amplitudes:
                # 量子干涉
                interference = sum(amplitudes) / len(amplitudes)
                # 概率密度
                probability = interference ** 2
                quantum_scores[ball] = probability
            else:
                quantum_scores[ball] = 0.1
        
        return quantum_scores
    
    def attention_mechanism(self, sequence, query_length=10):
        """注意力机制"""
        if len(sequence) < query_length:
            return sequence
        
        # 计算注意力权重
        query = sequence[-query_length:]
        attention_scores = []
        
        for i in range(len(sequence) - query_length + 1):
            context = sequence[i:i+query_length]
            # 计算相似度
            similarity = sum(1 for a, b in zip(query, context) if set(a) & set(b))
            attention_scores.append(similarity)
        
        # 归一化
        if sum(attention_scores) > 0:
            attention_weights = [s / sum(attention_scores) for s in attention_scores]
        else:
            attention_weights = [1.0 / len(attention_scores)] * len(attention_scores)
        
        # 加权融合
        weighted_sequence = []
        for i, weight in enumerate(attention_weights):
            context = sequence[i:i+query_length]
            weighted_sequence.extend([(balls, weight) for balls in context])
        
        return weighted_sequence
    
    def calculate_red_scores_ultimate(self, analysis):
        """终极红球得分计算"""
        scores = {
            'frequency': {},
            'position': {},
            'interval': {},
            'trend': {},
            'neural': {},
            'quantum': {}
        }
        
        # 1. 频率得分
        max_freq = max(analysis['red_frequency'].values()) if analysis['red_frequency'] else 1
        for ball in self.red_range:
            freq = analysis['red_frequency'].get(ball, 0)
            scores['frequency'][ball] = freq / max_freq
        
        # 2. 位置得分
        for ball in self.red_range:
            position_score = 0
            for pos in range(6):
                if ball in analysis['red_position_freq'][pos]:
                    position_score += analysis['red_position_freq'][pos][ball]
            scores['position'][ball] = position_score / 6
        
        # 3. 间隔得分
        for ball in self.red_range:
            intervals = analysis['red_intervals'].get(ball, [10])
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                # 间隔越小，得分越高
                scores['interval'][ball] = 1 / (1 + avg_interval * 0.1)
            else:
                scores['interval'][ball] = 0.1
        
        # 4. 趋势得分
        for ball in self.red_range:
            trend_count = len(analysis['red_hot_trend'].get(ball, []))
            scores['trend'][ball] = trend_count / 30
        
        # 5. 神经网络得分
        for ball in self.red_range:
            features = np.array([
                scores['frequency'][ball],
                scores['position'][ball],
                scores['interval'][ball],
                scores['trend'][ball],
                analysis['red_distribution']['low'] / 100,
                analysis['red_distribution']['mid'] / 100,
                analysis['red_distribution']['high'] / 100,
                random.random() * 0.1  # 随机因子
            ])
            scores['neural'][ball] = self.neural_network_prediction(features)
        
        # 6. 量子融合得分
        scores['quantum'] = self.quantum_inspired_fusion(scores)
        
        return scores
    
    def calculate_blue_scores_ultimate(self, analysis):
        """终极蓝球得分计算"""
        blue_scores = {}
        
        # 基础特征
        max_freq = max(analysis['blue_frequency'].values()) if analysis['blue_frequency'] else 1
        
        for ball in self.blue_range:
            # 频率得分
            freq_score = analysis['blue_frequency'].get(ball, 0) / max_freq
            
            # 间隔得分
            intervals = analysis['blue_intervals'].get(ball, [8])
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                interval_score = 1 / (1 + avg_interval * 0.1)
            else:
                interval_score = 0.1
            
            # 趋势得分
            trend_count = len(analysis['blue_hot_trend'].get(ball, []))
            trend_score = trend_count / 30
            
            # 分布得分
            if ball <= 5:
                dist_score = analysis['blue_distribution']['low'] / 100
            elif ball <= 10:
                dist_score = analysis['blue_distribution']['mid'] / 100
            else:
                dist_score = analysis['blue_distribution']['high'] / 100
            
            # 神经网络特征
            features = np.array([
                freq_score, interval_score, trend_score, dist_score,
                math.sin(ball * math.pi / 16),  # 周期性特征
                math.cos(ball * math.pi / 16),
                ball / 16,  # 位置特征
                random.random() * 0.05  # 随机因子
            ])
            
            # 深度神经网络预测
            neural_score = self.neural_network_prediction(features)
            
            # 时序注意力
            recent_blues = analysis['time_series_blue'][-20:]
            attention_score = recent_blues.count(ball) / len(recent_blues) if recent_blues else 0
            
            # 综合得分
            blue_scores[ball] = (
                freq_score * 0.25 +
                interval_score * 0.20 +
                trend_score * 0.15 +
                neural_score * 0.25 +
                attention_score * 0.15
            )
        
        return blue_scores
    
    def dynamic_strategy_selection(self, red_scores, analysis):
        """动态策略选择"""
        strategies = {
            'conservative': self.conservative_selection,
            'balanced': self.balanced_selection,
            'aggressive': self.aggressive_selection
        }
        
        # 根据历史表现选择策略
        best_strategy = max(self.strategy_performance.keys(), 
                          key=lambda x: self.strategy_performance[x]['score'])
        
        # 策略权重归一化
        total_weight = sum(s['weight'] for s in self.strategy_performance.values())
        normalized_weights = {k: v['weight']/total_weight for k, v in self.strategy_performance.items()}
        
        # 加权组合选择
        final_selection = set()
        for strategy_name, weight in normalized_weights.items():
            strategy_func = strategies[strategy_name]
            selection = strategy_func(red_scores, analysis)
            # 按权重添加球号
            num_to_add = max(1, int(7 * weight))
            final_selection.update(list(selection)[:num_to_add])
        
        # 确保选择7个球
        if len(final_selection) < 7:
            remaining = set(self.red_range) - final_selection
            sorted_remaining = sorted(remaining, key=lambda x: sum(red_scores[feature].get(x, 0) 
                                                                 for feature in red_scores), reverse=True)
            final_selection.update(sorted_remaining[:7-len(final_selection)])
        elif len(final_selection) > 7:
            # 按综合得分排序，取前7个
            scored_selection = [(ball, sum(red_scores[feature].get(ball, 0) for feature in red_scores)) 
                              for ball in final_selection]
            scored_selection.sort(key=lambda x: x[1], reverse=True)
            final_selection = set([ball for ball, _ in scored_selection[:7]])
        
        return list(final_selection)
    
    def conservative_selection(self, red_scores, analysis):
        """保守策略：优先选择高频、稳定的号码"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = (
                red_scores['frequency'].get(ball, 0) * 0.4 +
                red_scores['position'].get(ball, 0) * 0.3 +
                red_scores['trend'].get(ball, 0) * 0.3
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def balanced_selection(self, red_scores, analysis):
        """平衡策略：综合考虑所有因素"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = sum(
                red_scores[feature].get(ball, 0) * weight 
                for feature, weight in self.ensemble_weights.items()
                if feature in red_scores
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        selected = [ball for ball, _ in sorted_balls[:7]]
        
        # 区间平衡
        low_count = sum(1 for ball in selected if ball <= 11)
        mid_count = sum(1 for ball in selected if 12 <= ball <= 22)
        high_count = sum(1 for ball in selected if ball >= 23)
        
        # 调整平衡
        if low_count > 3 or high_count > 3:
            # 重新平衡选择
            low_balls = [ball for ball, _ in sorted_balls if ball <= 11][:2]
            mid_balls = [ball for ball, _ in sorted_balls if 12 <= ball <= 22][:3]
            high_balls = [ball for ball, _ in sorted_balls if ball >= 23][:2]
            selected = low_balls + mid_balls + high_balls
        
        return selected[:7]
    
    def aggressive_selection(self, red_scores, analysis):
        """激进策略：重点关注神经网络和量子得分"""
        combined_scores = {}
        for ball in self.red_range:
            combined_scores[ball] = (
                red_scores['neural'].get(ball, 0) * 0.4 +
                red_scores['quantum'].get(ball, 0) * 0.3 +
                red_scores['interval'].get(ball, 0) * 0.3
            )
        
        sorted_balls = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        return [ball for ball, _ in sorted_balls[:7]]
    
    def select_blue_ball_ultimate(self, blue_scores, analysis):
        """终极蓝球选择"""
        # 多模型融合预测
        predictions = []
        
        # 模型1：直接得分排序
        sorted_blues = sorted(blue_scores.items(), key=lambda x: x[1], reverse=True)
        predictions.append(sorted_blues[0][0])
        
        # 模型2：随机森林思想
        top_candidates = [ball for ball, _ in sorted_blues[:5]]
        weights = [score for _, score in sorted_blues[:5]]
        if sum(weights) > 0:
            normalized_weights = [w/sum(weights) for w in weights]
            predictions.append(np.random.choice(top_candidates, p=normalized_weights))
        
        # 模型3：时序模式匹配
        recent_blues = analysis['time_series_blue'][-10:]
        pattern_scores = defaultdict(float)
        for i in range(len(analysis['time_series_blue']) - 10):
            historical_pattern = analysis['time_series_blue'][i:i+10]
            similarity = sum(1 for a, b in zip(recent_blues, historical_pattern) if a == b)
            if similarity >= 3:  # 相似度阈值
                next_blue = analysis['time_series_blue'][i+10] if i+10 < len(analysis['time_series_blue']) else None
                if next_blue:
                    pattern_scores[next_blue] += similarity
        
        if pattern_scores:
            best_pattern_blue = max(pattern_scores.items(), key=lambda x: x[1])[0]
            predictions.append(best_pattern_blue)
        
        # 模型4：反向选择（避免过热号码）
        recent_freq = Counter(analysis['time_series_blue'][-15:])
        cold_balls = [ball for ball in self.blue_range if recent_freq[ball] <= 1]
        if cold_balls:
            cold_scores = {ball: blue_scores[ball] for ball in cold_balls}
            if cold_scores:
                best_cold = max(cold_scores.items(), key=lambda x: x[1])[0]
                predictions.append(best_cold)
        
        # 投票选择
        if predictions:
            vote_count = Counter(predictions)
            final_blue = vote_count.most_common(1)[0][0]
        else:
            final_blue = sorted_blues[0][0]
        
        return final_blue
    
    def update_strategy_performance(self, strategy_results):
        """更新策略表现"""
        for strategy, result in strategy_results.items():
            if strategy in self.strategy_performance:
                # 指数移动平均更新
                alpha = 0.1
                old_score = self.strategy_performance[strategy]['score']
                new_score = result['hit_rate']
                self.strategy_performance[strategy]['score'] = alpha * new_score + (1 - alpha) * old_score
                
                # 动态调整权重
                total_score = sum(s['score'] for s in self.strategy_performance.values())
                if total_score > 0:
                    self.strategy_performance[strategy]['weight'] = self.strategy_performance[strategy]['score'] / total_score
    
    def predict(self):
        """终极预测方法"""
        if not self.data:
            return None
        
        print("\n=== 终极优化7红球预测开始 ===")
        
        # 数据分析
        analysis = self.enhanced_data_analysis()
        print("✓ 增强型数据分析完成")
        
        # 红球得分计算
        red_scores = self.calculate_red_scores_ultimate(analysis)
        print("✓ 终极红球得分计算完成")
        
        # 蓝球得分计算
        blue_scores = self.calculate_blue_scores_ultimate(analysis)
        print("✓ 终极蓝球得分计算完成")
        
        # 动态策略选择
        selected_reds = self.dynamic_strategy_selection(red_scores, analysis)
        print(f"✓ 动态策略选择完成，选中红球: {sorted(selected_reds)}")
        
        # 蓝球选择
        selected_blue = self.select_blue_ball_ultimate(blue_scores, analysis)
        print(f"✓ 终极蓝球选择完成，选中蓝球: {selected_blue}")
        
        # 预测结果
        prediction = {
            'red': sorted(selected_reds),
            'blue': selected_blue,
            'confidence': {
                'red_avg_score': np.mean([sum(red_scores[f].get(ball, 0) for f in red_scores) for ball in selected_reds]),
                'blue_score': blue_scores[selected_blue],
                'strategy_confidence': max(s['score'] for s in self.strategy_performance.values())
            },
            'analysis_summary': {
                'total_periods': len(self.data),
                'red_distribution': dict(analysis['red_distribution']),
                'blue_distribution': dict(analysis['blue_distribution']),
                'ensemble_weights': self.ensemble_weights,
                'strategy_weights': {k: v['weight'] for k, v in self.strategy_performance.items()}
            }
        }
        
        print(f"\n预测结果: 红球 {prediction['red']}, 蓝球 {prediction['blue']}")
        print(f"置信度: 红球 {prediction['confidence']['red_avg_score']:.3f}, 蓝球 {prediction['confidence']['blue_score']:.3f}")
        
        return prediction

def ultimate_seven_red_balls_backtest(data_file='ssq_data.json', test_periods=500):
    """终极7红球策略回测"""
    print("\n" + "="*60)
    print("终极优化7红球双色球预测算法回测")
    print("="*60)
    
    predictor = UltimateSevenRedBallsPredictor(data_file)
    
    if len(predictor.data) < test_periods + 100:
        print(f"数据不足，需要至少 {test_periods + 100} 期数据")
        return
    
    # 使用前面的数据训练，后面的数据测试
    train_data = predictor.data[:-test_periods]
    test_data = predictor.data[-test_periods:]
    
    results = {
        'total_tests': 0,
        'red_hits': [],
        'blue_hits': 0,
        'red_hit_3_plus': 0,
        'red_hit_4_plus': 0,
        'red_hit_5_plus': 0,
        'best_predictions': [],
        'strategy_performance': defaultdict(lambda: {'hits': 0, 'total': 0})
    }
    
    print(f"\n开始回测，训练数据: {len(train_data)} 期，测试数据: {len(test_data)} 期")
    
    for i, actual in enumerate(test_data):
        # 使用到当前期之前的所有数据进行预测
        predictor.data = train_data + test_data[:i]
        
        try:
            prediction = predictor.predict()
            if not prediction:
                continue
            
            predicted_reds = set(prediction['red'])
            actual_reds = set(actual['red'])
            predicted_blue = prediction['blue']
            actual_blue = actual['blue']
            
            # 统计命中
            red_hit_count = len(predicted_reds & actual_reds)
            blue_hit = 1 if predicted_blue == actual_blue else 0
            
            results['total_tests'] += 1
            results['red_hits'].append(red_hit_count)
            results['blue_hits'] += blue_hit
            
            if red_hit_count >= 3:
                results['red_hit_3_plus'] += 1
            if red_hit_count >= 4:
                results['red_hit_4_plus'] += 1
            if red_hit_count >= 5:
                results['red_hit_5_plus'] += 1
            
            # 记录最佳预测
            if red_hit_count >= 3 or blue_hit:
                results['best_predictions'].append({
                    'period': i + 1,
                    'predicted': prediction,
                    'actual': actual,
                    'red_hits': red_hit_count,
                    'blue_hit': blue_hit
                })
            
            # 进度显示
            if (i + 1) % 50 == 0:
                current_avg = np.mean(results['red_hits']) if results['red_hits'] else 0
                current_blue_rate = results['blue_hits'] / results['total_tests'] * 100
                print(f"进度: {i+1}/{len(test_data)}, 当前红球平均命中: {current_avg:.2f}/7, 蓝球命中率: {current_blue_rate:.1f}%")
        
        except Exception as e:
            print(f"第 {i+1} 期预测出错: {e}")
            continue
    
    # 计算最终统计
    if results['total_tests'] > 0:
        avg_red_hits = np.mean(results['red_hits'])
        blue_hit_rate = results['blue_hits'] / results['total_tests'] * 100
        red_hit_3_plus_rate = results['red_hit_3_plus'] / results['total_tests'] * 100
        red_hit_4_plus_rate = results['red_hit_4_plus'] / results['total_tests'] * 100
        red_hit_5_plus_rate = results['red_hit_5_plus'] / results['total_tests'] * 100
        
        # 目标达成度计算
        red_target = 1.5  # 目标红球命中数
        blue_target = 10.0  # 目标蓝球命中率
        
        red_achievement = min(avg_red_hits / red_target * 100, 100)
        blue_achievement = min(blue_hit_rate / blue_target * 100, 100)
        overall_achievement = (red_achievement + blue_achievement) / 2
        
        print("\n" + "="*60)
        print("终极优化7红球策略回测结果")
        print("="*60)
        print(f"测试期数: {results['total_tests']}")
        print(f"红球平均命中: {avg_red_hits:.2f}/7 ({avg_red_hits/7*100:.1f}%)")
        print(f"蓝球命中率: {blue_hit_rate:.1f}%")
        print(f"红球命中≥3个概率: {red_hit_3_plus_rate:.1f}%")
        print(f"红球命中≥4个概率: {red_hit_4_plus_rate:.1f}%")
        print(f"红球命中≥5个概率: {red_hit_5_plus_rate:.1f}%")
        print(f"红球命中4个次数: {results['red_hit_4_plus']}")
        print(f"红球命中5个次数: {results['red_hit_5_plus']}")
        
        print("\n目标达成度分析:")
        print(f"红球目标达成度: {red_achievement:.1f}% (目标: ≥{red_target}/7)")
        print(f"蓝球目标达成度: {blue_achievement:.1f}% (目标: ≥{blue_target}%)")
        print(f"综合达成度: {overall_achievement:.1f}%")
        
        # 理论分析
        theoretical_7_ball_expectation = 7 * (6/33)  # 7球选择的理论期望
        actual_vs_theoretical = (avg_red_hits / theoretical_7_ball_expectation - 1) * 100
        print(f"\n理论分析:")
        print(f"7球理论期望值: {theoretical_7_ball_expectation:.2f}")
        print(f"实际平均命中: {avg_red_hits:.2f}")
        print(f"比理论期望值: {actual_vs_theoretical:+.1f}%")
        
        # 成本效益分析
        cost_multiplier = 7  # 7红球需要购买7注
        hit_improvement = (avg_red_hits / (6 * 6/33) - 1) * 100  # 相比6红球的提升
        print(f"\n成本效益分析:")
        print(f"成本倍数: {cost_multiplier}x")
        print(f"相比6红球命中提升: {hit_improvement:+.1f}%")
        print(f"成本效益比: {hit_improvement/cost_multiplier:.2f}%")
        
        # 显示最佳预测记录
        if results['best_predictions']:
            print(f"\n最佳预测记录 (前15期):")
            best_sorted = sorted(results['best_predictions'], 
                               key=lambda x: (x['red_hits'], x['blue_hit']), reverse=True)
            for i, record in enumerate(best_sorted[:15]):
                print(f"{i+1:2d}. 第{record['period']:3d}期: "
                      f"预测 {record['predicted']['red']}|{record['predicted']['blue']} "
                      f"实际 {record['actual']['red']}|{record['actual']['blue']} "
                      f"命中 {record['red_hits']}/7红球 {record['blue_hit']}/1蓝球")
        
        print("\n" + "="*60)
        print("终极优化策略总结:")
        print("- 集成学习框架: 融合6种预测模型")
        print("- 动态策略切换: 根据历史表现自适应")
        print("- 深度蓝球分析: 专门的神经网络预测")
        print("- 时序注意力机制: 捕捉长短期趋势")
        print("- 量子启发融合: 多维特征量子叠加")
        print(f"- 平均命中提升: {hit_improvement:+.1f}%")
        print(f"- 成本增加: {cost_multiplier}倍")
        print(f"- 综合达成度: {overall_achievement:.1f}%")
        print("="*60)
    
    return results

if __name__ == "__main__":
    # 运行终极优化7红球策略回测
    results = ultimate_seven_red_balls_backtest()