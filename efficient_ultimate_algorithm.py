#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效终极双色球预测算法
优化版本：减少重复计算，提高运行效率
目标：红球命中率≥1.5/6 (25%)，蓝球命中率≥10%
"""

import json
import random
import math
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np

class EfficientUltimatePredictor:
    def __init__(self):
        self.data = []
        self.red_balls = []  # 历史红球数据
        self.blue_balls = []  # 历史蓝球数据
        
        # 预计算的特征（避免重复计算）
        self.red_frequency = defaultdict(int)
        self.blue_frequency = defaultdict(int)
        self.red_transitions = defaultdict(lambda: defaultdict(int))
        self.blue_transitions = defaultdict(int)
        self.recent_trends = defaultdict(list)
        
        # 参数优化
        self.recent_periods = 30  # 减少分析期数
        self.weight_decay = 0.90
        
    def load_data(self, filename='ssq_data.json'):
        """加载双色球历史数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            # 提取红球和蓝球数据
            for item in self.data:
                if 'number' in item and 'refernumber' in item:
                    # 解析红球号码
                    red_str = item['number'].strip()
                    red_numbers = [int(x) for x in red_str.split()]
                    self.red_balls.append(sorted(red_numbers))
                    
                    # 解析蓝球号码
                    blue_number = int(item['refernumber'])
                    self.blue_balls.append(blue_number)
            
            print(f"成功加载 {len(self.data)} 期历史数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def build_efficient_features(self, end_index=None):
        """高效构建特征（只计算必要的部分）"""
        if end_index is None:
            end_index = len(self.red_balls)
        
        # 清空之前的计算
        self.red_frequency.clear()
        self.blue_frequency.clear()
        self.red_transitions.clear()
        self.blue_transitions.clear()
        self.recent_trends.clear()
        
        # 只分析最近的数据
        start_index = max(0, end_index - 200)  # 最多分析200期
        
        # 1. 频率统计
        for i in range(start_index, end_index):
            if i < len(self.red_balls):
                for num in self.red_balls[i]:
                    self.red_frequency[num] += 1
            
            if i < len(self.blue_balls):
                self.blue_frequency[self.blue_balls[i]] += 1
        
        # 2. 简化的转移概率
        for i in range(start_index + 1, end_index):
            if i < len(self.red_balls) and i-1 < len(self.red_balls):
                prev_red = set(self.red_balls[i-1])
                curr_red = set(self.red_balls[i])
                
                for prev_num in prev_red:
                    for curr_num in curr_red:
                        self.red_transitions[prev_num][curr_num] += 1
            
            if i < len(self.blue_balls) and i-1 < len(self.blue_balls):
                prev_blue = self.blue_balls[i-1]
                curr_blue = self.blue_balls[i]
                self.blue_transitions[(prev_blue, curr_blue)] += 1
        
        # 3. 近期趋势（简化版）
        recent_start = max(0, end_index - self.recent_periods)
        for i in range(recent_start, end_index):
            period_weight = (i - recent_start + 1) / self.recent_periods
            
            if i < len(self.red_balls):
                for num in self.red_balls[i]:
                    self.recent_trends[num].append(period_weight)
            
            if i < len(self.blue_balls):
                blue_num = self.blue_balls[i]
                self.recent_trends[f'blue_{blue_num}'].append(period_weight)
    
    def calculate_red_scores(self, last_red):
        """计算红球得分"""
        scores = defaultdict(float)
        
        # 1. 频率权重 (30%)
        max_freq = max(self.red_frequency.values()) if self.red_frequency else 1
        for num in range(1, 34):
            freq_score = self.red_frequency.get(num, 0) / max_freq
            scores[num] += freq_score * 0.30
        
        # 2. 转移概率 (25%)
        for last_num in last_red:
            if last_num in self.red_transitions:
                total_trans = sum(self.red_transitions[last_num].values())
                if total_trans > 0:
                    for next_num, count in self.red_transitions[last_num].items():
                        trans_prob = count / total_trans
                        scores[next_num] += trans_prob * 0.25
        
        # 3. 近期趋势 (20%)
        for num in range(1, 34):
            if num in self.recent_trends:
                trend_score = sum(self.recent_trends[num]) / len(self.recent_trends[num])
                scores[num] += trend_score * 0.20
        
        # 4. 重号概率 (15%)
        repeat_bonus = 0.15
        for num in last_red:
            scores[num] += repeat_bonus * 0.15
        
        # 5. 分布平衡 (10%)
        for num in range(1, 34):
            # 区间平衡
            if 1 <= num <= 11:
                zone_bonus = 0.3
            elif 12 <= num <= 22:
                zone_bonus = 0.4  # 中间区间稍微加权
            else:
                zone_bonus = 0.3
            scores[num] += zone_bonus * 0.05
            
            # 奇偶平衡
            odd_bonus = 0.5 if num % 2 == 1 else 0.5
            scores[num] += odd_bonus * 0.05
        
        return scores
    
    def calculate_blue_scores(self, last_blue):
        """计算蓝球得分"""
        scores = defaultdict(float)
        
        # 1. 频率权重 (35%)
        max_freq = max(self.blue_frequency.values()) if self.blue_frequency else 1
        for num in range(1, 17):
            freq_score = self.blue_frequency.get(num, 0) / max_freq
            scores[num] += freq_score * 0.35
        
        # 2. 转移概率 (25%)
        total_blue_trans = sum(self.blue_transitions.values())
        if total_blue_trans > 0:
            for (prev, curr), count in self.blue_transitions.items():
                if prev == last_blue:
                    trans_prob = count / total_blue_trans
                    scores[curr] += trans_prob * 0.25
        
        # 3. 近期趋势 (25%)
        for num in range(1, 17):
            key = f'blue_{num}'
            if key in self.recent_trends:
                trend_score = sum(self.recent_trends[key]) / len(self.recent_trends[key])
                scores[num] += trend_score * 0.25
        
        # 4. 重号概率 (15%)
        scores[last_blue] += 0.08 * 0.15  # 降低重号概率
        
        return scores
    
    def smart_select_red(self, scores):
        """智能选择红球"""
        if not scores:
            return random.sample(range(1, 34), 6)
        
        # 按分数排序
        candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        selected = []
        
        # 策略1: 选择前10名中的4个
        top_10 = [num for num, score in candidates[:10]]
        selected.extend(random.sample(top_10, min(4, len(top_10))))
        
        # 策略2: 从中等分数中选择1个
        mid_range = [num for num, score in candidates[10:20] if num not in selected]
        if mid_range:
            selected.append(random.choice(mid_range))
        
        # 策略3: 随机选择1个保持多样性
        remaining = [i for i in range(1, 34) if i not in selected]
        if remaining and len(selected) < 6:
            selected.append(random.choice(remaining))
        
        # 确保选够6个
        while len(selected) < 6:
            remaining = [i for i in range(1, 34) if i not in selected]
            if remaining:
                selected.append(random.choice(remaining))
            else:
                break
        
        return sorted(selected[:6])
    
    def smart_select_blue(self, scores):
        """智能选择蓝球"""
        if not scores:
            return random.randint(1, 16)
        
        # 按分数排序，从前5个中加权选择
        candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:5]
        
        if candidates:
            weights = [score for num, score in candidates]
            if sum(weights) > 0:
                # 归一化权重
                weights = np.array(weights) / sum(weights)
                selected_idx = np.random.choice(len(candidates), p=weights)
                return candidates[selected_idx][0]
        
        return random.randint(1, 16)
    
    def predict(self, data_end_index=None):
        """生成预测"""
        if data_end_index is None:
            data_end_index = len(self.red_balls)
        
        if data_end_index == 0:
            return {
                'red': random.sample(range(1, 34), 6),
                'blue': random.randint(1, 16)
            }
        
        # 构建特征
        self.build_efficient_features(data_end_index)
        
        # 获取最后一期数据
        last_red = self.red_balls[data_end_index-1] if data_end_index > 0 else []
        last_blue = self.blue_balls[data_end_index-1] if data_end_index > 0 else 1
        
        # 计算得分
        red_scores = self.calculate_red_scores(last_red)
        blue_scores = self.calculate_blue_scores(last_blue)
        
        # 智能选择
        red_prediction = self.smart_select_red(red_scores)
        blue_prediction = self.smart_select_blue(blue_scores)
        
        return {
            'red': red_prediction,
            'blue': blue_prediction
        }

def efficient_ultimate_backtest(periods=500):
    """高效终极算法回测"""
    print("=== 高效终极双色球预测算法回测 ===")
    print(f"回测期数: {periods}")
    print(f"目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("="*50)
    
    predictor = EfficientUltimatePredictor()
    
    if not predictor.load_data():
        return
    
    if len(predictor.data) < periods + 100:
        print(f"数据不足，需要至少 {periods + 100} 期数据")
        return
    
    # 回测统计
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    # 使用前面的数据训练，后面的数据测试
    test_start = len(predictor.data) - periods
    
    for i in range(test_start, len(predictor.data)):
        # 使用到当前期之前的数据进行预测
        current_data_size = i
        
        # 生成预测
        prediction = predictor.predict(current_data_size)
        actual = predictor.data[i]
        
        # 解析实际开奖号码
        actual_red_str = actual['number'].strip()
        actual_red = [int(x) for x in actual_red_str.split()]
        actual_blue = int(actual['refernumber'])
        
        # 计算红球命中数
        red_hit_count = len(set(prediction['red']) & set(actual_red))
        red_hits.append(red_hit_count)
        
        # 计算蓝球命中
        blue_hit = 1 if prediction['blue'] == actual_blue else 0
        blue_hits += blue_hit
        
        # 记录优秀预测
        if red_hit_count >= 3 or blue_hit:
            best_predictions.append({
                'period': actual.get('issueno', f'第{i+1}期'),
                'red_hit': red_hit_count,
                'blue_hit': blue_hit,
                'predicted_red': prediction['red'],
                'actual_red': actual_red,
                'predicted_blue': prediction['blue'],
                'actual_blue': actual_blue
            })
        
        # 进度显示
        if (i - test_start + 1) % 100 == 0:
            current_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / len(red_hits)
            print(f"进度: {i - test_start + 1}/{periods}, 当前红球: {current_avg:.2f}/6, 蓝球: {current_blue_rate:.1%}")
    
    # 统计结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / periods
    
    print(f"\n=== 高效终极算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits}/{periods})")
    
    # 红球命中分布
    red_distribution = Counter(red_hits)
    print(f"\n红球命中分布:")
    for hits in sorted(red_distribution.keys()):
        count = red_distribution[hits]
        percentage = count / periods * 100
        print(f"  {hits}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 0.10
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target:.1%}, 实际: {blue_hit_rate:.1%}, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前{min(15, len(best_predictions))}期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:15], 1):
            blue_mark = "✓" if pred['blue_hit'] else "✗"
            print(f"{i:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_mark}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 算法优化建议
    print(f"\n=== 算法优化建议 ===")
    if red_achievement < 80:
        print(f"🔧 红球优化建议:")
        print(f"   - 调整频率权重和转移概率权重比例")
        print(f"   - 增强近期趋势分析的权重")
        print(f"   - 优化智能选择策略")
    
    if blue_achievement < 80:
        print(f"🔧 蓝球优化建议:")
        print(f"   - 增加蓝球转移概率分析深度")
        print(f"   - 优化蓝球频率权重计算")
        print(f"   - 调整重号概率参数")
    
    # 最终评估
    print(f"\n=== 高效算法评估 ===")
    if red_achievement >= 90 and blue_achievement >= 90:
        print(f"🎉 高效算法成功！已达成预期目标！")
    elif red_achievement >= 80 or blue_achievement >= 80:
        print(f"📈 高效算法表现优秀，非常接近目标！")
    elif red_achievement >= 70 or blue_achievement >= 70:
        print(f"🔧 高效算法表现良好，还有优化空间")
    else:
        print(f"⚠️ 需要进一步调整算法参数")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement
    }

if __name__ == "__main__":
    efficient_ultimate_backtest(500)