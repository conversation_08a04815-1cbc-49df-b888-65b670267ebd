#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化双色球预测算法
基于深度学习思想的多层特征融合
目标：红球命中率≥1.5/6 (25%)，蓝球命中率≥10%
"""

import json
import random
import math
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from itertools import combinations

class FinalOptimizedPredictor:
    def __init__(self):
        self.data = []
        self.red_balls = []  # 历史红球数据
        self.blue_balls = []  # 历史蓝球数据
        
        # 多层特征
        self.layer1_features = {}  # 基础特征层
        self.layer2_features = {}  # 组合特征层
        self.layer3_features = {}  # 高级特征层
        
        # 动态权重系统
        self.dynamic_weights = {
            'frequency': 0.25,
            'transition': 0.20,
            'trend': 0.15,
            'pattern': 0.15,
            'repeat': 0.10,
            'balance': 0.15
        }
        
        # 自适应参数
        self.recent_periods = 40
        self.weight_decay = 0.88
        self.learning_rate = 0.01
        
    def load_data(self, filename='ssq_data.json'):
        """加载双色球历史数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            # 提取红球和蓝球数据
            for item in self.data:
                if 'number' in item and 'refernumber' in item:
                    # 解析红球号码
                    red_str = item['number'].strip()
                    red_numbers = [int(x) for x in red_str.split()]
                    self.red_balls.append(sorted(red_numbers))
                    
                    # 解析蓝球号码
                    blue_number = int(item['refernumber'])
                    self.blue_balls.append(blue_number)
            
            print(f"成功加载 {len(self.data)} 期历史数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def build_layer1_features(self, end_index):
        """构建第一层基础特征"""
        start_index = max(0, end_index - 150)
        
        # 频率特征
        red_freq = defaultdict(int)
        blue_freq = defaultdict(int)
        
        # 位置特征
        position_freq = defaultdict(lambda: defaultdict(int))
        
        # 间隔特征
        interval_patterns = defaultdict(list)
        
        for i in range(start_index, end_index):
            if i < len(self.red_balls):
                red_list = self.red_balls[i]
                
                # 频率统计
                for num in red_list:
                    red_freq[num] += 1
                
                # 位置统计
                for pos, num in enumerate(red_list):
                    position_freq[pos][num] += 1
                
                # 间隔统计
                for j in range(len(red_list) - 1):
                    interval = red_list[j+1] - red_list[j]
                    interval_patterns[red_list[j]].append(interval)
            
            if i < len(self.blue_balls):
                blue_freq[self.blue_balls[i]] += 1
        
        self.layer1_features = {
            'red_freq': red_freq,
            'blue_freq': blue_freq,
            'position_freq': position_freq,
            'interval_patterns': interval_patterns
        }
    
    def build_layer2_features(self, end_index):
        """构建第二层组合特征"""
        start_index = max(0, end_index - 100)
        
        # 转移概率矩阵
        red_transitions = defaultdict(lambda: defaultdict(int))
        blue_transitions = defaultdict(int)
        
        # 配对出现频率
        pair_freq = defaultdict(int)
        
        # 三元组频率
        triplet_freq = defaultdict(int)
        
        # 和值分布
        sum_distribution = []
        
        for i in range(start_index + 1, end_index):
            if i < len(self.red_balls) and i-1 < len(self.red_balls):
                prev_red = set(self.red_balls[i-1])
                curr_red = set(self.red_balls[i])
                
                # 转移概率
                for prev_num in prev_red:
                    for curr_num in curr_red:
                        red_transitions[prev_num][curr_num] += 1
                
                # 配对分析
                curr_list = self.red_balls[i]
                for pair in combinations(curr_list, 2):
                    pair_freq[pair] += 1
                
                # 三元组分析
                for triplet in combinations(curr_list, 3):
                    triplet_freq[triplet] += 1
                
                # 和值分析
                sum_distribution.append(sum(curr_list))
            
            if i < len(self.blue_balls) and i-1 < len(self.blue_balls):
                prev_blue = self.blue_balls[i-1]
                curr_blue = self.blue_balls[i]
                blue_transitions[(prev_blue, curr_blue)] += 1
        
        self.layer2_features = {
            'red_transitions': red_transitions,
            'blue_transitions': blue_transitions,
            'pair_freq': pair_freq,
            'triplet_freq': triplet_freq,
            'sum_distribution': sum_distribution
        }
    
    def build_layer3_features(self, end_index):
        """构建第三层高级特征"""
        start_index = max(0, end_index - self.recent_periods)
        
        # 趋势分析
        trend_scores = defaultdict(list)
        
        # 周期性分析
        cycle_patterns = defaultdict(list)
        
        # 热度分析
        heat_scores = defaultdict(float)
        
        # 重号概率
        repeat_analysis = {'red': 0, 'blue': 0, 'total': 0}
        
        for i in range(start_index, end_index):
            period_weight = (i - start_index + 1) / (end_index - start_index)
            
            if i < len(self.red_balls):
                red_list = self.red_balls[i]
                
                # 趋势分析
                for num in red_list:
                    trend_scores[num].append(period_weight)
                
                # 周期性分析（每7期为一个周期）
                cycle_pos = i % 7
                for num in red_list:
                    cycle_patterns[cycle_pos].append(num)
                
                # 热度分析（近期出现频率加权）
                for num in red_list:
                    heat_scores[num] += period_weight
                
                # 重号分析
                if i > 0 and i-1 < len(self.red_balls):
                    prev_red = set(self.red_balls[i-1])
                    curr_red = set(red_list)
                    if prev_red & curr_red:
                        repeat_analysis['red'] += 1
                    repeat_analysis['total'] += 1
            
            if i < len(self.blue_balls):
                blue_num = self.blue_balls[i]
                
                # 蓝球趋势
                trend_scores[f'blue_{blue_num}'].append(period_weight)
                
                # 蓝球热度
                heat_scores[f'blue_{blue_num}'] += period_weight
                
                # 蓝球重号
                if i > 0 and i-1 < len(self.blue_balls):
                    if self.blue_balls[i-1] == blue_num:
                        repeat_analysis['blue'] += 1
        
        self.layer3_features = {
            'trend_scores': trend_scores,
            'cycle_patterns': cycle_patterns,
            'heat_scores': heat_scores,
            'repeat_analysis': repeat_analysis
        }
    
    def calculate_adaptive_red_scores(self, last_red):
        """自适应红球得分计算"""
        scores = defaultdict(float)
        
        # 第一层特征权重
        freq_weight = self.dynamic_weights['frequency']
        max_freq = max(self.layer1_features['red_freq'].values()) if self.layer1_features['red_freq'] else 1
        
        for num in range(1, 34):
            # 1. 频率特征
            freq_score = self.layer1_features['red_freq'].get(num, 0) / max_freq
            scores[num] += freq_score * freq_weight
            
            # 2. 位置特征
            position_score = 0
            for pos in range(6):
                if num in self.layer1_features['position_freq'][pos]:
                    position_score += self.layer1_features['position_freq'][pos][num] / 20
            scores[num] += position_score * 0.05
            
            # 3. 间隔特征
            interval_score = 0
            if num in self.layer1_features['interval_patterns']:
                intervals = self.layer1_features['interval_patterns'][num]
                if intervals:
                    avg_interval = sum(intervals[-10:]) / len(intervals[-10:])
                    # 预测下一个可能的号码
                    for last_num in last_red:
                        if abs((num - last_num) - avg_interval) < 3:
                            interval_score += 0.3
            scores[num] += interval_score * 0.08
        
        # 第二层特征权重
        trans_weight = self.dynamic_weights['transition']
        
        # 4. 转移概率
        for last_num in last_red:
            if last_num in self.layer2_features['red_transitions']:
                total_trans = sum(self.layer2_features['red_transitions'][last_num].values())
                if total_trans > 0:
                    for next_num, count in self.layer2_features['red_transitions'][last_num].items():
                        trans_prob = count / total_trans
                        scores[next_num] += trans_prob * trans_weight
        
        # 5. 配对分析
        pair_weight = self.dynamic_weights['pattern']
        for num in range(1, 34):
            pair_score = 0
            for last_num in last_red:
                pair_key = tuple(sorted([last_num, num]))
                if pair_key in self.layer2_features['pair_freq']:
                    pair_score += self.layer2_features['pair_freq'][pair_key] / 50
            scores[num] += pair_score * pair_weight
        
        # 第三层特征权重
        trend_weight = self.dynamic_weights['trend']
        
        # 6. 趋势分析
        for num in range(1, 34):
            if num in self.layer3_features['trend_scores']:
                trend_score = sum(self.layer3_features['trend_scores'][num]) / len(self.layer3_features['trend_scores'][num])
                scores[num] += trend_score * trend_weight
        
        # 7. 热度分析
        max_heat = max(self.layer3_features['heat_scores'].values()) if self.layer3_features['heat_scores'] else 1
        for num in range(1, 34):
            heat_score = self.layer3_features['heat_scores'].get(num, 0) / max_heat
            scores[num] += heat_score * 0.12
        
        # 8. 重号概率调整
        repeat_weight = self.dynamic_weights['repeat']
        repeat_rate = self.layer3_features['repeat_analysis']['red'] / max(1, self.layer3_features['repeat_analysis']['total'])
        
        for num in last_red:
            scores[num] += repeat_rate * repeat_weight
        
        # 9. 平衡性调整
        balance_weight = self.dynamic_weights['balance']
        
        # 和值约束
        if self.layer2_features['sum_distribution']:
            recent_sums = self.layer2_features['sum_distribution'][-20:]
            target_sum = sum(recent_sums) / len(recent_sums)
            
            # 根据当前选择调整分数
            for num in range(1, 34):
                # 简化的和值贡献评估
                sum_contribution = abs(num - target_sum/6)
                if sum_contribution < 5:
                    scores[num] += 0.2 * balance_weight
        
        # 区间平衡
        for num in range(1, 34):
            if 1 <= num <= 11:
                scores[num] += 0.33 * balance_weight * 0.5
            elif 12 <= num <= 22:
                scores[num] += 0.34 * balance_weight * 0.5
            else:
                scores[num] += 0.33 * balance_weight * 0.5
        
        return scores
    
    def calculate_adaptive_blue_scores(self, last_blue):
        """自适应蓝球得分计算"""
        scores = defaultdict(float)
        
        # 频率权重
        max_freq = max(self.layer1_features['blue_freq'].values()) if self.layer1_features['blue_freq'] else 1
        for num in range(1, 17):
            freq_score = self.layer1_features['blue_freq'].get(num, 0) / max_freq
            scores[num] += freq_score * 0.35
        
        # 转移概率
        total_blue_trans = sum(self.layer2_features['blue_transitions'].values())
        if total_blue_trans > 0:
            for (prev, curr), count in self.layer2_features['blue_transitions'].items():
                if prev == last_blue:
                    trans_prob = count / total_blue_trans
                    scores[curr] += trans_prob * 0.25
        
        # 趋势分析
        for num in range(1, 17):
            key = f'blue_{num}'
            if key in self.layer3_features['trend_scores']:
                trend_score = sum(self.layer3_features['trend_scores'][key]) / len(self.layer3_features['trend_scores'][key])
                scores[num] += trend_score * 0.20
        
        # 热度分析
        blue_heat_values = [v for k, v in self.layer3_features['heat_scores'].items() if isinstance(k, str) and k.startswith('blue_')]
        max_heat = max(blue_heat_values) if blue_heat_values else 1
        for num in range(1, 17):
            key = f'blue_{num}'
            heat_score = self.layer3_features['heat_scores'].get(key, 0) / max_heat
            scores[num] += heat_score * 0.15
        
        # 重号概率
        repeat_rate = self.layer3_features['repeat_analysis']['blue'] / max(1, self.layer3_features['repeat_analysis']['total'])
        scores[last_blue] += repeat_rate * 0.05
        
        return scores
    
    def intelligent_selection(self, scores, count, is_red=True):
        """智能选择算法"""
        if not scores:
            if is_red:
                return random.sample(range(1, 34), count)
            else:
                return random.randint(1, 16)
        
        if is_red:
            # 红球选择策略
            candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            selected = []
            
            # 多轮选择策略
            # 第一轮：选择前8个高分候选中的4个
            top_candidates = [num for num, score in candidates[:8]]
            selected.extend(random.sample(top_candidates, min(4, len(top_candidates))))
            
            # 第二轮：从次高分中选择1个
            mid_candidates = [num for num, score in candidates[8:16] if num not in selected]
            if mid_candidates:
                selected.append(random.choice(mid_candidates))
            
            # 第三轮：保持多样性，随机选择1个
            remaining = [i for i in range(1, 34) if i not in selected]
            if remaining and len(selected) < count:
                selected.append(random.choice(remaining))
            
            # 确保选够数量
            while len(selected) < count:
                remaining = [i for i in range(1, 34) if i not in selected]
                if remaining:
                    selected.append(random.choice(remaining))
                else:
                    break
            
            return sorted(selected[:count])
        
        else:
            # 蓝球选择策略
            candidates = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:6]
            
            if candidates:
                weights = [score for num, score in candidates]
                if sum(weights) > 0:
                    # 加权随机选择
                    weights = np.array(weights) / sum(weights)
                    selected_idx = np.random.choice(len(candidates), p=weights)
                    return candidates[selected_idx][0]
            
            return random.randint(1, 16)
    
    def update_dynamic_weights(self, prediction_accuracy):
        """根据预测准确率动态调整权重"""
        # 简化的权重更新机制
        if prediction_accuracy > 0.2:  # 如果准确率较高
            # 增强表现好的特征权重
            self.dynamic_weights['frequency'] *= (1 + self.learning_rate)
            self.dynamic_weights['transition'] *= (1 + self.learning_rate)
        else:
            # 调整权重分配
            self.dynamic_weights['trend'] *= (1 + self.learning_rate)
            self.dynamic_weights['pattern'] *= (1 + self.learning_rate)
        
        # 归一化权重
        total_weight = sum(self.dynamic_weights.values())
        for key in self.dynamic_weights:
            self.dynamic_weights[key] /= total_weight
    
    def predict(self, data_end_index=None):
        """生成最终优化预测"""
        if data_end_index is None:
            data_end_index = len(self.red_balls)
        
        if data_end_index == 0:
            return {
                'red': random.sample(range(1, 34), 6),
                'blue': random.randint(1, 16)
            }
        
        # 构建多层特征
        self.build_layer1_features(data_end_index)
        self.build_layer2_features(data_end_index)
        self.build_layer3_features(data_end_index)
        
        # 获取最后一期数据
        last_red = self.red_balls[data_end_index-1] if data_end_index > 0 else []
        last_blue = self.blue_balls[data_end_index-1] if data_end_index > 0 else 1
        
        # 计算自适应得分
        red_scores = self.calculate_adaptive_red_scores(last_red)
        blue_scores = self.calculate_adaptive_blue_scores(last_blue)
        
        # 智能选择
        red_prediction = self.intelligent_selection(red_scores, 6, is_red=True)
        blue_prediction = self.intelligent_selection(blue_scores, 1, is_red=False)
        
        return {
            'red': red_prediction,
            'blue': blue_prediction
        }

def final_optimized_backtest(periods=500):
    """最终优化算法回测"""
    print("=== 最终优化双色球预测算法回测 ===")
    print(f"回测期数: {periods}")
    print(f"目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("="*50)
    
    predictor = FinalOptimizedPredictor()
    
    if not predictor.load_data():
        return
    
    if len(predictor.data) < periods + 100:
        print(f"数据不足，需要至少 {periods + 100} 期数据")
        return
    
    # 回测统计
    red_hits = []
    blue_hits = 0
    best_predictions = []
    accuracy_history = []
    
    # 使用前面的数据训练，后面的数据测试
    test_start = len(predictor.data) - periods
    
    for i in range(test_start, len(predictor.data)):
        # 使用到当前期之前的数据进行预测
        current_data_size = i
        
        # 生成预测
        prediction = predictor.predict(current_data_size)
        actual = predictor.data[i]
        
        # 解析实际开奖号码
        actual_red_str = actual['number'].strip()
        actual_red = [int(x) for x in actual_red_str.split()]
        actual_blue = int(actual['refernumber'])
        
        # 计算红球命中数
        red_hit_count = len(set(prediction['red']) & set(actual_red))
        red_hits.append(red_hit_count)
        
        # 计算蓝球命中
        blue_hit = 1 if prediction['blue'] == actual_blue else 0
        blue_hits += blue_hit
        
        # 记录准确率用于动态调整
        current_accuracy = red_hit_count / 6
        accuracy_history.append(current_accuracy)
        
        # 动态调整权重（每50期调整一次）
        if len(accuracy_history) >= 50 and len(accuracy_history) % 50 == 0:
            recent_accuracy = sum(accuracy_history[-50:]) / 50
            predictor.update_dynamic_weights(recent_accuracy)
        
        # 记录优秀预测
        if red_hit_count >= 3 or blue_hit:
            best_predictions.append({
                'period': actual.get('issueno', f'第{i+1}期'),
                'red_hit': red_hit_count,
                'blue_hit': blue_hit,
                'predicted_red': prediction['red'],
                'actual_red': actual_red,
                'predicted_blue': prediction['blue'],
                'actual_blue': actual_blue
            })
        
        # 进度显示
        if (i - test_start + 1) % 100 == 0:
            current_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / len(red_hits)
            print(f"进度: {i - test_start + 1}/{periods}, 当前红球: {current_avg:.2f}/6, 蓝球: {current_blue_rate:.1%}")
    
    # 统计结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / periods
    
    print(f"\n=== 最终优化算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1%} ({blue_hits}/{periods})")
    
    # 红球命中分布
    red_distribution = Counter(red_hits)
    print(f"\n红球命中分布:")
    for hits in sorted(red_distribution.keys()):
        count = red_distribution[hits]
        percentage = count / periods * 100
        print(f"  {hits}个: {count}次 ({percentage:.1f}%)")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 0.10
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target:.1%}, 实际: {blue_hit_rate:.1%}, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前{min(20, len(best_predictions))}期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:20], 1):
            blue_mark = "✓" if pred['blue_hit'] else "✗"
            print(f"{i:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_mark}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 最终评估
    print(f"\n=== 最终算法评估 ===")
    if red_achievement >= 95 and blue_achievement >= 95:
        print(f"🎉 最终算法完美成功！超额完成目标！")
    elif red_achievement >= 85 and blue_achievement >= 85:
        print(f"🎊 最终算法成功！已达成预期目标！")
    elif red_achievement >= 75 or blue_achievement >= 75:
        print(f"📈 最终算法表现优秀，非常接近目标！")
    elif red_achievement >= 65 or blue_achievement >= 65:
        print(f"🔧 最终算法表现良好，还有优化空间")
    else:
        print(f"⚠️ 需要进一步调整算法参数")
    
    # 显示动态权重
    print(f"\n=== 最终动态权重 ===")
    for feature, weight in predictor.dynamic_weights.items():
        print(f"  {feature}: {weight:.3f}")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement
    }

if __name__ == "__main__":
    final_optimized_backtest(500)