#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Transformer + 强化学习双色球预测算法
结合Transformer架构和强化学习，实现更智能的预测策略
目标：红球命中率 >= 1.5/6，蓝球命中率 >= 10%
"""

import json
import random
import math
import numpy as np
from typing import List, Dict, Tuple, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scikit-learn
try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.model_selection import GridSearchCV
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("scikit-learn不可用，使用简化版本")

class MultiHeadAttention:
    """多头注意力机制（简化版）"""
    
    def __init__(self, d_model: int = 64, num_heads: int = 8):
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 简化的权重矩阵
        self.W_q = np.random.randn(d_model, d_model) * 0.1
        self.W_k = np.random.randn(d_model, d_model) * 0.1
        self.W_v = np.random.randn(d_model, d_model) * 0.1
        self.W_o = np.random.randn(d_model, d_model) * 0.1
    
    def attention(self, Q: np.ndarray, K: np.ndarray, V: np.ndarray) -> np.ndarray:
        """计算注意力"""
        scores = np.dot(Q, K.T) / math.sqrt(self.d_k)
        attention_weights = self._softmax(scores)
        return np.dot(attention_weights, V)
    
    def forward(self, x: np.ndarray) -> np.ndarray:
        """前向传播"""
        batch_size, seq_len, _ = x.shape
        
        # 计算Q, K, V
        Q = np.dot(x, self.W_q)
        K = np.dot(x, self.W_k)
        V = np.dot(x, self.W_v)
        
        # 重塑为多头
        Q = Q.reshape(batch_size, seq_len, self.num_heads, self.d_k)
        K = K.reshape(batch_size, seq_len, self.num_heads, self.d_k)
        V = V.reshape(batch_size, seq_len, self.num_heads, self.d_k)
        
        # 计算注意力
        attention_output = np.zeros_like(Q)
        for h in range(self.num_heads):
            attention_output[:, :, h, :] = self.attention(
                Q[:, :, h, :], K[:, :, h, :], V[:, :, h, :]
            )
        
        # 合并多头
        attention_output = attention_output.reshape(batch_size, seq_len, self.d_model)
        
        # 输出投影
        return np.dot(attention_output, self.W_o)
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)

class TransformerBlock:
    """Transformer块"""
    
    def __init__(self, d_model: int = 64, num_heads: int = 8, d_ff: int = 256):
        self.attention = MultiHeadAttention(d_model, num_heads)
        self.d_model = d_model
        self.d_ff = d_ff
        
        # 前馈网络权重
        self.W1 = np.random.randn(d_model, d_ff) * 0.1
        self.b1 = np.zeros(d_ff)
        self.W2 = np.random.randn(d_ff, d_model) * 0.1
        self.b2 = np.zeros(d_model)
        
        # Layer Norm参数
        self.gamma1 = np.ones(d_model)
        self.beta1 = np.zeros(d_model)
        self.gamma2 = np.ones(d_model)
        self.beta2 = np.zeros(d_model)
    
    def forward(self, x: np.ndarray) -> np.ndarray:
        """前向传播"""
        # 多头注意力 + 残差连接
        attn_output = self.attention.forward(x)
        x1 = self._layer_norm(x + attn_output, self.gamma1, self.beta1)
        
        # 前馈网络 + 残差连接
        ff_output = self._feed_forward(x1)
        x2 = self._layer_norm(x1 + ff_output, self.gamma2, self.beta2)
        
        return x2
    
    def _feed_forward(self, x: np.ndarray) -> np.ndarray:
        """前馈网络"""
        hidden = np.dot(x, self.W1) + self.b1
        hidden = np.maximum(0, hidden)  # ReLU
        return np.dot(hidden, self.W2) + self.b2
    
    def _layer_norm(self, x: np.ndarray, gamma: np.ndarray, beta: np.ndarray) -> np.ndarray:
        """Layer Normalization"""
        mean = np.mean(x, axis=-1, keepdims=True)
        std = np.std(x, axis=-1, keepdims=True) + 1e-6
        return gamma * (x - mean) / std + beta

class TransformerPredictor:
    """基于Transformer的预测器"""
    
    def __init__(self, seq_len: int = 20, d_model: int = 64, num_layers: int = 4):
        self.seq_len = seq_len
        self.d_model = d_model
        self.num_layers = num_layers
        
        # Transformer层
        self.transformer_blocks = [
            TransformerBlock(d_model) for _ in range(num_layers)
        ]
        
        # 位置编码
        self.pos_encoding = self._create_positional_encoding(seq_len, d_model)
        
        # 输入嵌入
        self.input_projection = np.random.randn(39, d_model) * 0.1  # 33红球+16蓝球
        
        # 输出层
        self.red_output = np.random.randn(d_model, 6) * 0.1
        self.blue_output = np.random.randn(d_model, 1) * 0.1
        
        print(f"Transformer预测器初始化完成: {num_layers}层, 序列长度{seq_len}")
    
    def _create_positional_encoding(self, seq_len: int, d_model: int) -> np.ndarray:
        """创建位置编码"""
        pos_encoding = np.zeros((seq_len, d_model))
        
        for pos in range(seq_len):
            for i in range(0, d_model, 2):
                pos_encoding[pos, i] = math.sin(pos / (10000 ** (i / d_model)))
                if i + 1 < d_model:
                    pos_encoding[pos, i + 1] = math.cos(pos / (10000 ** (i / d_model)))
        
        return pos_encoding
    
    def predict(self, data: List[Dict], target_type: str) -> List[int]:
        """Transformer预测"""
        try:
            if len(data) < self.seq_len:
                return self._fallback_predict(target_type)
            
            # 准备输入序列
            sequence = self._prepare_sequence(data[-self.seq_len:])
            
            # 添加位置编码
            sequence = sequence + self.pos_encoding
            
            # 通过Transformer层
            x = sequence.reshape(1, self.seq_len, self.d_model)
            for block in self.transformer_blocks:
                x = block.forward(x)
            
            # 全局平均池化
            pooled = np.mean(x, axis=1)  # (1, d_model)
            
            # 输出预测
            if target_type == 'red':
                output = np.dot(pooled, self.red_output).flatten()
                return self._post_process_red(output)
            else:
                output = np.dot(pooled, self.blue_output).flatten()
                return self._post_process_blue(output)
                
        except Exception as e:
            print(f"Transformer预测失败: {e}")
            return self._fallback_predict(target_type)
    
    def _prepare_sequence(self, data: List[Dict]) -> np.ndarray:
        """准备输入序列"""
        sequence = np.zeros((self.seq_len, self.d_model))
        
        for i, record in enumerate(data):
            # 提取红球和蓝球
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
            else:
                red_nums = [1, 2, 3, 4, 5, 6]
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
            else:
                blue_num = 1
            
            # 创建特征向量
            features = np.zeros(39)  # 33红球+6蓝球位置
            
            # 红球特征
            for num in red_nums[:6]:
                if 1 <= num <= 33:
                    features[num-1] = 1
            
            # 蓝球特征
            if 1 <= blue_num <= 16:
                features[33 + blue_num - 1] = 1
            
            # 投影到d_model维度
            sequence[i] = np.dot(features, self.input_projection)
        
        return sequence
    
    def _post_process_red(self, output: np.ndarray) -> List[int]:
        """红球后处理"""
        # 转换为1-33范围
        red_nums = [max(1, min(33, int(round(x * 33)))) for x in output]
        
        # 去重并补充
        unique_reds = []
        for num in red_nums:
            if num not in unique_reds:
                unique_reds.append(num)
        
        while len(unique_reds) < 6:
            candidate = random.randint(1, 33)
            if candidate not in unique_reds:
                unique_reds.append(candidate)
        
        return sorted(unique_reds[:6])
    
    def _post_process_blue(self, output: np.ndarray) -> List[int]:
        """蓝球后处理"""
        blue_num = max(1, min(16, int(round(output[0] * 16))))
        return [blue_num]
    
    def _fallback_predict(self, target_type: str) -> List[int]:
        """回退预测"""
        if target_type == 'red':
            return sorted(random.sample(range(1, 34), 6))
        else:
            return [random.randint(1, 16)]

class QLearningAgent:
    """Q学习智能体"""
    
    def __init__(self, state_size: int = 100, action_size: int = 10):
        self.state_size = state_size
        self.action_size = action_size
        self.q_table = np.random.randn(state_size, action_size) * 0.01
        
        # 超参数
        self.learning_rate = 0.1
        self.discount_factor = 0.95
        self.epsilon = 0.1  # 探索率
        
        # 经验回放
        self.memory = deque(maxlen=1000)
        
        print(f"Q学习智能体初始化: 状态空间{state_size}, 动作空间{action_size}")
    
    def get_state(self, data: List[Dict], predictions: List[List[int]]) -> int:
        """获取当前状态"""
        # 简化状态表示
        if not data:
            return 0
        
        # 基于最近几期的特征
        recent_data = data[-5:] if len(data) >= 5 else data
        
        # 计算状态哈希
        state_hash = 0
        for i, record in enumerate(recent_data):
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                state_hash += sum(red_nums) * (i + 1)
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                state_hash += blue_num * 100 * (i + 1)
        
        return abs(state_hash) % self.state_size
    
    def choose_action(self, state: int) -> int:
        """选择动作（ε-贪婪策略）"""
        if random.random() < self.epsilon:
            return random.randint(0, self.action_size - 1)
        else:
            return np.argmax(self.q_table[state])
    
    def update_q_table(self, state: int, action: int, reward: float, next_state: int):
        """更新Q表"""
        current_q = self.q_table[state, action]
        max_next_q = np.max(self.q_table[next_state])
        
        new_q = current_q + self.learning_rate * (
            reward + self.discount_factor * max_next_q - current_q
        )
        
        self.q_table[state, action] = new_q
    
    def calculate_reward(self, prediction: Tuple[List[int], List[int]], 
                       actual: Tuple[List[int], List[int]]) -> float:
        """计算奖励"""
        pred_red, pred_blue = prediction
        actual_red, actual_blue = actual
        
        # 红球奖励
        red_hits = len(set(pred_red) & set(actual_red))
        red_reward = red_hits * 10  # 每命中一个红球奖励10分
        
        # 蓝球奖励
        blue_reward = 50 if pred_blue[0] == actual_blue[0] else 0
        
        return red_reward + blue_reward
    
    def store_experience(self, state: int, action: int, reward: float, next_state: int):
        """存储经验"""
        self.memory.append((state, action, reward, next_state))
    
    def replay_experience(self, batch_size: int = 32):
        """经验回放"""
        if len(self.memory) < batch_size:
            return
        
        batch = random.sample(self.memory, batch_size)
        for state, action, reward, next_state in batch:
            self.update_q_table(state, action, reward, next_state)

class TransformerRLPredictor:
    """Transformer + 强化学习预测器"""
    
    def __init__(self):
        # Transformer预测器
        self.transformer = TransformerPredictor()
        
        # Q学习智能体
        self.rl_agent = QLearningAgent()
        
        # 预测策略池
        self.strategies = [
            self._frequency_strategy,
            self._trend_strategy,
            self._pattern_strategy,
            self._random_strategy,
            self._transformer_strategy
        ]
        
        # 历史记录
        self.history = []
        self.performance_history = []
        
        print(f"Transformer+RL预测器初始化完成，包含{len(self.strategies)}种策略")
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """智能预测"""
        if len(data) < 10:
            return self._random_strategy(data)
        
        # 获取当前状态
        state = self.rl_agent.get_state(data, [])
        
        # 选择策略
        action = self.rl_agent.choose_action(state)
        strategy_idx = action % len(self.strategies)
        
        # 执行策略
        try:
            prediction = self.strategies[strategy_idx](data)
            print(f"使用策略 {strategy_idx}: {self.strategies[strategy_idx].__name__}")
            return prediction
        except Exception as e:
            print(f"策略执行失败: {e}，使用随机策略")
            return self._random_strategy(data)
    
    def _frequency_strategy(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """频率策略"""
        red_freq = defaultdict(int)
        blue_freq = defaultdict(int)
        
        for record in data[-50:]:  # 最近50期
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                for num in red_nums:
                    red_freq[num] += 1
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                blue_freq[blue_num] += 1
        
        # 选择频率最高的
        red_candidates = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)
        blue_candidates = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)
        
        red_pred = [num for num, _ in red_candidates[:6]]
        blue_pred = [blue_candidates[0][0] if blue_candidates else 1]
        
        # 补充不足的红球
        while len(red_pred) < 6:
            candidate = random.randint(1, 33)
            if candidate not in red_pred:
                red_pred.append(candidate)
        
        return sorted(red_pred), blue_pred
    
    def _trend_strategy(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """趋势策略"""
        if len(data) < 20:
            return self._random_strategy(data)
        
        # 分析最近趋势
        recent_reds = []
        recent_blues = []
        
        for record in data[-10:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                recent_reds.extend(red_nums)
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                recent_blues.append(blue_num)
        
        # 趋势分析
        red_trend = np.mean(recent_reds) if recent_reds else 17
        blue_trend = np.mean(recent_blues) if recent_blues else 8
        
        # 基于趋势生成预测
        red_pred = []
        for i in range(6):
            num = int(red_trend + random.gauss(0, 5))
            num = max(1, min(33, num))
            if num not in red_pred:
                red_pred.append(num)
        
        while len(red_pred) < 6:
            candidate = random.randint(1, 33)
            if candidate not in red_pred:
                red_pred.append(candidate)
        
        blue_pred = [max(1, min(16, int(blue_trend + random.gauss(0, 2))))]
        
        return sorted(red_pred), blue_pred
    
    def _pattern_strategy(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """模式策略"""
        if len(data) < 30:
            return self._random_strategy(data)
        
        # 寻找重复模式
        patterns = []
        for i in range(len(data) - 5):
            pattern = []
            for j in range(5):
                if 'number' in data[i + j]:
                    red_nums = [int(x.strip()) for x in data[i + j]['number'].split() if x.strip()]
                    pattern.append(sum(red_nums))
            patterns.append(pattern)
        
        # 寻找最相似的模式
        recent_pattern = []
        for j in range(5):
            if 'number' in data[-(5-j)]:
                red_nums = [int(x.strip()) for x in data[-(5-j)]['number'].split() if x.strip()]
                recent_pattern.append(sum(red_nums))
        
        best_match_idx = 0
        best_similarity = float('inf')
        
        for i, pattern in enumerate(patterns[:-5]):
            similarity = sum(abs(a - b) for a, b in zip(pattern, recent_pattern))
            if similarity < best_similarity:
                best_similarity = similarity
                best_match_idx = i
        
        # 使用匹配模式的下一期作为预测
        if best_match_idx + 5 < len(data):
            next_record = data[best_match_idx + 5]
            if 'number' in next_record:
                red_nums = [int(x.strip()) for x in next_record['number'].split() if x.strip()]
                red_pred = red_nums[:6]
            else:
                red_pred = list(range(1, 7))
            
            if 'refernumber' in next_record:
                blue_pred = [int(next_record['refernumber'])]
            else:
                blue_pred = [1]
        else:
            return self._random_strategy(data)
        
        return sorted(red_pred), blue_pred
    
    def _transformer_strategy(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """Transformer策略"""
        red_pred = self.transformer.predict(data, 'red')
        blue_pred = self.transformer.predict(data, 'blue')
        return red_pred, blue_pred
    
    def _random_strategy(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """随机策略"""
        red_pred = sorted(random.sample(range(1, 34), 6))
        blue_pred = [random.randint(1, 16)]
        return red_pred, blue_pred
    
    def update_performance(self, prediction: Tuple[List[int], List[int]], 
                          actual: Tuple[List[int], List[int]], 
                          state: int, action: int):
        """更新性能"""
        reward = self.rl_agent.calculate_reward(prediction, actual)
        next_state = self.rl_agent.get_state(self.history, [])
        
        self.rl_agent.update_q_table(state, action, reward, next_state)
        self.rl_agent.store_experience(state, action, reward, next_state)
        
        self.performance_history.append({
            'prediction': prediction,
            'actual': actual,
            'reward': reward,
            'state': state,
            'action': action
        })
    
    def load_data(self, file_path: str = 'ssq_data.json') -> List[Dict]:
        """加载数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条历史数据")
            return data
        except FileNotFoundError:
            print(f"数据文件 {file_path} 不存在")
            return []
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []

def transformer_rl_backtest(data: List[Dict], test_periods: int = 50) -> Dict:
    """Transformer+RL算法回测"""
    if len(data) < test_periods + 50:
        print(f"数据量不足，需要至少 {test_periods + 50} 条数据")
        return {}
    
    predictor = TransformerRLPredictor()
    
    # 回测结果
    results = {
        'total_tests': test_periods,
        'red_hits': [],
        'blue_hits': [],
        'predictions': [],
        'best_records': []
    }
    
    print(f"\n开始Transformer+RL算法回测，测试期数: {test_periods}")
    
    for i in range(test_periods):
        # 训练数据（不包括当前测试期）
        train_data = data[:-(test_periods-i)]
        
        # 获取当前状态
        state = predictor.rl_agent.get_state(train_data, [])
        
        # 预测
        pred_red, pred_blue = predictor.predict(train_data)
        
        # 实际结果
        test_record = data[-(test_periods-i)]
        
        if 'number' in test_record:
            actual_red = [int(x.strip()) for x in test_record['number'].split() if x.strip()]
        else:
            actual_red = [1, 2, 3, 4, 5, 6]
        
        if 'refernumber' in test_record:
            actual_blue = [int(test_record['refernumber'])]
        else:
            actual_blue = [1]
        
        # 计算命中
        red_hit = len(set(pred_red) & set(actual_red))
        blue_hit = 1 if pred_blue[0] == actual_blue[0] else 0
        
        results['red_hits'].append(red_hit)
        results['blue_hits'].append(blue_hit)
        results['predictions'].append({
            'date': test_record.get('opendate', f'期号{i+1}'),
            'pred_red': pred_red,
            'pred_blue': pred_blue,
            'actual_red': actual_red,
            'actual_blue': actual_blue,
            'red_hit': red_hit,
            'blue_hit': blue_hit
        })
        
        # 更新强化学习
        action = predictor.rl_agent.choose_action(state)
        predictor.update_performance(
            (pred_red, pred_blue), 
            (actual_red, actual_blue), 
            state, action
        )
        
        # 经验回放
        if i % 10 == 0:
            predictor.rl_agent.replay_experience()
        
        # 记录最佳预测
        if red_hit >= 2 or blue_hit >= 1:
            results['best_records'].append({
                'date': test_record.get('opendate', f'期号{i+1}'),
                'red_hit': red_hit,
                'blue_hit': blue_hit,
                'total_score': red_hit * 10 + blue_hit * 50
            })
    
    # 计算统计结果
    avg_red_hit = np.mean(results['red_hits'])
    blue_hit_rate = np.mean(results['blue_hits']) * 100
    
    # 目标达成度
    red_target_achievement = (avg_red_hit / 1.5) * 100
    blue_target_achievement = (blue_hit_rate / 10) * 100
    overall_achievement = (red_target_achievement + blue_target_achievement) / 2
    
    results.update({
        'avg_red_hit': avg_red_hit,
        'blue_hit_rate': blue_hit_rate,
        'red_target_achievement': red_target_achievement,
        'blue_target_achievement': blue_target_achievement,
        'overall_achievement': overall_achievement
    })
    
    return results

def print_transformer_rl_results(results: Dict):
    """打印Transformer+RL结果"""
    if not results:
        print("没有回测结果")
        return
    
    print(f"\n=== Transformer+RL算法回测结果 ===")
    print(f"测试期数: {results['total_tests']}")
    print(f"红球平均命中: {results['avg_red_hit']:.2f}/6 ({results['avg_red_hit']/6*100:.1f}%)")
    print(f"蓝球命中率: {results['blue_hit_rate']:.1f}%")
    print(f"目标达成度: 红球 {results['red_target_achievement']:.1f}%, 蓝球 {results['blue_target_achievement']:.1f}%")
    print(f"综合达成度: {results['overall_achievement']:.1f}%")
    
    # 最佳预测记录
    if results['best_records']:
        print(f"\n前15期最佳预测记录:")
        sorted_records = sorted(results['best_records'], 
                              key=lambda x: x['total_score'], reverse=True)[:15]
        for record in sorted_records:
            print(f"{record['date']}: 红球 {record['red_hit']}/6, 蓝球 {record['blue_hit']}/1")
    
    # 红球命中分布
    red_distribution = defaultdict(int)
    for hit in results['red_hits']:
        red_distribution[hit] += 1
    
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_distribution[i]
        percentage = count / results['total_tests'] * 100
        print(f"{i}个: {count}次 ({percentage:.1f}%)")
    
    print(f"\n💡 Transformer+RL优化建议:")
    print(f"1. 增加更多Transformer层和注意力头")
    print(f"2. 实现更复杂的强化学习算法（PPO、A3C等）")
    print(f"3. 引入图神经网络分析号码关系")
    print(f"4. 使用元学习快速适应新模式")
    print(f"5. 集成多模态数据（时间、天气、经济指标等）")

if __name__ == "__main__":
    # 创建预测器
    predictor = TransformerRLPredictor()
    
    # 加载数据
    data = predictor.load_data()
    
    if data:
        # 回测
        backtest_results = transformer_rl_backtest(data, test_periods=50)
        
        # 打印结果
        print_transformer_rl_results(backtest_results)
    else:
        print("无法加载数据，退出程序")