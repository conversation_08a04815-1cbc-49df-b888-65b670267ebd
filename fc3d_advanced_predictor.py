import requests
import json
import time
import random
import math
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class AdvancedFC3DPredictor:
    """高级福彩3D预测器 - 集成多种算法的智能预测系统"""
    
    def __init__(self, data=None):
        """初始化高级福彩3D预测器"""
        self.data = data if data else []
        self.digit_range = list(range(0, 10))  # 每位数字范围0-9
        
        # 神经网络参数
        self.input_dim = 45
        self.hidden_dim = 128
        self.output_dim = 10
        
        # 初始化神经网络权重
        self.init_neural_networks()
        
        # 算法权重（可动态调整）
        self.algorithm_weights = {
            'frequency_analysis': 0.15,
            'position_pattern': 0.15,
            'sequence_learning': 0.12,
            'sum_distribution': 0.12,
            'span_analysis': 0.10,
            'neural_prediction': 0.10,
            'trend_analysis': 0.08,
            'markov_chain': 0.08,
            'hot_cold_analysis': 0.05,
            'cycle_analysis': 0.05
        }
        
        # 预测历史和性能统计
        self.prediction_history = []
        self.performance_stats = {
            'total_predictions': 0,
            'exact_matches': 0,      # 直选命中
            'group3_matches': 0,     # 组选3命中
            'group6_matches': 0,     # 组选6命中
            'position_matches': [0, 0, 0],  # 各位置命中次数
            'accuracy_rate': 0.0,
            'recent_performance': []
        }
        
        # 学习参数
        self.learning_rate = 0.01
        self.adaptation_threshold = 0.3
        
        # 缓存计算结果
        self.cache = {}
    
    def init_neural_networks(self):
        """初始化神经网络权重"""
        # 主神经网络
        self.main_nn = {
            'input_hidden': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'hidden_hidden': np.random.randn(self.hidden_dim, self.hidden_dim) * 0.1,
            'hidden_output': np.random.randn(self.hidden_dim, self.output_dim) * 0.1
        }
        
        # 位置特定神经网络
        self.position_nn = {
            'hundreds': np.random.randn(self.input_dim, self.output_dim) * 0.1,
            'tens': np.random.randn(self.input_dim, self.output_dim) * 0.1,
            'units': np.random.randn(self.input_dim, self.output_dim) * 0.1
        }
        
        # LSTM权重（简化版）
        self.lstm_weights = {
            'forget_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'input_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'output_gate': np.random.randn(self.input_dim, self.hidden_dim) * 0.1,
            'cell_state': np.random.randn(self.input_dim, self.hidden_dim) * 0.1
        }
    
    def extract_comprehensive_features(self, sequence_length=50):
        """提取全面的特征向量"""
        if not self.data or len(self.data) < sequence_length:
            return np.zeros(self.input_dim)
        
        recent_data = self.data[-sequence_length:]
        features = []
        
        # 提取数字序列
        numbers = self._extract_numbers(recent_data)
        if not numbers:
            return np.zeros(self.input_dim)
        
        # 1. 基础频率特征 (10维)
        all_digits = [digit for num in numbers for digit in num]
        frequency = Counter(all_digits)
        freq_features = [frequency[i] / len(all_digits) for i in range(10)]
        features.extend(freq_features)
        
        # 2. 位置频率特征 (10维)
        pos_freq = []
        for pos in range(3):
            pos_digits = [num[pos] for num in numbers]
            pos_counter = Counter(pos_digits)
            # 计算最高频数字的频率
            max_freq = max(pos_counter.values()) if pos_counter else 0
            total = len(pos_digits)
            pos_freq.append(max_freq / total if total > 0 else 0)
        
        # 扩展到10维
        pos_freq.extend([0] * 7)
        features.extend(pos_freq[:10])
        
        # 3. 和值分布特征 (5维)
        sums = [sum(num) for num in numbers]
        sum_features = [
            np.mean(sums),
            np.std(sums) if len(sums) > 1 else 0,
            len([s for s in sums if s <= 10]) / len(sums),  # 小和值比例
            len([s for s in sums if 11 <= s <= 19]) / len(sums),  # 中和值比例
            len([s for s in sums if s >= 20]) / len(sums)  # 大和值比例
        ]
        features.extend(sum_features)
        
        # 4. 跨度分布特征 (5维)
        spans = [max(num) - min(num) for num in numbers]
        span_features = [
            np.mean(spans),
            np.std(spans) if len(spans) > 1 else 0,
            len([s for s in spans if s <= 3]) / len(spans),  # 小跨度比例
            len([s for s in spans if 4 <= s <= 6]) / len(spans),  # 中跨度比例
            len([s for s in spans if s >= 7]) / len(spans)  # 大跨度比例
        ]
        features.extend(span_features)
        
        # 5. 奇偶比特征 (5维)
        odd_even_features = []
        for pos in range(3):
            pos_digits = [num[pos] for num in numbers]
            odd_count = sum(1 for d in pos_digits if d % 2 == 1)
            odd_even_features.append(odd_count / len(pos_digits) if pos_digits else 0)
        odd_even_features.extend([0, 0])
        features.extend(odd_even_features)
        
        # 6. 大小比特征 (5维)
        big_small_features = []
        for pos in range(3):
            pos_digits = [num[pos] for num in numbers]
            big_count = sum(1 for d in pos_digits if d >= 5)
            big_small_features.append(big_count / len(pos_digits) if pos_digits else 0)
        big_small_features.extend([0, 0])
        features.extend(big_small_features)
        
        # 7. 连号特征 (5维)
        consecutive_features = []
        for num in numbers[-10:]:  # 最近10期
            consecutive_count = 0
            sorted_num = sorted(num)
            for i in range(len(sorted_num) - 1):
                if sorted_num[i+1] - sorted_num[i] == 1:
                    consecutive_count += 1
            consecutive_features.append(consecutive_count)
        
        # 统计特征
        if consecutive_features:
            conn_stats = [
                np.mean(consecutive_features),
                np.max(consecutive_features),
                len([c for c in consecutive_features if c > 0]) / len(consecutive_features),
                0, 0
            ]
        else:
            conn_stats = [0] * 5
        features.extend(conn_stats)
        
        # 确保特征向量长度正确
        while len(features) < self.input_dim:
            features.append(0)
        
        return np.array(features[:self.input_dim])
    
    def _extract_numbers(self, data):
        """从数据中提取数字序列"""
        numbers = []
        for entry in data:
            if 'number' in entry:
                num_str = str(entry['number']).replace(' ', '').replace('-', '')
                if len(num_str) >= 3 and num_str.isdigit():
                    hundreds = int(num_str[0])
                    tens = int(num_str[1])
                    units = int(num_str[2])
                    numbers.append([hundreds, tens, units])
        return numbers
    
    # 激活函数
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def relu(self, x):
        return np.maximum(0, x)
    
    def tanh(self, x):
        return np.tanh(x)
    
    def softmax(self, x):
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def frequency_analysis_advanced(self):
        """高级频率分析"""
        if len(self.data) < 20:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 分析不同时间窗口的频率
        windows = [30, 50, 100]
        position_scores = [defaultdict(float) for _ in range(3)]
        
        for window in windows:
            window_data = self.data[-window:] if len(self.data) >= window else self.data
            numbers = self._extract_numbers(window_data)
            
            if not numbers:
                continue
            
            # 计算每个位置的频率权重
            weight = 1.0 / len(windows)
            for pos in range(3):
                pos_digits = [num[pos] for num in numbers]
                counter = Counter(pos_digits)
                total = len(pos_digits)
                
                for digit, count in counter.items():
                    # 频率越高，分数越高，但加入衰减因子
                    freq_score = (count / total) * weight
                    # 添加反向权重，避免过度集中
                    diversity_bonus = (1 - freq_score) * 0.1
                    position_scores[pos][digit] += freq_score + diversity_bonus
        
        # 选择每个位置的最佳数字
        prediction = []
        for pos in range(3):
            if position_scores[pos]:
                # 使用加权随机选择，增加多样性
                digits = list(position_scores[pos].keys())
                weights = list(position_scores[pos].values())
                
                # 归一化权重
                total_weight = sum(weights)
                if total_weight > 0:
                    weights = [w / total_weight for w in weights]
                    
                    # 加权随机选择
                    selected = np.random.choice(digits, p=weights)
                    prediction.append(selected)
                else:
                    prediction.append(random.randint(0, 9))
            else:
                prediction.append(random.randint(0, 9))
        
        return prediction
    
    def markov_chain_prediction(self):
        """马尔可夫链预测"""
        if len(self.data) < 30:
            return [random.randint(0, 9) for _ in range(3)]
        
        numbers = self._extract_numbers(self.data[-100:])
        if len(numbers) < 2:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 构建转移矩阵
        transition_matrices = [np.zeros((10, 10)) for _ in range(3)]
        
        for i in range(len(numbers) - 1):
            curr_num = numbers[i]
            next_num = numbers[i + 1]
            
            for pos in range(3):
                curr_digit = curr_num[pos]
                next_digit = next_num[pos]
                transition_matrices[pos][curr_digit][next_digit] += 1
        
        # 归一化转移矩阵
        for pos in range(3):
            for i in range(10):
                row_sum = np.sum(transition_matrices[pos][i])
                if row_sum > 0:
                    transition_matrices[pos][i] /= row_sum
        
        # 基于最后一期预测下一期
        last_number = numbers[-1]
        prediction = []
        
        for pos in range(3):
            last_digit = last_number[pos]
            probabilities = transition_matrices[pos][last_digit]
            
            if np.sum(probabilities) > 0:
                # 加权随机选择
                predicted_digit = np.random.choice(10, p=probabilities)
                prediction.append(predicted_digit)
            else:
                prediction.append(random.randint(0, 9))
        
        return prediction
    
    def neural_network_prediction(self):
        """神经网络预测"""
        features = self.extract_comprehensive_features()
        
        # 主神经网络前向传播
        hidden1 = self.relu(np.dot(features, self.main_nn['input_hidden']))
        hidden2 = self.relu(np.dot(hidden1, self.main_nn['hidden_hidden']))
        main_output = self.softmax(np.dot(hidden2, self.main_nn['hidden_output']))
        
        # 位置特定预测
        position_predictions = []
        position_names = ['hundreds', 'tens', 'units']
        
        for pos_name in position_names:
            pos_output = self.softmax(np.dot(features, self.position_nn[pos_name]))
            # 结合主网络和位置网络的输出
            combined_output = 0.6 * pos_output + 0.4 * main_output
            predicted_digit = np.argmax(combined_output)
            position_predictions.append(predicted_digit)
        
        return position_predictions
    
    def hot_cold_analysis(self):
        """冷热号分析"""
        if len(self.data) < 50:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 分析最近期数的冷热情况
        recent_periods = [10, 20, 30]
        position_hot_cold = [defaultdict(int) for _ in range(3)]
        
        for period in recent_periods:
            period_data = self.data[-period:] if len(self.data) >= period else self.data
            numbers = self._extract_numbers(period_data)
            
            for pos in range(3):
                pos_digits = [num[pos] for num in numbers]
                counter = Counter(pos_digits)
                
                # 计算冷热度
                avg_freq = len(pos_digits) / 10  # 平均频率
                for digit in range(10):
                    freq = counter.get(digit, 0)
                    if freq > avg_freq * 1.2:  # 热号
                        position_hot_cold[pos][digit] += 1
                    elif freq < avg_freq * 0.8:  # 冷号
                        position_hot_cold[pos][digit] -= 1
        
        # 选择适中的号码（不太热也不太冷）
        prediction = []
        for pos in range(3):
            # 找到冷热度适中的数字
            moderate_digits = []
            for digit in range(10):
                hot_cold_score = position_hot_cold[pos][digit]
                if -1 <= hot_cold_score <= 1:  # 适中范围
                    moderate_digits.append(digit)
            
            if moderate_digits:
                prediction.append(random.choice(moderate_digits))
            else:
                prediction.append(random.randint(0, 9))
        
        return prediction
    
    def cycle_analysis(self):
        """周期性分析"""
        if len(self.data) < 60:
            return [random.randint(0, 9) for _ in range(3)]
        
        numbers = self._extract_numbers(self.data)
        if len(numbers) < 30:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 分析7天、14天、30天周期
        cycles = [7, 14, 30]
        prediction = []
        
        for pos in range(3):
            pos_digits = [num[pos] for num in numbers]
            cycle_predictions = []
            
            for cycle in cycles:
                if len(pos_digits) >= cycle:
                    # 找到cycle天前的数字
                    cycle_ago_digit = pos_digits[-cycle]
                    cycle_predictions.append(cycle_ago_digit)
            
            if cycle_predictions:
                # 使用最常见的周期预测
                counter = Counter(cycle_predictions)
                most_common = counter.most_common(1)[0][0]
                prediction.append(most_common)
            else:
                prediction.append(random.randint(0, 9))
        
        return prediction
    
    def ensemble_prediction(self):
        """集成预测方法"""
        if not self.data:
            return [random.randint(0, 9) for _ in range(3)]
        
        # 获取各种算法的预测结果
        predictions = {
            'frequency': self.frequency_analysis_advanced(),
            'markov': self.markov_chain_prediction(),
            'neural': self.neural_network_prediction(),
            'hot_cold': self.hot_cold_analysis(),
            'cycle': self.cycle_analysis()
        }
        
        # 投票机制
        final_prediction = []
        for pos in range(3):
            votes = defaultdict(float)
            
            for method, pred in predictions.items():
                if method in self.algorithm_weights:
                    weight = self.algorithm_weights[method]
                    votes[pred[pos]] += weight
            
            # 选择得票最高的数字
            if votes:
                best_digit = max(votes.items(), key=lambda x: x[1])[0]
                final_prediction.append(best_digit)
            else:
                final_prediction.append(random.randint(0, 9))
        
        return final_prediction
    
    def generate_smart_combinations(self, count=8):
        """生成智能组合"""
        combinations = []
        
        # 生成不同策略的组合
        strategies = [
            self.ensemble_prediction,
            self.frequency_analysis_advanced,
            self.neural_network_prediction,
            self.markov_chain_prediction,
            self.hot_cold_analysis,
            self.cycle_analysis
        ]
        
        for i in range(count):
            # 随机选择策略或使用集成方法
            if i == 0:
                # 第一个使用集成方法
                prediction = self.ensemble_prediction()
            else:
                # 其他使用不同策略
                strategy = strategies[i % len(strategies)]
                prediction = strategy()
            
            # 确保预测有效
            prediction = [max(0, min(9, digit)) for digit in prediction]
            combinations.append(prediction)
        
        # 去重
        unique_combinations = []
        seen = set()
        for combo in combinations:
            combo_str = ''.join(map(str, combo))
            if combo_str not in seen:
                seen.add(combo_str)
                unique_combinations.append(combo)
        
        return unique_combinations[:count]
    
    def analyze_combination(self, combination):
        """分析组合特征"""
        digits = list(combination)
        unique_digits = set(digits)
        
        analysis = {
            'number': ''.join(map(str, digits)),
            'sum': sum(digits),
            'span': max(digits) - min(digits),
            'type': '',
            'description': '',
            'odd_count': sum(1 for d in digits if d % 2 == 1),
            'even_count': sum(1 for d in digits if d % 2 == 0),
            'big_count': sum(1 for d in digits if d >= 5),
            'small_count': sum(1 for d in digits if d < 5)
        }
        
        # 判断类型
        if len(unique_digits) == 3:
            analysis['type'] = '组选6'
            analysis['description'] = '三个数字都不相同，共6种排列'
        elif len(unique_digits) == 2:
            analysis['type'] = '组选3'
            analysis['description'] = '有两个数字相同，共3种排列'
        else:
            analysis['type'] = '豹子号'
            analysis['description'] = '三个数字都相同，只有1种排列'
        
        return analysis
    
    def validate_prediction_accuracy(self, test_periods=20):
        """验证预测准确性"""
        if len(self.data) < test_periods + 50:
            return None
        
        # 使用历史数据进行回测
        test_data = self.data[:-test_periods]
        validation_data = self.data[-test_periods:]
        
        # 临时保存当前数据
        original_data = self.data
        self.data = test_data
        
        results = {
            'total_tests': test_periods,
            'exact_matches': 0,
            'position_matches': [0, 0, 0],
            'group_matches': 0,
            'accuracy_rate': 0.0
        }
        
        for i, actual_entry in enumerate(validation_data):
            # 生成预测
            prediction = self.ensemble_prediction()
            
            # 提取实际结果
            if 'number' in actual_entry:
                actual_str = str(actual_entry['number']).replace(' ', '').replace('-', '')
                if len(actual_str) >= 3 and actual_str.isdigit():
                    actual = [int(actual_str[j]) for j in range(3)]
                    
                    # 检查准确性
                    if prediction == actual:
                        results['exact_matches'] += 1
                    
                    # 检查位置匹配
                    for pos in range(3):
                        if prediction[pos] == actual[pos]:
                            results['position_matches'][pos] += 1
                    
                    # 检查组选匹配
                    if set(prediction) == set(actual):
                        results['group_matches'] += 1
            
            # 更新数据用于下次预测
            self.data.append(actual_entry)
        
        # 恢复原始数据
        self.data = original_data
        
        # 计算准确率
        results['accuracy_rate'] = results['exact_matches'] / test_periods
        results['position_accuracy'] = [matches / test_periods for matches in results['position_matches']]
        results['group_accuracy'] = results['group_matches'] / test_periods
        
        return results

# 数据获取和管理函数
APPKEY = 'eb6cc4f9bf4a23b4'
FC3D_DATA_FILE = 'fc3d_data.json'

def get_fc3d_data_from_api(count=200):
    """从API获取福彩3D数据 - 使用极速数据API"""
    # 使用与双色球相同的API服务商
    url = f'https://api.jisuapi.com/caipiao/history?appkey={APPKEY}&caipiaoid=35&num={min(count, 20)}&start=0'
    
    try:
        response = requests.get(url, timeout=15)
        response.raise_for_status()
        result = response.json()
        
        if result.get('status') == 0:
            # 转换数据格式以匹配原有结构
            lottery_data = []
            for item in result.get('result', {}).get('list', []):
                lottery_data.append({
                    'lottery_id': 'fc3d',
                    'lottery_name': '福彩3D',
                    'lottery_no': item.get('issueno', ''),
                    'lottery_date': item.get('opendate', ''),
                    'lottery_res': item.get('number', ''),
                    'lottery_sale_amount': item.get('saleamount', ''),
                    'lottery_pool_amount': item.get('totalmoney', '')
                })
            return lottery_data
        else:
            print(f"API错误: {result.get('msg', '未知错误')}")
            return []
    except Exception as e:
        print(f"获取数据失败: {str(e)}")
        return []

def load_local_data():
    """加载本地数据"""
    try:
        # 优先尝试加载1000期数据文件
        if os.path.exists('fc3d_data_1000.json'):
            with open('fc3d_data_1000.json', 'r', encoding='utf-8') as f:
                file_data = json.load(f)
                data = file_data['data']  # 获取实际的开奖数据
                print(f"成功加载1000期数据: {len(data)} 期")
                return data
        elif os.path.exists(FC3D_DATA_FILE):
            with open(FC3D_DATA_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"成功加载本地数据: {len(data)} 期")
                return data
    except Exception as e:
        print(f"加载本地数据失败: {str(e)}")
    return []

def save_local_data(data):
    """保存数据到本地"""
    try:
        with open(FC3D_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到 {FC3D_DATA_FILE}")
        return True
    except Exception as e:
        print(f"保存数据失败: {str(e)}")
        return False

def generate_demo_data(count=100):
    """生成演示数据"""
    demo_data = []
    base_date = datetime.now() - timedelta(days=count)
    
    for i in range(count):
        date = base_date + timedelta(days=i)
        number = f"{random.randint(0,9)}{random.randint(0,9)}{random.randint(0,9)}"
        
        demo_data.append({
            'number': number,
            'opendate': date.strftime('%Y-%m-%d'),
            'issueno': f"2024{i+1:03d}"
        })
    
    return demo_data

def main():
    """主程序"""
    print("=" * 60)
    print("           福彩3D高级智能预测系统")
    print("=" * 60)
    
    # 数据加载
    print("\n📊 正在加载数据...")
    data = load_local_data()
    
    if len(data) < 50:
        print("本地数据不足，尝试从API获取...")
        api_data = get_fc3d_data_from_api(300)
        
        if api_data:
            data = api_data
            save_local_data(data)
            print(f"✅ 成功获取API数据: {len(data)} 期")
        else:
            print("⚠️  API获取失败，使用演示数据")
            data = generate_demo_data(150)
            save_local_data(data)
    
    print(f"📈 数据加载完成，共 {len(data)} 期历史数据")
    
    # 显示最新开奖信息
    if data:
        latest = data[0]  # 第一个元素是最新的数据
        number_spaced = latest.get('number', '')
        number_continuous = number_spaced.replace(' ', '')
        print(f"\n📋 最新开奖信息:")
        print(f"   期号: {latest.get('issueno', 'N/A')}")
        print(f"   开奖日期: {latest.get('opendate', 'N/A')}")
        print(f"   开奖号码: {number_spaced} (标准格式)")
        print(f"   开奖号码: {number_continuous} (连续格式)")
    
    # 创建预测器
    predictor = AdvancedFC3DPredictor(data)
    
    # 验证准确性
    print("\n🔍 正在验证预测准确性...")
    validation_result = predictor.validate_prediction_accuracy(20)
    
    if validation_result:
        print(f"📊 历史验证结果:")
        print(f"   直选准确率: {validation_result['accuracy_rate']:.2%}")
        print(f"   组选准确率: {validation_result['group_accuracy']:.2%}")
        print(f"   位置准确率: 百位{validation_result['position_accuracy'][0]:.2%} "
              f"十位{validation_result['position_accuracy'][1]:.2%} "
              f"个位{validation_result['position_accuracy'][2]:.2%}")
    
    # 生成预测
    print("\n🎯 生成预测组合...")
    combinations = predictor.generate_smart_combinations(8)
    
    print("\n" + "=" * 60)
    print("                预测结果")
    print("=" * 60)
    
    for i, combo in enumerate(combinations, 1):
        analysis = predictor.analyze_combination(combo)
        print(f"\n预测 {i}: {analysis['number']}")
        print(f"   类型: {analysis['type']} - {analysis['description']}")
        print(f"   和值: {analysis['sum']}  跨度: {analysis['span']}")
        print(f"   奇偶: {analysis['odd_count']}奇{analysis['even_count']}偶  "
              f"大小: {analysis['big_count']}大{analysis['small_count']}小")
    
    # 推荐预测
    best_combo = combinations[0] if combinations else [0, 0, 0]
    best_analysis = predictor.analyze_combination(best_combo)
    
    print("\n" + "=" * 60)
    print("                推荐预测")
    print("=" * 60)
    print(f"\n🎯 推荐号码: {best_analysis['number']}")
    print(f"📋 投注类型: {best_analysis['type']}")
    print(f"📊 号码特征: 和值{best_analysis['sum']} 跨度{best_analysis['span']}")
    
    # 投注建议
    print("\n💡 投注建议:")
    if best_analysis['type'] == '组选6':
        from itertools import permutations
        perms = list(permutations(best_combo))
        perm_strs = [''.join(map(str, p)) for p in perms]
        print(f"   直选: {best_analysis['number']}")
        print(f"   组选6: {', '.join(perm_strs)}")
    elif best_analysis['type'] == '组选3':
        print(f"   直选: {best_analysis['number']}")
        print(f"   组选3: 包含数字 {set(best_combo)} 的所有组合")
    else:
        print(f"   直选: {best_analysis['number']}")
        print(f"   豹子号: 只有一种形态")
    
    print("\n" + "=" * 60)
    print("🍀 预测完成，祝您好运！")
    print("💡 理性购彩，量力而行")
    print("=" * 60)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f'\n❌ 程序运行出错: {str(e)}')
        print("请检查网络连接和数据文件")