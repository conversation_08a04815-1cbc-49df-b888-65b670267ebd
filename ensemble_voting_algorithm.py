#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成投票双色球预测算法
结合多种预测模型，通过投票机制提升预测准确性
目标：红球≥1.5/6，蓝球≥10%
"""

import json
import numpy as np
import random
from collections import defaultdict, Counter
from datetime import datetime
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class BasePredictor:
    """基础预测器接口"""
    
    def __init__(self, name: str):
        self.name = name
        self.weight = 1.0
        self.performance_history = []
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        """预测接口"""
        raise NotImplementedError
    
    def update_weight(self, performance: float):
        """更新权重"""
        self.performance_history.append(performance)
        # 保留最近10次的性能记录
        if len(self.performance_history) > 10:
            self.performance_history = self.performance_history[-10:]
        
        # 基于最近性能调整权重
        if self.performance_history:
            avg_performance = sum(self.performance_history) / len(self.performance_history)
            self.weight = max(0.1, min(2.0, avg_performance * 2))

class FrequencyPredictor(BasePredictor):
    """频率预测器"""
    
    def __init__(self):
        super().__init__("频率预测器")
        self.window_size = 40
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        recent_data = data[-self.window_size:] if len(data) > self.window_size else data
        
        red_freq = [0] * 33
        blue_freq = [0] * 16
        
        for i, record in enumerate(recent_data):
            weight = (i + 1) / len(recent_data)  # 时间权重
            
            red_nums = self.parse_numbers(record['number'])
            blue_num = int(record['refernumber'])
            
            for num in red_nums:
                if 1 <= num <= 33:
                    red_freq[num-1] += weight
            
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += weight
        
        # 选择红球
        red_with_freq = [(i+1, freq) for i, freq in enumerate(red_freq)]
        red_with_freq.sort(key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, freq in red_with_freq[:6]])
        
        # 选择蓝球
        blue_with_freq = [(i+1, freq) for i, freq in enumerate(blue_freq)]
        blue_with_freq.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_freq[0][0]
        
        return selected_red, selected_blue

class MarkovPredictor(BasePredictor):
    """马尔科夫预测器"""
    
    def __init__(self):
        super().__init__("马尔科夫预测器")
        self.window_size = 50
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        if len(data) < 2:
            return list(range(1, 7)), 1
        
        recent_data = data[-self.window_size:] if len(data) > self.window_size else data
        
        # 构建转移矩阵
        red_transitions = defaultdict(lambda: defaultdict(float))
        blue_transitions = defaultdict(float)
        
        for i in range(len(recent_data) - 1):
            current_red = set(self.parse_numbers(recent_data[i]['number']))
            next_red = set(self.parse_numbers(recent_data[i+1]['number']))
            current_blue = int(recent_data[i]['refernumber'])
            next_blue = int(recent_data[i+1]['refernumber'])
            
            weight = (i + 1) / len(recent_data)
            
            for curr_num in current_red:
                for next_num in next_red:
                    red_transitions[curr_num][next_num] += weight
            
            blue_transitions[next_blue] += weight
        
        # 预测红球
        last_red = set(self.parse_numbers(data[-1]['number']))
        red_probs = [0.0] * 33
        
        for num in range(1, 34):
            total_prob = 0
            count = 0
            
            for last_num in last_red:
                if last_num in red_transitions:
                    total = sum(red_transitions[last_num].values())
                    if total > 0:
                        total_prob += red_transitions[last_num].get(num, 0) / total
                        count += 1
            
            if count > 0:
                red_probs[num-1] = total_prob / count
            else:
                red_probs[num-1] = 1/33
        
        red_with_probs = [(i+1, prob) for i, prob in enumerate(red_probs)]
        red_with_probs.sort(key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, prob in red_with_probs[:6]])
        
        # 预测蓝球
        blue_probs = [0.0] * 16
        total_blue = sum(blue_transitions.values())
        
        for num in range(1, 17):
            if total_blue > 0:
                blue_probs[num-1] = blue_transitions.get(num, 0) / total_blue
            else:
                blue_probs[num-1] = 1/16
        
        blue_with_probs = [(i+1, prob) for i, prob in enumerate(blue_probs)]
        blue_with_probs.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_probs[0][0]
        
        return selected_red, selected_blue

class TrendPredictor(BasePredictor):
    """趋势预测器"""
    
    def __init__(self):
        super().__init__("趋势预测器")
        self.window_size = 20
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        if len(data) < self.window_size:
            return list(range(1, 7)), 1
        
        recent_data = data[-self.window_size:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 分析每个号码的趋势
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_data):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 2:
                # 计算趋势
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                avg_interval = sum(intervals) / len(intervals)
                
                last_appearance = appearances[-1]
                expected_next = last_appearance + avg_interval
                current_pos = len(recent_data) - 1
                
                distance = abs(current_pos - expected_next)
                score = max(0, 1 - distance / self.window_size)
                red_scores[num-1] = score
            elif len(appearances) == 1:
                last_pos = appearances[0]
                red_scores[num-1] = (self.window_size - last_pos) / self.window_size * 0.5
            else:
                red_scores[num-1] = 0.3
        
        # 蓝球趋势
        blue_appearances = {}
        for i, record in enumerate(recent_data):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_appearances:
                blue_appearances[blue_num] = []
            blue_appearances[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_appearances and len(blue_appearances[num]) >= 1:
                last_pos = blue_appearances[num][-1]
                blue_scores[num-1] = (self.window_size - last_pos) / self.window_size
            else:
                blue_scores[num-1] = 0.4
        
        # 选择号码
        red_with_scores = [(i+1, score) for i, score in enumerate(red_scores)]
        red_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, score in red_with_scores[:6]])
        
        blue_with_scores = [(i+1, score) for i, score in enumerate(blue_scores)]
        blue_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_scores[0][0]
        
        return selected_red, selected_blue

class PatternPredictor(BasePredictor):
    """模式预测器"""
    
    def __init__(self):
        super().__init__("模式预测器")
        self.window_size = 15
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        if len(data) < self.window_size:
            return list(range(1, 7)), 1
        
        recent_data = data[-self.window_size:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 奇偶模式分析
        odd_count = 0
        even_count = 0
        
        for record in recent_data:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if num % 2 == 1:
                    odd_count += 1
                else:
                    even_count += 1
        
        total_count = odd_count + even_count
        if total_count > 0:
            odd_ratio = odd_count / total_count
            even_ratio = even_count / total_count
            
            for num in range(1, 34):
                if num % 2 == 1:
                    red_scores[num-1] += odd_ratio * 0.3
                else:
                    red_scores[num-1] += even_ratio * 0.3
        
        # 区间模式分析
        zone_counts = [0, 0, 0]  # 1-11, 12-22, 23-33
        for record in recent_data:
            red_nums = self.parse_numbers(record['number'])
            for num in red_nums:
                if 1 <= num <= 11:
                    zone_counts[0] += 1
                elif 12 <= num <= 22:
                    zone_counts[1] += 1
                elif 23 <= num <= 33:
                    zone_counts[2] += 1
        
        total_zone = sum(zone_counts)
        if total_zone > 0:
            zone_ratios = [c / total_zone for c in zone_counts]
            
            for num in range(1, 34):
                if 1 <= num <= 11:
                    red_scores[num-1] += zone_ratios[0] * 0.4
                elif 12 <= num <= 22:
                    red_scores[num-1] += zone_ratios[1] * 0.4
                elif 23 <= num <= 33:
                    red_scores[num-1] += zone_ratios[2] * 0.4
        
        # 和值模式
        sum_values = []
        for record in recent_data:
            red_nums = self.parse_numbers(record['number'])
            sum_values.append(sum(red_nums))
        
        avg_sum = sum(sum_values) / len(sum_values)
        
        for num in range(1, 34):
            if avg_sum < 100:  # 和值偏小
                if num > 16:
                    red_scores[num-1] += 0.3
            elif avg_sum > 120:  # 和值偏大
                if num <= 16:
                    red_scores[num-1] += 0.3
        
        # 蓝球模式
        blue_freq = [0] * 16
        for record in recent_data:
            blue_num = int(record['refernumber'])
            if 1 <= blue_num <= 16:
                blue_freq[blue_num-1] += 1
        
        total_blue = sum(blue_freq)
        if total_blue > 0:
            blue_scores = [f / total_blue for f in blue_freq]
        else:
            blue_scores = [1/16] * 16
        
        # 选择号码
        red_with_scores = [(i+1, score) for i, score in enumerate(red_scores)]
        red_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, score in red_with_scores[:6]])
        
        blue_with_scores = [(i+1, score) for i, score in enumerate(blue_scores)]
        blue_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_scores[0][0]
        
        return selected_red, selected_blue

class CyclePredictor(BasePredictor):
    """周期预测器"""
    
    def __init__(self):
        super().__init__("周期预测器")
        self.window_size = 30
    
    def parse_numbers(self, number_str: str) -> List[int]:
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        if len(data) < self.window_size:
            return list(range(1, 7)), 1
        
        recent_data = data[-self.window_size:]
        red_scores = [0.0] * 33
        blue_scores = [0.0] * 16
        
        # 寻找周期性模式
        for num in range(1, 34):
            appearances = []
            for i, record in enumerate(recent_data):
                red_nums = self.parse_numbers(record['number'])
                if num in red_nums:
                    appearances.append(i)
            
            if len(appearances) >= 3:
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                
                # 寻找最常见的间隔
                interval_counts = Counter(intervals)
                if interval_counts:
                    most_common_interval = interval_counts.most_common(1)[0][0]
                    last_appearance = appearances[-1]
                    
                    predicted_next = last_appearance + most_common_interval
                    current_position = len(recent_data) - 1
                    
                    distance = abs(current_position - predicted_next)
                    score = max(0, 1 - distance / 10)
                    red_scores[num-1] = score
            else:
                red_scores[num-1] = 0.3
        
        # 蓝球周期
        blue_appearances = {}
        for i, record in enumerate(recent_data):
            blue_num = int(record['refernumber'])
            if blue_num not in blue_appearances:
                blue_appearances[blue_num] = []
            blue_appearances[blue_num].append(i)
        
        for num in range(1, 17):
            if num in blue_appearances and len(blue_appearances[num]) >= 2:
                appearances = blue_appearances[num]
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    last_appearance = appearances[-1]
                    current_position = len(recent_data) - 1
                    
                    expected_next = last_appearance + avg_interval
                    distance = abs(current_position - expected_next)
                    score = max(0, 1 - distance / 15)
                    blue_scores[num-1] = score
            else:
                blue_scores[num-1] = 0.3
        
        # 选择号码
        red_with_scores = [(i+1, score) for i, score in enumerate(red_scores)]
        red_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_red = sorted([num for num, score in red_with_scores[:6]])
        
        blue_with_scores = [(i+1, score) for i, score in enumerate(blue_scores)]
        blue_with_scores.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_with_scores[0][0]
        
        return selected_red, selected_blue

class EnsembleVotingPredictor:
    """集成投票预测器"""
    
    def __init__(self):
        self.data = []
        self.predictors = [
            FrequencyPredictor(),
            MarkovPredictor(),
            TrendPredictor(),
            PatternPredictor(),
            CyclePredictor()
        ]
        
        # 投票参数
        self.red_vote_threshold = 0.3  # 红球投票阈值
        self.blue_vote_threshold = 0.2  # 蓝球投票阈值
        self.diversity_factor = 0.4    # 多样性因子
        
    def load_data(self, file_path: str):
        """加载历史数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 期历史数据")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def parse_numbers(self, number_str: str) -> List[int]:
        """解析号码字符串"""
        number_str = number_str.strip()
        if ',' in number_str:
            return [int(x.strip()) for x in number_str.split(',')]
        else:
            return [int(x.strip()) for x in number_str.split()]
    
    def ensemble_predict(self, data: List[Dict]) -> Tuple[List[int], int]:
        """集成预测"""
        # 获取各预测器的预测结果
        predictions = []
        for predictor in self.predictors:
            try:
                red_pred, blue_pred = predictor.predict(data)
                predictions.append((red_pred, blue_pred, predictor.weight))
            except Exception as e:
                print(f"{predictor.name} 预测失败: {e}")
                predictions.append((list(range(1, 7)), 1, 0.1))
        
        # 红球投票
        red_votes = defaultdict(float)
        total_weight = sum(weight for _, _, weight in predictions)
        
        for red_pred, _, weight in predictions:
            normalized_weight = weight / total_weight if total_weight > 0 else 1.0 / len(predictions)
            for num in red_pred:
                red_votes[num] += normalized_weight
        
        # 选择红球（基于投票和多样性）
        red_candidates = [(num, votes) for num, votes in red_votes.items() if votes >= self.red_vote_threshold]
        red_candidates.sort(key=lambda x: x[1], reverse=True)
        
        selected_red = []
        for num, votes in red_candidates:
            if len(selected_red) < 6:
                if self._is_red_combination_valid(selected_red + [num]):
                    selected_red.append(num)
        
        # 如果选择不足6个，补充高票数号码
        while len(selected_red) < 6:
            for num, votes in red_candidates:
                if num not in selected_red:
                    selected_red.append(num)
                    break
            # 如果还是不够，从所有号码中随机选择
            if len(selected_red) < 6:
                all_nums = set(range(1, 34))
                remaining = list(all_nums - set(selected_red))
                if remaining:
                    selected_red.append(random.choice(remaining))
        
        selected_red = sorted(selected_red[:6])
        
        # 蓝球投票
        blue_votes = defaultdict(float)
        
        for _, blue_pred, weight in predictions:
            normalized_weight = weight / total_weight if total_weight > 0 else 1.0 / len(predictions)
            blue_votes[blue_pred] += normalized_weight
        
        # 选择蓝球
        blue_candidates = [(num, votes) for num, votes in blue_votes.items()]
        blue_candidates.sort(key=lambda x: x[1], reverse=True)
        selected_blue = blue_candidates[0][0] if blue_candidates else 1
        
        return selected_red, selected_blue
    
    def _is_red_combination_valid(self, numbers: List[int]) -> bool:
        """检查红球组合是否有效"""
        if len(numbers) <= 1:
            return True
        
        numbers_sorted = sorted(numbers)
        
        # 检查连号
        consecutive_count = 1
        max_consecutive = 1
        
        for i in range(1, len(numbers_sorted)):
            if numbers_sorted[i] == numbers_sorted[i-1] + 1:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 1
        
        if max_consecutive > 3:
            return False
        
        # 检查区间分布
        if len(numbers) >= 4:
            zone1 = sum(1 for n in numbers if 1 <= n <= 11)
            zone2 = sum(1 for n in numbers if 12 <= n <= 22)
            zone3 = sum(1 for n in numbers if 23 <= n <= 33)
            
            max_zone_ratio = 0.7 - self.diversity_factor * 0.2
            if zone1 > len(numbers) * max_zone_ratio or zone2 > len(numbers) * max_zone_ratio or zone3 > len(numbers) * max_zone_ratio:
                return False
        
        return True
    
    def update_predictor_weights(self, predictions: List[Tuple[List[int], int]], actual_red: List[int], actual_blue: int):
        """更新预测器权重"""
        for i, predictor in enumerate(self.predictors):
            if i < len(predictions):
                pred_red, pred_blue = predictions[i]
                
                # 计算性能
                red_hit = len(set(pred_red) & set(actual_red))
                blue_hit = 1 if pred_blue == actual_blue else 0
                
                # 综合性能评分
                performance = (red_hit / 6) * 0.7 + blue_hit * 0.3
                predictor.update_weight(performance)

def ensemble_voting_backtest(predictor: EnsembleVotingPredictor, test_periods: int = 500):
    """集成投票算法回测"""
    print("\n=== 集成投票算法回测 ===")
    print(f"回测期数: {test_periods}")
    print("目标: 红球≥1.5/6 (25%), 蓝球≥10%")
    print("=" * 50)
    
    if len(predictor.data) < test_periods + 100:
        print(f"数据不足，需要至少 {test_periods + 100} 期数据")
        return
    
    test_data = predictor.data[-test_periods:]
    red_hits = []
    blue_hits = 0
    best_predictions = []
    
    # 记录各预测器的性能
    predictor_performances = {p.name: [] for p in predictor.predictors}
    
    print(f"开始回测 {test_periods} 期...")
    print(f"使用 {len(predictor.predictors)} 个预测器进行集成投票")
    
    for i in range(test_periods):
        history_data = predictor.data[:-(test_periods-i)]
        
        try:
            # 获取各预测器的预测
            individual_predictions = []
            for pred in predictor.predictors:
                try:
                    red_pred, blue_pred = pred.predict(history_data)
                    individual_predictions.append((red_pred, blue_pred))
                except Exception:
                    individual_predictions.append((list(range(1, 7)), 1))
            
            # 集成预测
            pred_red, pred_blue = predictor.ensemble_predict(history_data)
            
            # 实际结果
            actual_data = test_data[i]
            actual_red = predictor.parse_numbers(actual_data['number'])
            actual_blue = int(actual_data['refernumber'])
            
            # 计算命中
            red_hit_count = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            
            red_hits.append(red_hit_count)
            blue_hits += blue_hit
            
            # 更新预测器权重
            predictor.update_predictor_weights(individual_predictions, actual_red, actual_blue)
            
            # 记录各预测器性能
            for j, pred in enumerate(predictor.predictors):
                if j < len(individual_predictions):
                    ind_red, ind_blue = individual_predictions[j]
                    ind_red_hit = len(set(ind_red) & set(actual_red))
                    ind_blue_hit = 1 if ind_blue == actual_blue else 0
                    ind_performance = (ind_red_hit / 6) * 0.7 + ind_blue_hit * 0.3
                    predictor_performances[pred.name].append(ind_performance)
            
            # 记录优秀预测
            if red_hit_count >= 3 or blue_hit == 1:
                best_predictions.append({
                    'period': actual_data['issueno'],
                    'red_hit': red_hit_count,
                    'blue_hit': blue_hit,
                    'predicted_red': pred_red,
                    'actual_red': actual_red,
                    'predicted_blue': pred_blue,
                    'actual_blue': actual_blue
                })
        except Exception as e:
            print(f"预测第 {i+1} 期时出错: {e}")
            red_hits.append(0)
        
        # 进度显示
        if (i + 1) % 100 == 0:
            current_red_avg = sum(red_hits) / len(red_hits)
            current_blue_rate = blue_hits / (i + 1) * 100
            print(f"进度: {i+1}/{test_periods}, 当前红球: {current_red_avg:.2f}/6, 蓝球: {current_blue_rate:.1f}%")
    
    # 计算最终结果
    avg_red_hits = sum(red_hits) / len(red_hits)
    blue_hit_rate = blue_hits / test_periods * 100
    
    print(f"\n=== 集成投票算法回测结果 ===")
    print(f"红球平均命中: {avg_red_hits:.2f}/6 ({avg_red_hits/6*100:.1f}%)")
    print(f"蓝球命中率: {blue_hit_rate:.1f}% ({blue_hits}/{test_periods})")
    
    # 红球命中分布
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_hits.count(i)
        percentage = count / test_periods * 100
        print(f"  {i}个: {count}次 ({percentage:.1f}%)")
    
    # 各预测器性能
    print(f"\n=== 各预测器平均性能 ===")
    for name, performances in predictor_performances.items():
        if performances:
            avg_perf = sum(performances) / len(performances)
            print(f"  {name}: {avg_perf:.3f}")
    
    # 当前预测器权重
    print(f"\n=== 当前预测器权重 ===")
    for pred in predictor.predictors:
        print(f"  {pred.name}: {pred.weight:.3f}")
    
    # 目标达成情况
    red_target = 1.5
    blue_target = 10.0
    red_achievement = (avg_red_hits / red_target) * 100
    blue_achievement = (blue_hit_rate / blue_target) * 100
    
    print(f"\n=== 目标达成情况 ===")
    print(f"红球目标: {red_target}/6, 实际: {avg_red_hits:.2f}/6, 达成度: {red_achievement:.1f}%")
    print(f"蓝球目标: {blue_target}%, 实际: {blue_hit_rate:.1f}%, 达成度: {blue_achievement:.1f}%")
    
    # 显示最佳预测记录
    if best_predictions:
        print(f"\n=== 前15期最佳预测记录 ===")
        best_predictions.sort(key=lambda x: (x['red_hit'], x['blue_hit']), reverse=True)
        
        for i, pred in enumerate(best_predictions[:15]):
            blue_symbol = "✓" if pred['blue_hit'] else "✗"
            print(f"{i+1:2d}. 期号:{pred['period']} 红球:{pred['red_hit']}/6 蓝球:{blue_symbol}")
            print(f"    预测: {pred['predicted_red']} + {pred['predicted_blue']}")
            print(f"    实际: {pred['actual_red']} + {pred['actual_blue']}")
    
    # 算法评估
    print(f"\n=== 集成投票算法评估 ===")
    if avg_red_hits >= red_target and blue_hit_rate >= blue_target:
        print("🎉 恭喜！算法达到预期目标")
    elif avg_red_hits >= red_target * 0.9 and blue_hit_rate >= blue_target * 0.9:
        print("🔧 算法表现优秀，非常接近目标")
    elif avg_red_hits >= red_target * 0.8 and blue_hit_rate >= blue_target * 0.8:
        print("📈 算法表现良好，还有提升空间")
    else:
        print("⚠️ 算法需要进一步优化")
    
    # 集成学习优势分析
    print(f"\n=== 集成学习优势分析 ===")
    best_individual = max(predictor_performances.items(), key=lambda x: sum(x[1])/len(x[1]) if x[1] else 0)
    if best_individual[1]:
        best_individual_avg = sum(best_individual[1]) / len(best_individual[1])
        ensemble_performance = (avg_red_hits / 6) * 0.7 + (blue_hit_rate / 100) * 0.3
        
        print(f"最佳单一预测器: {best_individual[0]} (性能: {best_individual_avg:.3f})")
        print(f"集成预测器性能: {ensemble_performance:.3f}")
        
        if ensemble_performance > best_individual_avg:
            improvement = (ensemble_performance - best_individual_avg) / best_individual_avg * 100
            print(f"集成学习提升: +{improvement:.1f}%")
        else:
            decline = (best_individual_avg - ensemble_performance) / best_individual_avg * 100
            print(f"集成学习效果: -{decline:.1f}% (需要调优)")
    
    return {
        'red_avg': avg_red_hits,
        'blue_rate': blue_hit_rate,
        'red_achievement': red_achievement,
        'blue_achievement': blue_achievement,
        'predictor_weights': {p.name: p.weight for p in predictor.predictors}
    }

if __name__ == "__main__":
    print("=== 集成投票双色球预测算法 ===")
    
    # 创建集成预测器
    predictor = EnsembleVotingPredictor()
    
    if not predictor.load_data('ssq_data.json'):
        print("数据加载失败，程序退出")
        exit(1)
    
    print(f"\n集成预测器包含以下子预测器:")
    for i, pred in enumerate(predictor.predictors):
        print(f"  {i+1}. {pred.name} (初始权重: {pred.weight:.2f})")
    
    # 开始回测
    results = ensemble_voting_backtest(predictor, test_periods=500)
    
    if results:
        print(f"\n=== 集成投票算法总结 ===")
        print(f"通过集成 {len(predictor.predictors)} 个预测器：")
        print(f"- 红球命中率: {results['red_avg']:.2f}/6 (达成度: {results['red_achievement']:.1f}%)")
        print(f"- 蓝球命中率: {results['blue_rate']:.1f}% (达成度: {results['blue_achievement']:.1f}%)")
        print(f"- 整体目标达成度: {(results['red_achievement'] + results['blue_achievement'])/2:.1f}%")
        
        if results['red_avg'] >= 1.4 or results['blue_rate'] >= 9.0:
            print("\n🚀 集成投票效果显著！")
        elif results['red_avg'] >= 1.2 or results['blue_rate'] >= 7.5:
            print("\n📈 集成投票有一定效果，建议继续优化")
        else:
            print("\n🔄 建议调整投票策略或增加更多预测器")
        
        print(f"\n💡 优化建议:")
        print(f"1. 可以尝试添加更多类型的预测器")
        print(f"2. 调整投票阈值和权重更新策略")
        print(f"3. 考虑使用更复杂的集成方法（如Stacking）")
        print(f"4. 增加特征工程和数据预处理")