#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强蓝球预测算法
专门针对蓝球命中率优化的算法
目标：蓝球命中率≥12%

核心创新：
1. 多模态蓝球特征提取
2. 深度学习蓝球专用网络
3. 时序模式挖掘
4. 集成学习蓝球预测
5. 自适应权重调整
6. 反向选择策略
"""

import json
import random
import math
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EnhancedBlueBallPredictor:
    def __init__(self, data_file='ssq_data.json'):
        """初始化增强蓝球预测器"""
        self.data_file = data_file
        self.data = self.load_data()
        self.blue_range = list(range(1, 17))  # 蓝球范围1-16
        
        # 蓝球专用特征权重
        self.feature_weights = {
            'frequency': 0.20,      # 频率分析
            'interval': 0.18,       # 间隔分析
            'trend': 0.15,          # 趋势分析
            'pattern': 0.15,        # 模式匹配
            'neural': 0.12,         # 神经网络
            'reverse': 0.10,        # 反向选择
            'ensemble': 0.10        # 集成预测
        }
        
        # 初始化神经网络权重
        self.init_neural_networks()
        
        # 历史表现跟踪
        self.prediction_history = []
        self.performance_stats = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'hit_rate': 0.0
        }
        
    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 数据预处理
            processed_data = []
            for entry in data:
                try:
                    # 解析红球号码
                    if 'number' in entry:
                        red_str = entry['number'].strip()
                        if ',' in red_str:
                            red_balls = [int(x.strip()) for x in red_str.split(',')]
                        else:
                            red_balls = [int(x.strip()) for x in red_str.split()]
                    else:
                        continue
                    
                    # 解析蓝球号码
                    if 'refernumber' in entry:
                        blue_ball = int(entry['refernumber'])
                    else:
                        continue
                    
                    processed_entry = {
                        'red': red_balls,
                        'blue': blue_ball,
                        'date': entry.get('date', ''),
                        'period': entry.get('issueno', '')
                    }
                    processed_data.append(processed_entry)
                    
                except (ValueError, KeyError) as e:
                    continue
            
            return processed_data
            
        except FileNotFoundError:
            print(f"数据文件 {self.data_file} 未找到")
            return []
        except Exception as e:
            print(f"数据加载失败: {e}")
            return []
    
    def init_neural_networks(self):
        """初始化神经网络权重"""
        # 蓝球专用深度神经网络
        input_size = 12
        hidden1_size = 24
        hidden2_size = 16
        output_size = 1
        
        # Xavier初始化
        self.blue_weights_1 = np.random.randn(input_size, hidden1_size) * np.sqrt(2.0 / input_size)
        self.blue_bias_1 = np.zeros(hidden1_size)
        
        self.blue_weights_2 = np.random.randn(hidden1_size, hidden2_size) * np.sqrt(2.0 / hidden1_size)
        self.blue_bias_2 = np.zeros(hidden2_size)
        
        self.blue_weights_3 = np.random.randn(hidden2_size, output_size) * np.sqrt(2.0 / hidden2_size)
        self.blue_bias_3 = np.zeros(output_size)
        
        # LSTM风格的记忆单元
        self.lstm_hidden = np.zeros(8)
        self.lstm_cell = np.zeros(8)
        
    def relu(self, x):
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def leaky_relu(self, x, alpha=0.01):
        """Leaky ReLU激活函数"""
        return np.where(x > 0, x, alpha * x)
    
    def sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def tanh(self, x):
        """Tanh激活函数"""
        return np.tanh(x)
    
    def analyze_blue_features(self):
        """分析蓝球特征"""
        if not self.data:
            return self._default_blue_analysis()
        
        analysis = {
            'frequency': defaultdict(int),
            'intervals': defaultdict(list),
            'trends': defaultdict(list),
            'patterns': [],
            'time_series': [],
            'distribution': {'low': 0, 'mid': 0, 'high': 0},
            'consecutive': defaultdict(int),
            'gaps': defaultdict(list)
        }
        
        # 提取蓝球时间序列
        blue_sequence = [entry['blue'] for entry in self.data]
        analysis['time_series'] = blue_sequence
        
        # 频率统计
        for blue in blue_sequence:
            analysis['frequency'][blue] += 1
        
        # 间隔分析
        for ball in self.blue_range:
            last_pos = -1
            for i, blue in enumerate(blue_sequence):
                if blue == ball:
                    if last_pos != -1:
                        interval = i - last_pos
                        analysis['intervals'][ball].append(interval)
                    last_pos = i
        
        # 趋势分析（最近30期）
        recent_blues = blue_sequence[-30:] if len(blue_sequence) >= 30 else blue_sequence
        for ball in self.blue_range:
            count = recent_blues.count(ball)
            analysis['trends'][ball] = count
        
        # 分布分析
        for blue in blue_sequence:
            if blue <= 5:
                analysis['distribution']['low'] += 1
            elif blue <= 10:
                analysis['distribution']['mid'] += 1
            else:
                analysis['distribution']['high'] += 1
        
        # 连续出现分析
        for i in range(1, len(blue_sequence)):
            if blue_sequence[i] == blue_sequence[i-1]:
                analysis['consecutive'][blue_sequence[i]] += 1
        
        # 模式挖掘（3期模式）
        for i in range(len(blue_sequence) - 2):
            pattern = tuple(blue_sequence[i:i+3])
            analysis['patterns'].append(pattern)
        
        # 间隙分析
        for ball in self.blue_range:
            gaps = []
            last_appear = -1
            for i, blue in enumerate(blue_sequence):
                if blue == ball:
                    if last_appear != -1:
                        gaps.append(i - last_appear)
                    last_appear = i
            analysis['gaps'][ball] = gaps
        
        return analysis
    
    def _default_blue_analysis(self):
        """默认蓝球分析"""
        return {
            'frequency': defaultdict(int),
            'intervals': defaultdict(list),
            'trends': defaultdict(int),
            'patterns': [],
            'time_series': [],
            'distribution': {'low': 33, 'mid': 33, 'high': 34},
            'consecutive': defaultdict(int),
            'gaps': defaultdict(list)
        }
    
    def frequency_score(self, ball, analysis):
        """频率得分"""
        total_count = sum(analysis['frequency'].values())
        if total_count == 0:
            return 1.0 / 16
        
        freq = analysis['frequency'][ball]
        expected_freq = total_count / 16
        
        # 使用正态分布计算得分
        score = math.exp(-0.5 * ((freq - expected_freq) / (expected_freq + 1)) ** 2)
        return score
    
    def interval_score(self, ball, analysis):
        """间隔得分"""
        intervals = analysis['intervals'][ball]
        if not intervals:
            return 0.5
        
        avg_interval = sum(intervals) / len(intervals)
        expected_interval = 16  # 理论平均间隔
        
        # 计算当前间隔
        time_series = analysis['time_series']
        current_interval = 0
        for i in range(len(time_series) - 1, -1, -1):
            if time_series[i] == ball:
                break
            current_interval += 1
        
        # 基于当前间隔和历史平均间隔计算得分
        if current_interval >= avg_interval * 0.8:
            score = min(1.0, current_interval / avg_interval)
        else:
            score = 0.3
        
        return score
    
    def trend_score(self, ball, analysis):
        """趋势得分"""
        recent_count = analysis['trends'][ball]
        expected_count = 30 / 16  # 最近30期的期望出现次数
        
        if recent_count == 0:
            return 0.8  # 冷号给予较高分数
        elif recent_count >= expected_count * 2:
            return 0.2  # 热号降低分数
        else:
            return 0.6
    
    def pattern_score(self, ball, analysis):
        """模式匹配得分"""
        if len(analysis['time_series']) < 3:
            return 0.5
        
        # 获取最近2期的蓝球
        recent_pattern = tuple(analysis['time_series'][-2:])
        
        # 在历史中寻找相似模式
        pattern_scores = defaultdict(int)
        patterns = analysis['patterns']
        
        for i, pattern in enumerate(patterns):
            if pattern[:2] == recent_pattern:
                next_ball = pattern[2]
                pattern_scores[next_ball] += 1
        
        if pattern_scores:
            total_matches = sum(pattern_scores.values())
            return pattern_scores[ball] / total_matches
        else:
            return 1.0 / 16
    
    def neural_score(self, ball, analysis):
        """神经网络得分"""
        # 构建特征向量
        features = np.array([
            self.frequency_score(ball, analysis),
            self.interval_score(ball, analysis),
            self.trend_score(ball, analysis),
            analysis['distribution']['low'] / 100,
            analysis['distribution']['mid'] / 100,
            analysis['distribution']['high'] / 100,
            analysis['consecutive'][ball] / 10,
            len(analysis['gaps'][ball]) / 10,
            ball / 16,
            math.sin(ball * math.pi / 16),
            math.cos(ball * math.pi / 16),
            random.random() * 0.1
        ])
        
        # 深度神经网络前向传播
        hidden1 = self.leaky_relu(np.dot(features, self.blue_weights_1) + self.blue_bias_1)
        hidden2 = self.leaky_relu(np.dot(hidden1, self.blue_weights_2) + self.blue_bias_2)
        output = self.sigmoid(np.dot(hidden2, self.blue_weights_3) + self.blue_bias_3)
        
        return float(output[0])
    
    def reverse_score(self, ball, analysis):
        """反向选择得分（避免过热号码）"""
        time_series = analysis['time_series']
        if len(time_series) < 10:
            return 0.5
        
        # 最近10期出现次数
        recent_count = time_series[-10:].count(ball)
        
        # 反向得分：出现越少得分越高
        if recent_count == 0:
            return 1.0
        elif recent_count == 1:
            return 0.8
        elif recent_count == 2:
            return 0.4
        else:
            return 0.1
    
    def ensemble_score(self, ball, analysis):
        """集成得分"""
        # 多种预测方法的集成
        scores = []
        
        # 方法1：基于频率的概率预测
        freq_score = self.frequency_score(ball, analysis)
        scores.append(freq_score)
        
        # 方法2：基于间隔的预测
        interval_score = self.interval_score(ball, analysis)
        scores.append(interval_score)
        
        # 方法3：基于趋势的预测
        trend_score = self.trend_score(ball, analysis)
        scores.append(trend_score)
        
        # 方法4：随机森林思想
        if len(analysis['time_series']) >= 5:
            recent_avg = sum(analysis['time_series'][-5:]) / 5
            distance_score = 1 - abs(ball - recent_avg) / 16
            scores.append(distance_score)
        
        # 加权平均
        weights = [0.3, 0.25, 0.25, 0.2]
        if len(scores) == len(weights):
            ensemble_score = sum(s * w for s, w in zip(scores, weights))
        else:
            ensemble_score = sum(scores) / len(scores)
        
        return ensemble_score
    
    def calculate_blue_scores(self, analysis):
        """计算所有蓝球的综合得分"""
        blue_scores = {}
        
        for ball in self.blue_range:
            # 计算各项得分
            freq_score = self.frequency_score(ball, analysis)
            interval_score = self.interval_score(ball, analysis)
            trend_score = self.trend_score(ball, analysis)
            pattern_score = self.pattern_score(ball, analysis)
            neural_score = self.neural_score(ball, analysis)
            reverse_score = self.reverse_score(ball, analysis)
            ensemble_score = self.ensemble_score(ball, analysis)
            
            # 加权综合得分
            total_score = (
                freq_score * self.feature_weights['frequency'] +
                interval_score * self.feature_weights['interval'] +
                trend_score * self.feature_weights['trend'] +
                pattern_score * self.feature_weights['pattern'] +
                neural_score * self.feature_weights['neural'] +
                reverse_score * self.feature_weights['reverse'] +
                ensemble_score * self.feature_weights['ensemble']
            )
            
            blue_scores[ball] = total_score
        
        return blue_scores
    
    def multi_model_prediction(self, analysis):
        """多模型预测"""
        predictions = []
        
        # 模型1：直接得分排序
        blue_scores = self.calculate_blue_scores(analysis)
        sorted_blues = sorted(blue_scores.items(), key=lambda x: x[1], reverse=True)
        predictions.append(sorted_blues[0][0])
        
        # 模型2：概率采样
        total_score = sum(blue_scores.values())
        if total_score > 0:
            probs = [blue_scores[ball] / total_score for ball in self.blue_range]
            sampled_blue = np.random.choice(self.blue_range, p=probs)
            predictions.append(sampled_blue)
        
        # 模型3：冷热平衡
        time_series = analysis['time_series']
        if len(time_series) >= 15:
            recent_freq = Counter(time_series[-15:])
            cold_balls = [ball for ball in self.blue_range if recent_freq[ball] <= 1]
            if cold_balls:
                cold_scores = {ball: blue_scores[ball] for ball in cold_balls}
                best_cold = max(cold_scores.items(), key=lambda x: x[1])[0]
                predictions.append(best_cold)
        
        # 模型4：模式延续
        if len(time_series) >= 3:
            recent_pattern = tuple(time_series[-2:])
            pattern_next = self.predict_by_pattern(recent_pattern, analysis)
            if pattern_next:
                predictions.append(pattern_next)
        
        # 模型5：LSTM风格预测
        lstm_pred = self.lstm_predict(analysis)
        predictions.append(lstm_pred)
        
        # 投票决策
        if predictions:
            vote_count = Counter(predictions)
            final_prediction = vote_count.most_common(1)[0][0]
        else:
            final_prediction = sorted_blues[0][0]
        
        return final_prediction
    
    def predict_by_pattern(self, recent_pattern, analysis):
        """基于模式预测"""
        patterns = analysis['patterns']
        next_balls = []
        
        for pattern in patterns:
            if pattern[:2] == recent_pattern:
                next_balls.append(pattern[2])
        
        if next_balls:
            return Counter(next_balls).most_common(1)[0][0]
        else:
            return None
    
    def lstm_predict(self, analysis):
        """LSTM风格预测"""
        time_series = analysis['time_series']
        if len(time_series) < 5:
            return random.randint(1, 16)
        
        # 简化的LSTM计算
        recent_sequence = time_series[-5:]
        
        # 更新LSTM状态
        for value in recent_sequence:
            normalized_value = value / 16.0
            
            # 遗忘门
            forget_gate = self.sigmoid(normalized_value + self.lstm_hidden[0])
            
            # 输入门
            input_gate = self.sigmoid(normalized_value + self.lstm_hidden[1])
            
            # 候选值
            candidate = self.tanh(normalized_value + self.lstm_hidden[2])
            
            # 更新细胞状态
            self.lstm_cell = forget_gate * self.lstm_cell + input_gate * candidate
            
            # 输出门
            output_gate = self.sigmoid(normalized_value + self.lstm_hidden[3])
            
            # 更新隐藏状态
            self.lstm_hidden = output_gate * self.tanh(self.lstm_cell)
        
        # 预测下一个值
        prediction = int(self.lstm_hidden[0] * 16) % 16 + 1
        return prediction
    
    def predict_blue_ball(self):
        """预测蓝球"""
        if not self.data:
            return random.randint(1, 16)
        
        # 分析蓝球特征
        analysis = self.analyze_blue_features()
        
        # 多模型预测
        prediction = self.multi_model_prediction(analysis)
        
        # 记录预测
        self.prediction_history.append({
            'prediction': prediction,
            'timestamp': datetime.now().isoformat(),
            'analysis': analysis
        })
        
        return prediction
    
    def update_performance(self, predicted_blue, actual_blue):
        """更新性能统计"""
        self.performance_stats['total_predictions'] += 1
        
        if predicted_blue == actual_blue:
            self.performance_stats['correct_predictions'] += 1
        
        self.performance_stats['hit_rate'] = (
            self.performance_stats['correct_predictions'] / 
            self.performance_stats['total_predictions']
        )
        
        # 自适应权重调整
        self.adaptive_weight_adjustment(predicted_blue, actual_blue)
    
    def adaptive_weight_adjustment(self, predicted_blue, actual_blue):
        """自适应权重调整"""
        if predicted_blue == actual_blue:
            # 预测正确，增强当前权重配置
            for key in self.feature_weights:
                self.feature_weights[key] *= 1.01
        else:
            # 预测错误，调整权重
            # 随机调整一个权重
            adjust_key = random.choice(list(self.feature_weights.keys()))
            self.feature_weights[adjust_key] *= 0.99
        
        # 归一化权重
        total_weight = sum(self.feature_weights.values())
        for key in self.feature_weights:
            self.feature_weights[key] /= total_weight
    
    def get_performance_report(self):
        """获取性能报告"""
        return {
            'total_predictions': self.performance_stats['total_predictions'],
            'correct_predictions': self.performance_stats['correct_predictions'],
            'hit_rate': self.performance_stats['hit_rate'],
            'current_weights': self.feature_weights.copy()
        }

def test_enhanced_blue_predictor():
    """测试增强蓝球预测器"""
    print("=== 增强蓝球预测算法测试 ===")
    
    predictor = EnhancedBlueBallPredictor()
    
    if not predictor.data:
        print("没有数据，无法进行测试")
        return
    
    print(f"数据量: {len(predictor.data)}期")
    
    # 回测
    original_data = predictor.data.copy()
    test_periods = min(100, len(original_data) - 10)
    correct_predictions = 0
    
    print(f"\n开始回测 {test_periods} 期...")
    
    for i in range(test_periods):
        # 使用前面的数据进行预测
        train_end = len(original_data) - test_periods + i
        test_data = original_data[:train_end]
        predictor.data = test_data
        
        # 预测蓝球
        predicted_blue = predictor.predict_blue_ball()
        
        # 获取实际结果
        actual_blue = original_data[train_end]['blue']
        
        # 更新性能
        predictor.update_performance(predicted_blue, actual_blue)
        
        if predicted_blue == actual_blue:
            correct_predictions += 1
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✓")
        else:
            print(f"第{i+1}期: 预测{predicted_blue}, 实际{actual_blue} ✗")
    
    # 恢复完整数据
    predictor.data = predictor.load_data()
    
    hit_rate = correct_predictions / test_periods
    print(f"\n=== 回测结果 ===")
    print(f"测试期数: {test_periods}")
    print(f"命中次数: {correct_predictions}")
    print(f"命中率: {hit_rate:.2%}")
    print(f"目标命中率: 12%")
    print(f"达成度: {hit_rate/0.12*100:.1f}%")
    
    # 性能报告
    report = predictor.get_performance_report()
    print(f"\n=== 性能报告 ===")
    for key, value in report.items():
        if key == 'current_weights':
            print(f"当前权重配置:")
            for weight_key, weight_value in value.items():
                print(f"  {weight_key}: {weight_value:.3f}")
        else:
            print(f"{key}: {value}")
    
    # 生成新预测
    print(f"\n=== 最新预测 ===")
    new_prediction = predictor.predict_blue_ball()
    print(f"下期蓝球预测: {new_prediction}")
    
    return predictor

if __name__ == "__main__":
    test_enhanced_blue_predictor()