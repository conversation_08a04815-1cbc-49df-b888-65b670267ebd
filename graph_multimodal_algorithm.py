#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图神经网络+多模态数据双色球预测算法
结合图神经网络分析号码关系，集成多模态数据源
目标：红球命中率 >= 1.5/6，蓝球命中率 >= 10%
"""

import json
import random
import math
import numpy as np
from typing import List, Dict, Tuple, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scikit-learn
try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.model_selection import GridSearchCV
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("scikit-learn不可用，使用简化版本")

class GraphNeuralNetwork:
    """图神经网络（简化版）"""
    
    def __init__(self, num_nodes: int = 49, hidden_dim: int = 64):
        self.num_nodes = num_nodes  # 33红球 + 16蓝球
        self.hidden_dim = hidden_dim
        
        # 邻接矩阵（号码之间的关系）
        self.adjacency_matrix = self._create_adjacency_matrix()
        
        # 权重矩阵
        self.W_self = np.random.randn(hidden_dim, hidden_dim) * 0.1
        self.W_neighbor = np.random.randn(hidden_dim, hidden_dim) * 0.1
        self.W_output = np.random.randn(hidden_dim, 1) * 0.1
        
        # 节点嵌入
        self.node_embeddings = np.random.randn(num_nodes, hidden_dim) * 0.1
        
        print(f"图神经网络初始化完成: {num_nodes}个节点, 隐藏维度{hidden_dim}")
    
    def _create_adjacency_matrix(self) -> np.ndarray:
        """创建邻接矩阵"""
        adj = np.zeros((self.num_nodes, self.num_nodes))
        
        # 红球之间的连接（基于数值相近性）
        for i in range(33):
            for j in range(33):
                if i != j:
                    # 相邻数字有更强的连接
                    distance = abs(i - j)
                    if distance == 1:
                        adj[i, j] = 1.0
                    elif distance == 2:
                        adj[i, j] = 0.5
                    elif distance <= 5:
                        adj[i, j] = 0.2
        
        # 蓝球之间的连接
        for i in range(33, 49):
            for j in range(33, 49):
                if i != j:
                    distance = abs(i - j)
                    if distance == 1:
                        adj[i, j] = 1.0
                    elif distance == 2:
                        adj[i, j] = 0.5
        
        # 红球和蓝球之间的弱连接
        for i in range(33):
            for j in range(33, 49):
                adj[i, j] = 0.1
                adj[j, i] = 0.1
        
        return adj
    
    def forward(self, node_features: np.ndarray) -> np.ndarray:
        """前向传播"""
        # 消息传递
        messages = np.zeros_like(node_features)
        
        for i in range(self.num_nodes):
            # 自身特征
            self_message = np.dot(node_features[i], self.W_self)
            
            # 邻居特征聚合
            neighbor_message = np.zeros(self.hidden_dim)
            neighbor_count = 0
            
            for j in range(self.num_nodes):
                if self.adjacency_matrix[i, j] > 0:
                    neighbor_message += self.adjacency_matrix[i, j] * np.dot(node_features[j], self.W_neighbor)
                    neighbor_count += 1
            
            if neighbor_count > 0:
                neighbor_message /= neighbor_count
            
            # 组合消息
            messages[i] = self._relu(self_message + neighbor_message)
        
        return messages
    
    def predict_probabilities(self, node_features: np.ndarray) -> np.ndarray:
        """预测各节点的概率"""
        hidden = self.forward(node_features)
        
        # 输出层
        logits = np.dot(hidden, self.W_output).flatten()
        
        # Softmax
        probabilities = self._softmax(logits)
        
        return probabilities
    
    def _relu(self, x: np.ndarray) -> np.ndarray:
        """ReLU激活函数"""
        return np.maximum(0, x)
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax函数"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def update_node_features(self, data: List[Dict]) -> np.ndarray:
        """更新节点特征"""
        features = np.copy(self.node_embeddings)
        
        # 基于历史数据更新特征
        red_freq = np.zeros(33)
        blue_freq = np.zeros(16)
        
        for record in data[-20:]:  # 最近20期
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                for num in red_nums:
                    if 1 <= num <= 33:
                        red_freq[num-1] += 1
            
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                if 1 <= blue_num <= 16:
                    blue_freq[blue_num-1] += 1
        
        # 归一化频率
        red_freq = red_freq / (np.sum(red_freq) + 1e-6)
        blue_freq = blue_freq / (np.sum(blue_freq) + 1e-6)
        
        # 更新节点特征
        for i in range(33):
            features[i, 0] = red_freq[i]  # 频率特征
        
        for i in range(16):
            features[33 + i, 0] = blue_freq[i]  # 频率特征
        
        return features

class MultiModalFeatureExtractor:
    """多模态特征提取器"""
    
    def __init__(self):
        self.feature_names = [
            'temporal', 'statistical', 'pattern', 'trend', 
            'correlation', 'entropy', 'momentum', 'volatility'
        ]
        print(f"多模态特征提取器初始化完成，支持{len(self.feature_names)}种特征")
    
    def extract_features(self, data: List[Dict]) -> Dict[str, np.ndarray]:
        """提取多模态特征"""
        features = {}
        
        # 时间特征
        features['temporal'] = self._extract_temporal_features(data)
        
        # 统计特征
        features['statistical'] = self._extract_statistical_features(data)
        
        # 模式特征
        features['pattern'] = self._extract_pattern_features(data)
        
        # 趋势特征
        features['trend'] = self._extract_trend_features(data)
        
        # 相关性特征
        features['correlation'] = self._extract_correlation_features(data)
        
        # 熵特征
        features['entropy'] = self._extract_entropy_features(data)
        
        # 动量特征
        features['momentum'] = self._extract_momentum_features(data)
        
        # 波动性特征
        features['volatility'] = self._extract_volatility_features(data)
        
        return features
    
    def _extract_temporal_features(self, data: List[Dict]) -> np.ndarray:
        """提取时间特征"""
        features = np.zeros(10)
        
        if not data:
            return features
        
        # 最近一期的时间特征
        last_record = data[-1]
        if 'opendate' in last_record:
            try:
                date = datetime.strptime(last_record['opendate'], '%Y-%m-%d')
                features[0] = date.weekday()  # 星期几
                features[1] = date.day  # 日期
                features[2] = date.month  # 月份
                features[3] = date.year % 100  # 年份后两位
                features[4] = date.timetuple().tm_yday  # 一年中的第几天
            except:
                pass
        
        # 期号特征
        if 'issueno' in last_record:
            try:
                issue_no = int(last_record['issueno'][-3:])  # 期号后三位
                features[5] = issue_no % 7  # 期号模7
                features[6] = issue_no % 10  # 期号模10
            except:
                pass
        
        return features
    
    def _extract_statistical_features(self, data: List[Dict]) -> np.ndarray:
        """提取统计特征"""
        features = np.zeros(20)
        
        if len(data) < 5:
            return features
        
        recent_data = data[-10:]
        red_nums_all = []
        blue_nums_all = []
        
        for record in recent_data:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                red_nums_all.extend(red_nums)
            
            if 'refernumber' in record:
                blue_nums_all.append(int(record['refernumber']))
        
        if red_nums_all:
            features[0] = np.mean(red_nums_all)  # 红球均值
            features[1] = np.std(red_nums_all)   # 红球标准差
            features[2] = np.min(red_nums_all)   # 红球最小值
            features[3] = np.max(red_nums_all)   # 红球最大值
            features[4] = np.median(red_nums_all)  # 红球中位数
        
        if blue_nums_all:
            features[5] = np.mean(blue_nums_all)  # 蓝球均值
            features[6] = np.std(blue_nums_all)   # 蓝球标准差
            features[7] = np.min(blue_nums_all)   # 蓝球最小值
            features[8] = np.max(blue_nums_all)   # 蓝球最大值
        
        # 奇偶比例
        if red_nums_all:
            odd_count = sum(1 for x in red_nums_all if x % 2 == 1)
            features[9] = odd_count / len(red_nums_all)
        
        # 大小比例（>16.5为大）
        if red_nums_all:
            large_count = sum(1 for x in red_nums_all if x > 16.5)
            features[10] = large_count / len(red_nums_all)
        
        return features
    
    def _extract_pattern_features(self, data: List[Dict]) -> np.ndarray:
        """提取模式特征"""
        features = np.zeros(15)
        
        if len(data) < 10:
            return features
        
        # 连号分析
        consecutive_counts = []
        for record in data[-10:]:
            if 'number' in record:
                red_nums = sorted([int(x.strip()) for x in record['number'].split() if x.strip()])
                consecutive = 0
                for i in range(len(red_nums) - 1):
                    if red_nums[i+1] - red_nums[i] == 1:
                        consecutive += 1
                consecutive_counts.append(consecutive)
        
        if consecutive_counts:
            features[0] = np.mean(consecutive_counts)
            features[1] = np.std(consecutive_counts)
        
        # 重号分析
        repeat_counts = []
        for i in range(1, min(len(data), 10)):
            if 'number' in data[-i] and 'number' in data[-i-1]:
                curr_red = set([int(x.strip()) for x in data[-i]['number'].split() if x.strip()])
                prev_red = set([int(x.strip()) for x in data[-i-1]['number'].split() if x.strip()])
                repeat_counts.append(len(curr_red & prev_red))
        
        if repeat_counts:
            features[2] = np.mean(repeat_counts)
            features[3] = np.std(repeat_counts)
        
        return features
    
    def _extract_trend_features(self, data: List[Dict]) -> np.ndarray:
        """提取趋势特征"""
        features = np.zeros(12)
        
        if len(data) < 5:
            return features
        
        # 红球和值趋势
        red_sums = []
        for record in data[-10:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                red_sums.append(sum(red_nums))
        
        if len(red_sums) >= 3:
            # 线性趋势
            x = np.arange(len(red_sums))
            slope = np.polyfit(x, red_sums, 1)[0]
            features[0] = slope
            
            # 变化率
            changes = [red_sums[i] - red_sums[i-1] for i in range(1, len(red_sums))]
            if changes:
                features[1] = np.mean(changes)
                features[2] = np.std(changes)
        
        # 蓝球趋势
        blue_nums = []
        for record in data[-10:]:
            if 'refernumber' in record:
                blue_nums.append(int(record['refernumber']))
        
        if len(blue_nums) >= 3:
            x = np.arange(len(blue_nums))
            slope = np.polyfit(x, blue_nums, 1)[0]
            features[3] = slope
        
        return features
    
    def _extract_correlation_features(self, data: List[Dict]) -> np.ndarray:
        """提取相关性特征"""
        features = np.zeros(8)
        
        if len(data) < 10:
            return features
        
        # 红球位置相关性
        positions = [[] for _ in range(6)]
        
        for record in data[-20:]:
            if 'number' in record:
                red_nums = sorted([int(x.strip()) for x in record['number'].split() if x.strip()])
                for i, num in enumerate(red_nums[:6]):
                    positions[i].append(num)
        
        # 计算相邻位置的相关性
        for i in range(5):
            if len(positions[i]) > 5 and len(positions[i+1]) > 5:
                corr = np.corrcoef(positions[i], positions[i+1])[0, 1]
                if not np.isnan(corr):
                    features[i] = corr
        
        return features
    
    def _extract_entropy_features(self, data: List[Dict]) -> np.ndarray:
        """提取熵特征"""
        features = np.zeros(6)
        
        if len(data) < 10:
            return features
        
        # 红球分布熵
        red_freq = np.zeros(33)
        for record in data[-20:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                for num in red_nums:
                    if 1 <= num <= 33:
                        red_freq[num-1] += 1
        
        red_prob = red_freq / (np.sum(red_freq) + 1e-6)
        red_entropy = -np.sum(red_prob * np.log(red_prob + 1e-6))
        features[0] = red_entropy
        
        # 蓝球分布熵
        blue_freq = np.zeros(16)
        for record in data[-20:]:
            if 'refernumber' in record:
                blue_num = int(record['refernumber'])
                if 1 <= blue_num <= 16:
                    blue_freq[blue_num-1] += 1
        
        blue_prob = blue_freq / (np.sum(blue_freq) + 1e-6)
        blue_entropy = -np.sum(blue_prob * np.log(blue_prob + 1e-6))
        features[1] = blue_entropy
        
        return features
    
    def _extract_momentum_features(self, data: List[Dict]) -> np.ndarray:
        """提取动量特征"""
        features = np.zeros(10)
        
        if len(data) < 5:
            return features
        
        # 红球动量
        red_sums = []
        for record in data[-10:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                red_sums.append(sum(red_nums))
        
        if len(red_sums) >= 3:
            # 短期动量（3期）
            short_momentum = red_sums[-1] - red_sums[-3]
            features[0] = short_momentum
            
            # 长期动量（5期）
            if len(red_sums) >= 5:
                long_momentum = red_sums[-1] - red_sums[-5]
                features[1] = long_momentum
        
        return features
    
    def _extract_volatility_features(self, data: List[Dict]) -> np.ndarray:
        """提取波动性特征"""
        features = np.zeros(8)
        
        if len(data) < 5:
            return features
        
        # 红球和值波动性
        red_sums = []
        for record in data[-10:]:
            if 'number' in record:
                red_nums = [int(x.strip()) for x in record['number'].split() if x.strip()]
                red_sums.append(sum(red_nums))
        
        if len(red_sums) >= 3:
            # 标准差
            features[0] = np.std(red_sums)
            
            # 变异系数
            mean_val = np.mean(red_sums)
            if mean_val > 0:
                features[1] = np.std(red_sums) / mean_val
            
            # 极差
            features[2] = np.max(red_sums) - np.min(red_sums)
        
        return features

class GraphMultiModalPredictor:
    """图神经网络+多模态预测器"""
    
    def __init__(self):
        # 图神经网络
        self.gnn = GraphNeuralNetwork()
        
        # 多模态特征提取器
        self.feature_extractor = MultiModalFeatureExtractor()
        
        # 集成学习器
        if SKLEARN_AVAILABLE:
            self.red_ensemble = [
                RandomForestRegressor(n_estimators=100, random_state=42),
                GradientBoostingRegressor(n_estimators=100, random_state=42)
            ]
            self.blue_ensemble = [
                RandomForestRegressor(n_estimators=100, random_state=42),
                GradientBoostingRegressor(n_estimators=100, random_state=42)
            ]
        else:
            self.red_ensemble = None
            self.blue_ensemble = None
        
        # 聚类器（用于模式识别）
        if SKLEARN_AVAILABLE:
            self.red_clusterer = KMeans(n_clusters=5, random_state=42)
            self.blue_clusterer = KMeans(n_clusters=3, random_state=42)
        else:
            self.red_clusterer = None
            self.blue_clusterer = None
        
        # 历史记录
        self.history = []
        self.is_trained = False
        
        print(f"图神经网络+多模态预测器初始化完成")
    
    def train(self, data: List[Dict], train_size: int = 200):
        """训练模型"""
        if len(data) < train_size + 20:
            print("数据量不足，跳过训练")
            return
        
        print("开始训练图神经网络+多模态模型...")
        
        train_data = data[:train_size]
        
        # 准备训练数据
        X_red = []
        y_red = []
        X_blue = []
        y_blue = []
        
        for i in range(20, len(train_data)):
            # 特征
            current_data = train_data[:i]
            features = self.feature_extractor.extract_features(current_data)
            
            # 合并所有特征
            combined_features = np.concatenate([
                features['temporal'],
                features['statistical'],
                features['pattern'],
                features['trend'],
                features['correlation'],
                features['entropy'],
                features['momentum'],
                features['volatility']
            ])
            
            X_red.append(combined_features)
            X_blue.append(combined_features)
            
            # 目标
            if 'number' in train_data[i]:
                red_nums = [int(x.strip()) for x in train_data[i]['number'].split() if x.strip()]
                y_red.append(red_nums[:6])
            else:
                y_red.append([1, 2, 3, 4, 5, 6])
            
            if 'refernumber' in train_data[i]:
                blue_num = int(train_data[i]['refernumber'])
                y_blue.append(blue_num)
            else:
                y_blue.append(1)
        
        X_red = np.array(X_red)
        X_blue = np.array(X_blue)
        y_red = np.array(y_red)
        y_blue = np.array(y_blue)
        
        # 训练集成学习器
        if SKLEARN_AVAILABLE and self.red_ensemble:
            try:
                # 红球预测（预测和值）
                y_red_sum = np.sum(y_red, axis=1)
                for model in self.red_ensemble:
                    model.fit(X_red, y_red_sum)
                
                # 蓝球预测
                for model in self.blue_ensemble:
                    model.fit(X_blue, y_blue)
                
                # 训练聚类器
                if self.red_clusterer:
                    self.red_clusterer.fit(y_red)
                if self.blue_clusterer:
                    self.blue_clusterer.fit(y_blue.reshape(-1, 1))
                
                self.is_trained = True
                print("模型训练完成")
                
            except Exception as e:
                print(f"模型训练失败: {e}")
        else:
            print("使用简化版本")
            self.is_trained = True
    
    def predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """预测"""
        if len(data) < 10:
            return self._fallback_predict()
        
        try:
            # 图神经网络预测
            gnn_red, gnn_blue = self._gnn_predict(data)
            
            # 多模态特征预测
            mm_red, mm_blue = self._multimodal_predict(data)
            
            # 集成预测
            final_red = self._ensemble_red_prediction([gnn_red, mm_red])
            final_blue = self._ensemble_blue_prediction([gnn_blue, mm_blue])
            
            return final_red, final_blue
            
        except Exception as e:
            print(f"预测失败: {e}，使用回退策略")
            return self._fallback_predict()
    
    def _gnn_predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """图神经网络预测"""
        # 更新节点特征
        node_features = self.gnn.update_node_features(data)
        
        # 预测概率
        probabilities = self.gnn.predict_probabilities(node_features)
        
        # 红球预测（选择概率最高的6个）
        red_probs = probabilities[:33]
        red_indices = np.argsort(red_probs)[-6:]
        red_pred = sorted([i + 1 for i in red_indices])
        
        # 蓝球预测（选择概率最高的1个）
        blue_probs = probabilities[33:49]
        blue_idx = np.argmax(blue_probs)
        blue_pred = [blue_idx + 1]
        
        return red_pred, blue_pred
    
    def _multimodal_predict(self, data: List[Dict]) -> Tuple[List[int], List[int]]:
        """多模态特征预测"""
        if not self.is_trained or not SKLEARN_AVAILABLE:
            return self._fallback_predict()
        
        # 提取特征
        features = self.feature_extractor.extract_features(data)
        combined_features = np.concatenate([
            features['temporal'],
            features['statistical'],
            features['pattern'],
            features['trend'],
            features['correlation'],
            features['entropy'],
            features['momentum'],
            features['volatility']
        ]).reshape(1, -1)
        
        # 红球预测
        red_predictions = []
        for model in self.red_ensemble:
            try:
                pred_sum = model.predict(combined_features)[0]
                red_predictions.append(pred_sum)
            except:
                red_predictions.append(100)  # 默认和值
        
        avg_sum = np.mean(red_predictions)
        red_pred = self._generate_red_from_sum(avg_sum)
        
        # 蓝球预测
        blue_predictions = []
        for model in self.blue_ensemble:
            try:
                pred_blue = model.predict(combined_features)[0]
                blue_predictions.append(pred_blue)
            except:
                blue_predictions.append(8)  # 默认值
        
        avg_blue = np.mean(blue_predictions)
        blue_pred = [max(1, min(16, int(round(avg_blue))))]
        
        return red_pred, blue_pred
    
    def _generate_red_from_sum(self, target_sum: float) -> List[int]:
        """根据目标和值生成红球组合"""
        target_sum = max(21, min(183, target_sum))  # 限制范围
        
        # 随机生成组合，调整到接近目标和值
        best_combination = None
        best_diff = float('inf')
        
        for _ in range(100):  # 尝试100次
            combination = sorted(random.sample(range(1, 34), 6))
            current_sum = sum(combination)
            diff = abs(current_sum - target_sum)
            
            if diff < best_diff:
                best_diff = diff
                best_combination = combination
        
        return best_combination or sorted(random.sample(range(1, 34), 6))
    
    def _ensemble_red_prediction(self, predictions: List[List[int]]) -> List[int]:
        """红球集成预测"""
        # 投票机制
        vote_count = defaultdict(int)
        
        for pred in predictions:
            for num in pred:
                vote_count[num] += 1
        
        # 选择得票最多的6个
        sorted_votes = sorted(vote_count.items(), key=lambda x: x[1], reverse=True)
        result = [num for num, _ in sorted_votes[:6]]
        
        # 补充不足的
        while len(result) < 6:
            candidate = random.randint(1, 33)
            if candidate not in result:
                result.append(candidate)
        
        return sorted(result)
    
    def _ensemble_blue_prediction(self, predictions: List[List[int]]) -> List[int]:
        """蓝球集成预测"""
        # 简单平均
        blue_nums = [pred[0] for pred in predictions]
        avg_blue = np.mean(blue_nums)
        return [max(1, min(16, int(round(avg_blue))))]
    
    def _fallback_predict(self) -> Tuple[List[int], List[int]]:
        """回退预测"""
        red_pred = sorted(random.sample(range(1, 34), 6))
        blue_pred = [random.randint(1, 16)]
        return red_pred, blue_pred
    
    def load_data(self, file_path: str = 'ssq_data.json') -> List[Dict]:
        """加载数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条历史数据")
            return data
        except FileNotFoundError:
            print(f"数据文件 {file_path} 不存在")
            return []
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []

def graph_multimodal_backtest(data: List[Dict], test_periods: int = 50) -> Dict:
    """图神经网络+多模态算法回测"""
    if len(data) < test_periods + 100:
        print(f"数据量不足，需要至少 {test_periods + 100} 条数据")
        return {}
    
    predictor = GraphMultiModalPredictor()
    
    # 训练模型
    train_data = data[:-test_periods]
    predictor.train(train_data)
    
    # 回测结果
    results = {
        'total_tests': test_periods,
        'red_hits': [],
        'blue_hits': [],
        'predictions': [],
        'best_records': []
    }
    
    print(f"\n开始图神经网络+多模态算法回测，测试期数: {test_periods}")
    
    for i in range(test_periods):
        # 训练数据（不包括当前测试期）
        train_data = data[:-(test_periods-i)]
        
        # 预测
        pred_red, pred_blue = predictor.predict(train_data)
        
        # 实际结果
        test_record = data[-(test_periods-i)]
        
        if 'number' in test_record:
            actual_red = [int(x.strip()) for x in test_record['number'].split() if x.strip()]
        else:
            actual_red = [1, 2, 3, 4, 5, 6]
        
        if 'refernumber' in test_record:
            actual_blue = [int(test_record['refernumber'])]
        else:
            actual_blue = [1]
        
        # 计算命中
        red_hit = len(set(pred_red) & set(actual_red))
        blue_hit = 1 if pred_blue[0] == actual_blue[0] else 0
        
        results['red_hits'].append(red_hit)
        results['blue_hits'].append(blue_hit)
        results['predictions'].append({
            'date': test_record.get('opendate', f'期号{i+1}'),
            'pred_red': pred_red,
            'pred_blue': pred_blue,
            'actual_red': actual_red,
            'actual_blue': actual_blue,
            'red_hit': red_hit,
            'blue_hit': blue_hit
        })
        
        # 记录最佳预测
        if red_hit >= 2 or blue_hit >= 1:
            results['best_records'].append({
                'date': test_record.get('opendate', f'期号{i+1}'),
                'red_hit': red_hit,
                'blue_hit': blue_hit,
                'total_score': red_hit * 10 + blue_hit * 50
            })
    
    # 计算统计结果
    avg_red_hit = np.mean(results['red_hits'])
    blue_hit_rate = np.mean(results['blue_hits']) * 100
    
    # 目标达成度
    red_target_achievement = (avg_red_hit / 1.5) * 100
    blue_target_achievement = (blue_hit_rate / 10) * 100
    overall_achievement = (red_target_achievement + blue_target_achievement) / 2
    
    results.update({
        'avg_red_hit': avg_red_hit,
        'blue_hit_rate': blue_hit_rate,
        'red_target_achievement': red_target_achievement,
        'blue_target_achievement': blue_target_achievement,
        'overall_achievement': overall_achievement
    })
    
    return results

def print_graph_multimodal_results(results: Dict):
    """打印图神经网络+多模态结果"""
    if not results:
        print("没有回测结果")
        return
    
    print(f"\n=== 图神经网络+多模态算法回测结果 ===")
    print(f"测试期数: {results['total_tests']}")
    print(f"红球平均命中: {results['avg_red_hit']:.2f}/6 ({results['avg_red_hit']/6*100:.1f}%)")
    print(f"蓝球命中率: {results['blue_hit_rate']:.1f}%")
    print(f"目标达成度: 红球 {results['red_target_achievement']:.1f}%, 蓝球 {results['blue_target_achievement']:.1f}%")
    print(f"综合达成度: {results['overall_achievement']:.1f}%")
    
    # 最佳预测记录
    if results['best_records']:
        print(f"\n前15期最佳预测记录:")
        sorted_records = sorted(results['best_records'], 
                              key=lambda x: x['total_score'], reverse=True)[:15]
        for record in sorted_records:
            print(f"{record['date']}: 红球 {record['red_hit']}/6, 蓝球 {record['blue_hit']}/1")
    
    # 红球命中分布
    red_distribution = defaultdict(int)
    for hit in results['red_hits']:
        red_distribution[hit] += 1
    
    print(f"\n红球命中分布:")
    for i in range(7):
        count = red_distribution[i]
        percentage = count / results['total_tests'] * 100
        print(f"{i}个: {count}次 ({percentage:.1f}%)")
    
    print(f"\n💡 图神经网络+多模态优化建议:")
    print(f"1. 增加更多图卷积层和注意力机制")
    print(f"2. 引入更多外部数据源（天气、经济、社会事件等）")
    print(f"3. 实现动态图结构学习")
    print(f"4. 使用更先进的图神经网络架构（GraphSAGE、GAT等）")
    print(f"5. 实现端到端的深度学习优化")

if __name__ == "__main__":
    # 创建预测器
    predictor = GraphMultiModalPredictor()
    
    # 加载数据
    data = predictor.load_data()
    
    if data:
        # 回测
        backtest_results = graph_multimodal_backtest(data, test_periods=50)
        
        # 打印结果
        print_graph_multimodal_results(backtest_results)
    else:
        print("无法加载数据，退出程序")