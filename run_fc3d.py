#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D预测系统启动器
提供简单的菜单界面，方便用户选择不同版本的预测程序
"""

import os
import sys
import subprocess

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════╗
    ║                  福彩3D智能预测系统                        ║
    ║                                                          ║
    ║              基于机器学习的号码预测程序                     ║
    ╚══════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """打印菜单"""
    menu = """
    ┌─────────────────────────────────────────────────────────┐
    │                      程序选择菜单                        │
    ├─────────────────────────────────────────────────────────┤
    │  1. 运行基础版预测器 (fc3d_predictor.py)                 │
    │     - 简单易懂的算法实现                                  │
    │     - 适合学习和理解                                      │
    │     - 运行速度快                                          │
    │                                                         │
    │  2. 运行高级版预测器 (fc3d_advanced_predictor.py)        │
    │     - 集成多种高级算法                                    │
    │     - 包含历史验证功能                                    │
    │     - 详细的分析报告                                      │
    │                                                         │
    │  3. 查看使用说明 (FC3D_README.md)                        │
    │     - 详细的使用指南                                      │
    │     - 算法原理说明                                        │
    │     - 常见问题解答                                        │
    │                                                         │
    │  4. 退出程序                                             │
    └─────────────────────────────────────────────────────────┘
    """
    print(menu)

def run_basic_predictor():
    """运行基础版预测器"""
    print("\n🚀 启动基础版福彩3D预测器...\n")
    try:
        subprocess.run([sys.executable, 'fc3d_predictor.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 程序运行出错: {e}")
    except FileNotFoundError:
        print("❌ 找不到 fc3d_predictor.py 文件")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def run_advanced_predictor():
    """运行高级版预测器"""
    print("\n🚀 启动高级版福彩3D预测器...\n")
    try:
        subprocess.run([sys.executable, 'fc3d_advanced_predictor.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 程序运行出错: {e}")
    except FileNotFoundError:
        print("❌ 找不到 fc3d_advanced_predictor.py 文件")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def show_readme():
    """显示使用说明"""
    print("\n📖 正在打开使用说明...\n")
    try:
        if os.name == 'nt':  # Windows
            os.startfile('FC3D_README.md')
        elif os.name == 'posix':  # macOS/Linux
            subprocess.run(['open', 'FC3D_README.md'], check=True)
        else:
            # 如果无法打开文件，直接显示内容
            with open('FC3D_README.md', 'r', encoding='utf-8') as f:
                content = f.read()
                print(content[:2000])  # 显示前2000个字符
                print("\n... (内容过长，请直接打开 FC3D_README.md 文件查看完整内容)")
    except FileNotFoundError:
        print("❌ 找不到 FC3D_README.md 文件")
    except Exception as e:
        print(f"❌ 打开文件失败: {e}")
        print("💡 请手动打开 FC3D_README.md 文件查看使用说明")

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        'fc3d_predictor.py',
        'fc3d_advanced_predictor.py',
        'FC3D_README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️  警告：以下文件缺失：")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 请确保所有程序文件都在当前目录中")
        return False
    
    return True

def wait_for_input():
    """等待用户输入"""
    input("\n按回车键继续...")

def main():
    """主函数"""
    while True:
        clear_screen()
        print_banner()
        
        # 检查文件
        if not check_files():
            wait_for_input()
            continue
        
        print_menu()
        
        try:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                clear_screen()
                run_basic_predictor()
                wait_for_input()
                
            elif choice == '2':
                clear_screen()
                run_advanced_predictor()
                wait_for_input()
                
            elif choice == '3':
                clear_screen()
                show_readme()
                wait_for_input()
                
            elif choice == '4':
                print("\n👋 感谢使用福彩3D预测系统！")
                print("💡 理性购彩，量力而行")
                break
                
            else:
                print("\n❌ 无效选择，请输入 1-4 之间的数字")
                wait_for_input()
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")
            wait_for_input()

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ 程序启动失败: {e}")
        print("💡 请检查Python环境和文件权限")
        input("\n按回车键退出...")