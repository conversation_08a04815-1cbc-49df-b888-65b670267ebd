import json
import random
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta

def load_data():
    """加载历史数据"""
    with open('ssq_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def parse_numbers(entry):
    """解析开奖号码"""
    red_str = entry['number'].split(' ')
    red = sorted([int(x) for x in red_str])
    blue = int(entry['refernumber'])
    return red, blue

def analyze_patterns(data):
    """深度模式分析"""
    patterns = {
        'red_freq': defaultdict(int),
        'blue_freq': defaultdict(int),
        'red_pairs': defaultdict(int),
        'blue_after_red_sum': defaultdict(list),
        'consecutive_patterns': defaultdict(int),
        'odd_even_patterns': defaultdict(int),
        'sum_range_patterns': defaultdict(int),
        'recent_trends': {'red': defaultdict(int), 'blue': defaultdict(int)}
    }
    
    recent_data = data[:100]  # 最近100期
    
    for i, entry in enumerate(data):
        red, blue = parse_numbers(entry)
        
        # 基础频率
        for r in red:
            patterns['red_freq'][r] += 1
            if i < 100:  # 最近趋势
                patterns['recent_trends']['red'][r] += 1
        
        patterns['blue_freq'][blue] += 1
        if i < 100:
            patterns['recent_trends']['blue'][blue] += 1
        
        # 红球配对分析
        for j in range(len(red)):
            for k in range(j+1, len(red)):
                pair = tuple(sorted([red[j], red[k]]))
                patterns['red_pairs'][pair] += 1
        
        # 蓝球与红球和值关系
        red_sum = sum(red)
        patterns['blue_after_red_sum'][red_sum].append(blue)
        
        # 连号分析
        consecutive_count = 0
        for j in range(len(red)-1):
            if red[j+1] - red[j] == 1:
                consecutive_count += 1
        patterns['consecutive_patterns'][consecutive_count] += 1
        
        # 奇偶分析
        odd_count = sum(1 for r in red if r % 2 == 1)
        patterns['odd_even_patterns'][odd_count] += 1
        
        # 和值范围分析
        sum_range = red_sum // 20  # 分成若干区间
        patterns['sum_range_patterns'][sum_range] += 1
    
    return patterns

def calculate_weights(patterns, recent_weight=0.3):
    """计算加权概率"""
    weights = {'red': {}, 'blue': {}}
    
    # 红球权重：历史频率 + 最近趋势
    total_red_freq = sum(patterns['red_freq'].values())
    total_recent_red = sum(patterns['recent_trends']['red'].values())
    
    for num in range(1, 34):
        hist_prob = patterns['red_freq'][num] / total_red_freq if total_red_freq > 0 else 0
        recent_prob = patterns['recent_trends']['red'][num] / total_recent_red if total_recent_red > 0 else 0
        weights['red'][num] = (1 - recent_weight) * hist_prob + recent_weight * recent_prob
    
    # 蓝球权重
    total_blue_freq = sum(patterns['blue_freq'].values())
    total_recent_blue = sum(patterns['recent_trends']['blue'].values())
    
    for num in range(1, 17):
        hist_prob = patterns['blue_freq'][num] / total_blue_freq if total_blue_freq > 0 else 0
        recent_prob = patterns['recent_trends']['blue'][num] / total_recent_blue if total_recent_blue > 0 else 0
        weights['blue'][num] = (1 - recent_weight) * hist_prob + recent_weight * recent_prob
    
    return weights

def advanced_markov_predict(data, patterns, weights):
    """高级马尔科夫预测算法"""
    historical = []
    for entry in data:
        red, blue = parse_numbers(entry)
        historical.append((red, blue))
    historical = historical[::-1]  # 从旧到新
    
    if len(historical) < 10:
        return None, None
    
    # === 蓝球预测改进 ===
    # 1. 基于红球和值预测蓝球
    last_red_sum = sum(historical[-1][0])
    blue_candidates_by_sum = patterns['blue_after_red_sum'].get(last_red_sum, [])
    
    # 2. 马尔科夫链
    blue_transition = defaultdict(lambda: defaultdict(int))
    for i in range(1, len(historical)):
        prev_blue = historical[i-1][1]
        curr_blue = historical[i][1]
        blue_transition[prev_blue][curr_blue] += 1
    
    last_blue = historical[-1][1]
    blue_probs = {}
    
    for blue in range(1, 17):
        # 马尔科夫概率
        markov_prob = 0
        if last_blue in blue_transition:
            total_transitions = sum(blue_transition[last_blue].values())
            if total_transitions > 0:
                markov_prob = blue_transition[last_blue][blue] / total_transitions
        
        # 基于红球和值的概率
        sum_prob = 0
        if blue_candidates_by_sum:
            sum_prob = blue_candidates_by_sum.count(blue) / len(blue_candidates_by_sum)
        
        # 频率权重
        freq_prob = weights['blue'][blue]
        
        # 综合概率
        blue_probs[blue] = 0.4 * markov_prob + 0.3 * sum_prob + 0.3 * freq_prob
    
    # 选择蓝球
    if sum(blue_probs.values()) > 0:
        blue_candidates = list(blue_probs.keys())
        blue_weights = list(blue_probs.values())
        next_blue = random.choices(blue_candidates, weights=blue_weights, k=1)[0]
    else:
        next_blue = random.randint(1, 16)
    
    # === 红球预测改进 ===
    # 1. 位置化马尔科夫链
    red_transition = [defaultdict(lambda: defaultdict(int)) for _ in range(7)]
    
    for pos in range(6):  # 前6个位置
        for i in range(1, len(historical)):
            if pos < len(historical[i-1][0]) and pos < len(historical[i][0]):
                prev_red = historical[i-1][0][pos]
                curr_red = historical[i][0][pos]
                red_transition[pos][prev_red][curr_red] += 1
    
    # 第7个位置使用平均值
    for i in range(1, 34):
        for j in range(1, 34):
            avg_count = sum(red_transition[pos][i][j] for pos in range(6)) / 6
            red_transition[6][i][j] = avg_count
    
    # 2. 考虑配对关系
    def get_pair_bonus(num, selected_nums):
        bonus = 0
        for selected in selected_nums:
            pair = tuple(sorted([num, selected]))
            if pair in patterns['red_pairs']:
                bonus += patterns['red_pairs'][pair] / sum(patterns['red_pairs'].values())
        return bonus
    
    # 3. 生成红球
    last_red = historical[-1][0] + [historical[-1][0][-1]]  # 扩展到7位
    next_red = []
    used = set()
    
    for pos in range(7):
        red_probs = {}
        prev_red = last_red[pos]
        
        for red in range(1, 34):
            if red in used:
                continue
            
            # 马尔科夫概率
            markov_prob = 0
            if prev_red in red_transition[pos]:
                total_transitions = sum(red_transition[pos][prev_red].values())
                if total_transitions > 0:
                    markov_prob = red_transition[pos][prev_red][red] / total_transitions
            
            # 频率权重
            freq_prob = weights['red'][red]
            
            # 配对奖励
            pair_bonus = get_pair_bonus(red, next_red)
            
            # 综合概率
            red_probs[red] = 0.5 * markov_prob + 0.3 * freq_prob + 0.2 * pair_bonus
        
        # 选择红球
        if red_probs and sum(red_probs.values()) > 0:
            red_candidates = list(red_probs.keys())
            red_weights = list(red_probs.values())
            selected = random.choices(red_candidates, weights=red_weights, k=1)[0]
        else:
            available = [r for r in range(1, 34) if r not in used]
            selected = random.choice(available) if available else 1
        
        next_red.append(selected)
        used.add(selected)
    
    next_red.sort()
    return next_red, next_blue

def ensemble_predict(data, num_predictions=10):
    """集成预测：生成多个预测并选择最优"""
    patterns = analyze_patterns(data)
    weights = calculate_weights(patterns)
    
    predictions = []
    for _ in range(num_predictions):
        red, blue = advanced_markov_predict(data, patterns, weights)
        if red and blue:
            predictions.append((red, blue))
    
    if not predictions:
        return None, None
    
    # 红球：选择出现频率最高的组合
    red_counter = Counter()
    for red, _ in predictions:
        red_counter[tuple(red)] += 1
    
    best_red = list(red_counter.most_common(1)[0][0])
    
    # 蓝球：选择出现频率最高的
    blue_counter = Counter()
    for _, blue in predictions:
        blue_counter[blue] += 1
    
    best_blue = blue_counter.most_common(1)[0][0]
    
    return best_red, best_blue

def smart_predict(data):
    """智能预测主函数"""
    # 设置随机种子确保可重现性
    random.seed(42)
    np.random.seed(42)
    
    # 使用集成方法
    red, blue = ensemble_predict(data)
    
    if red is None or blue is None:
        # 回退到简单方法
        patterns = analyze_patterns(data)
        weights = calculate_weights(patterns)
        red, blue = advanced_markov_predict(data, patterns, weights)
    
    return red, blue

if __name__ == '__main__':
    data = load_data()
    print(f"加载了{len(data)}期历史数据")
    
    # 进行预测
    predicted_red, predicted_blue = smart_predict(data)
    
    if predicted_red and predicted_blue:
        print(f"\n智能预测结果:")
        print(f"红球: {predicted_red}")
        print(f"蓝球: {predicted_blue}")
        
        # 分析预测的特征
        patterns = analyze_patterns(data)
        print(f"\n预测分析:")
        print(f"红球和值: {sum(predicted_red)}")
        print(f"奇数个数: {sum(1 for r in predicted_red if r % 2 == 1)}")
        print(f"连号个数: {sum(1 for i in range(len(predicted_red)-1) if predicted_red[i+1] - predicted_red[i] == 1)}")
    else:
        print("预测失败")